# =============================================================================
 # Recode Self-Control age 3 variables
# =============================================================================

# Load packages
library(pacman)
p_load(psych, corrplot, lavaan, semTools, VIM)

# =============================================================================
# 1. SELF-CONTROL VARIABLES DEFINITION
# =============================================================================

# Variables that NEED reverse coding
self_control_reverse <- c(
  "bmsdsta0",  # SDST: Think things out before acting
  "bmsdgfa0",  # Having at least one good friend
  "bmsdlca0",   # Generally liked by other children
  "bmsdpfa0",  # Being considerate of other people's feelings
  "bmsdsra0",  # Sharing readily with other children
  "bmsdhua0",  # Being helpful if someone is hurt
  "bmsdkya0",  # Being kind to younger children
  "bmsdvha0",  # Often volunteering to help others
  "bmsdtea0",  # Sees tasks through to the end, good attention span
  "bmsdora0"   # Generally obedient
)

# Variables that do NOT need reverse coding
self_control_normal <- c(
  "bmsdspa0",  # Being rather solitary and tending to play alone
  "bmsddca0",  # Is easily distracted, concentration wanders
  "bmsdgba0",  # Getting on better with adults than other children
  "bmsdtta0",  # Often has temper tantrums or hot tempers
  "bmsdmwa0",  # Having many worries
  "bmsduda0",  # Being often unhappy, down-hearted, or tearful
  "bmsdnca0",  # Being nervous or clingy in new situations
  "bmsdfea0",  # Having many fears, being easily scared
  "bmsdpba0",  # Child is restless, overactive, cannot stay still for long
  "bmsdfsa0",  # Child is constantly fidgeting or squirming
  "bmsdoaa0"   # Lying or cheating
)

# Combined list of all self-control variables
all_self_control_vars <- c(self_control_reverse, self_control_normal)

cat("=== SELF-CONTROL VARIABLES PROCESSING (AGE 3) ===\n")
cat("Variables requiring reverse coding:", length(self_control_reverse), "\n")
cat("Variables with normal coding:", length(self_control_normal), "\n")
cat("Total variables:", length(all_self_control_vars), "\n\n")

# =============================================================================
# 2. CHECK EXISTING VARIABLES
# =============================================================================

# Check which variables exist in the data
existing_vars <- all_self_control_vars[all_self_control_vars %in% names(merged_data)]
missing_vars <- all_self_control_vars[!all_self_control_vars %in% names(merged_data)]

cat("=== VARIABLE AVAILABILITY ===\n")
cat("Found variables (", length(existing_vars), "):", paste(existing_vars, collapse = ", "), "\n")
if(length(missing_vars) > 0) {
  cat("Missing variables (", length(missing_vars), "):", paste(missing_vars, collapse = ", "), "\n")
}
cat("\n")

# =============================================================================
# 3. DISPLAY FREQUENCY TABLES FOR EXISTING VARIABLES
# =============================================================================

cat("=== ORIGINAL VARIABLE DISTRIBUTIONS ===\n")
for(var in existing_vars) {
  cat("\n--- ", var, " ---\n")
  var_numeric <- as.vector(merged_data[[var]])
  print(table(var_numeric, useNA = "ifany"))
}

# =============================================================================
# 4. RECODE VARIABLES THAT NEED REVERSE CODING
# =============================================================================

cat("\n=== REVERSE CODING VARIABLES ===\n")

# SDST: Think things out before acting (reverse code)
if("bmsdsta0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$bmsdsta0)
  merged_data$sc3_think_act <- ifelse(var_numeric %in% c(-2, -1, 4), NA,
                                      ifelse(var_numeric == 1, 2,
                                             ifelse(var_numeric == 2, 1,
                                                    ifelse(var_numeric == 3, 0, var_numeric))))
  cat("✓ Reverse coded: bmsdsta0 -> sc3_think_act\n")
}

# Having at least one good friend (reverse code)
if("bmsdgfa0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$bmsdgfa0)
  merged_data$sc3_good_friend <- ifelse(var_numeric %in% c(-2, -1, 4), NA,
                                        ifelse(var_numeric == 1, 2,
                                               ifelse(var_numeric == 2, 1,
                                                      ifelse(var_numeric == 3, 0, var_numeric))))
  cat("✓ Reverse coded: bmsdgfa0 -> sc3_good_friend\n")
}

# Generally liked by other children (reverse code)
if("bmsdlca0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$bmsdlca0)
  merged_data$sc3_liked_children <- ifelse(var_numeric %in% c(-2, -1, 4), NA,
                                           ifelse(var_numeric == 1, 2,
                                                  ifelse(var_numeric == 2, 1,
                                                         ifelse(var_numeric == 3, 0, var_numeric))))
  cat("✓ Reverse coded: bmsdlca0 -> sc3_liked_children\n")
}

# Being considerate of other people's feelings (reverse code)
if("bmsdpfa0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$bmsdpfa0)
  merged_data$sc3_considerate <- ifelse(var_numeric %in% c(-2, -1, 4), NA,
                                        ifelse(var_numeric == 1, 2,
                                               ifelse(var_numeric == 2, 1,
                                                      ifelse(var_numeric == 3, 0, var_numeric))))
  cat("✓ Reverse coded: bmsdpfa0 -> sc3_considerate\n")
}

# Sharing readily with other children (reverse code)
if("bmsdsra0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$bmsdsra0)
  merged_data$sc3_sharing <- ifelse(var_numeric %in% c(-2, -1, 4), NA,
                                    ifelse(var_numeric == 1, 2,
                                           ifelse(var_numeric == 2, 1,
                                                  ifelse(var_numeric == 3, 0, var_numeric))))
  cat("✓ Reverse coded: bmsdsra0 -> sc3_sharing\n")
}

# Being helpful if someone is hurt (reverse code)
if("bmsdhua0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$bmsdhua0)
  merged_data$sc3_helpful <- ifelse(var_numeric %in% c(-2, -1, 4), NA,
                                    ifelse(var_numeric == 1, 2,
                                           ifelse(var_numeric == 2, 1,
                                                  ifelse(var_numeric == 3, 0, var_numeric))))
  cat("✓ Reverse coded: bmsdhua0 -> sc3_helpful\n")
}

# Being kind to younger children (reverse code)
if("bmsdkya0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$bmsdkya0)
  merged_data$sc3_kind_younger <- ifelse(var_numeric %in% c(-2, -1, 4), NA,
                                         ifelse(var_numeric == 1, 2,
                                                ifelse(var_numeric == 2, 1,
                                                       ifelse(var_numeric == 3, 0, var_numeric))))
  cat("✓ Reverse coded: bmsdkya0 -> sc3_kind_younger\n")
}

# Often volunteering to help others (reverse code)
if("bmsdvha0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$bmsdvha0)
  merged_data$sc3_volunteer_help <- ifelse(var_numeric %in% c(-2, -1, 4), NA,
                                           ifelse(var_numeric == 1, 2,
                                                  ifelse(var_numeric == 2, 1,
                                                         ifelse(var_numeric == 3, 0, var_numeric))))
  cat("✓ Reverse coded: bmsdvha0 -> sc3_volunteer_help\n")
}

# Sees tasks through to the end, good attention span (reverse code)
if("bmsdtea0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$bmsdtea0)
  merged_data$sc3_task_completion <- ifelse(var_numeric %in% c(-2, -1, 4), NA,
                                            ifelse(var_numeric == 1, 2,
                                                   ifelse(var_numeric == 2, 1,
                                                          ifelse(var_numeric == 3, 0, var_numeric))))
  cat("✓ Reverse coded: bmsdtea0 -> sc3_task_completion\n")
}

# Generally obedient (reverse code)
if("bmsdora0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$bmsdora0)
  merged_data$sc3_obedient <- ifelse(var_numeric %in% c(-2, -1, 4), NA,
                                     ifelse(var_numeric == 1, 2,
                                            ifelse(var_numeric == 2, 1,
                                                   ifelse(var_numeric == 3, 0, var_numeric))))
  cat("✓ Reverse coded: bmsdora0 -> sc3_obedient\n")
}

# =============================================================================
# 5. RECODE VARIABLES THAT DO NOT NEED REVERSE CODING
# =============================================================================

cat("\n=== NORMAL CODING VARIABLES ===\n")

# Being rather solitary and tending to play alone (normal coding)
if("bmsdspa0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$bmsdspa0)
  merged_data$sc3_solitary <- ifelse(var_numeric %in% c(-2, -1, 4), NA,
                                     ifelse(var_numeric == 1, 0,
                                            ifelse(var_numeric == 2, 1,
                                                   ifelse(var_numeric == 3, 2, var_numeric))))
  cat("✓ Normal coded: bmsdspa0 -> sc3_solitary\n")
}

# Is easily distracted, concentration wanders (normal coding)
if("bmsddca0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$bmsddca0)
  merged_data$sc3_distracted <- ifelse(var_numeric %in% c(-2, -1, 4), NA,
                                       ifelse(var_numeric == 1, 0,
                                              ifelse(var_numeric == 2, 1,
                                                     ifelse(var_numeric == 3, 2, var_numeric))))
  cat("✓ Normal coded: bmsddca0 -> sc3_distracted\n")
}

# Getting on better with adults than other children (normal coding)
if("bmsdgba0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$bmsdgba0)
  merged_data$sc3_better_adults <- ifelse(var_numeric %in% c(-2, -1, 4), NA,
                                          ifelse(var_numeric == 1, 0,
                                                 ifelse(var_numeric == 2, 1,
                                                        ifelse(var_numeric == 3, 2, var_numeric))))
  cat("✓ Normal coded: bmsdgba0 -> sc3_better_adults\n")
}

# Often has temper tantrums or hot tempers (normal coding)
if("bmsdtta0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$bmsdtta0)
  merged_data$sc3_temper <- ifelse(var_numeric %in% c(-2, -1, 4), NA,
                                   ifelse(var_numeric == 1, 0,
                                          ifelse(var_numeric == 2, 1,
                                                 ifelse(var_numeric == 3, 2, var_numeric))))
  cat("✓ Normal coded: bmsdtta0 -> sc3_temper\n")
}

# Having many worries (normal coding)
if("bmsdmwa0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$bmsdmwa0)
  merged_data$sc3_worries <- ifelse(var_numeric %in% c(-2, -1, 4), NA,
                                    ifelse(var_numeric == 1, 0,
                                           ifelse(var_numeric == 2, 1,
                                                  ifelse(var_numeric == 3, 2, var_numeric))))
  cat("✓ Normal coded: bmsdmwa0 -> sc3_worries\n")
}

# Being often unhappy, down-hearted, or tearful (normal coding)
if("bmsduda0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$bmsduda0)
  merged_data$sc3_unhappy <- ifelse(var_numeric %in% c(-2, -1, 4), NA,
                                    ifelse(var_numeric == 1, 0,
                                           ifelse(var_numeric == 2, 1,
                                                  ifelse(var_numeric == 3, 2, var_numeric))))
  cat("✓ Normal coded: bmsduda0 -> sc3_unhappy\n")
}

# Being nervous or clingy in new situations (normal coding)
if("bmsdnca0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$bmsdnca0)
  merged_data$sc3_nervous <- ifelse(var_numeric %in% c(-2, -1, 4), NA,
                                    ifelse(var_numeric == 1, 0,
                                           ifelse(var_numeric == 2, 1,
                                                  ifelse(var_numeric == 3, 2, var_numeric))))
  cat("✓ Normal coded: bmsdnca0 -> sc3_nervous\n")
}

# Having many fears, being easily scared (normal coding)
if("bmsdfea0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$bmsdfea0)
  merged_data$sc3_fears <- ifelse(var_numeric %in% c(-2, -1, 4), NA,
                                  ifelse(var_numeric == 1, 0,
                                         ifelse(var_numeric == 2, 1,
                                                ifelse(var_numeric == 3, 2, var_numeric))))
  cat("✓ Normal coded: bmsdfea0 -> sc3_fears\n")
}

# Child is restless, overactive, cannot stay still for long (normal coding)
if("bmsdpba0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$bmsdpba0)
  merged_data$sc3_restless <- ifelse(var_numeric %in% c(-2, -1, 4), NA,
                                     ifelse(var_numeric == 1, 0,
                                            ifelse(var_numeric == 2, 1,
                                                   ifelse(var_numeric == 3, 2, var_numeric))))
  cat("✓ Normal coded: bmsdpba0 -> sc3_restless\n")
}

# Child is constantly fidgeting or squirming (normal coding)
if("bmsdfsa0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$bmsdfsa0)
  merged_data$sc3_fidgeting <- ifelse(var_numeric %in% c(-2, -1, 4), NA,
                                      ifelse(var_numeric == 1, 0,
                                             ifelse(var_numeric == 2, 1,
                                                    ifelse(var_numeric == 3, 2, var_numeric))))
  cat("✓ Normal coded: bmsdfsa0 -> sc3_fidgeting\n")
}

# Lying or cheating (normal coding)
if("bmsdoaa0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$bmsdoaa0)
  merged_data$sc3_lying <- ifelse(var_numeric %in% c(-2, -1, 4), NA,
                                  ifelse(var_numeric == 1, 0,
                                         ifelse(var_numeric == 2, 1,
                                                ifelse(var_numeric == 3, 2, var_numeric))))
  cat("✓ Normal coded: bmsdoaa0 -> sc3_lying\n")
}

# =============================================================================
# 6. CHECK RECODED VARIABLES
# =============================================================================

# List of all new self-control variables
sc3_vars_reverse <- c("sc3_think_act", "sc3_good_friend", "sc3_liked_children",
                      "sc3_considerate", "sc3_sharing", "sc3_helpful", "sc3_kind_younger", "sc3_volunteer_help",
                      "sc3_task_completion", "sc3_obedient")

sc3_vars_normal <- c("sc3_solitary", "sc3_distracted", "sc3_better_adults", "sc3_temper",
                     "sc3_worries", "sc3_unhappy", "sc3_nervous", "sc3_fears", "sc3_restless", "sc3_fidgeting", "sc3_lying")

all_sc3_vars <- c(sc3_vars_reverse, sc3_vars_normal)

# Check which recoded variables exist
existing_recoded <- all_sc3_vars[all_sc3_vars %in% names(merged_data)]

cat("\n=== RECODED VARIABLE DISTRIBUTIONS ===\n")
cat("Successfully recoded variables (", length(existing_recoded), "):\n")

for(var in existing_recoded) {
  cat("\n--- ", var, " ---\n")
  print(table(merged_data[[var]], useNA = "ifany"))
}

# =============================================================================
# 7. SUMMARY
# =============================================================================

cat("\n=== PROCESSING SUMMARY ===\n")
cat("Original variables found:", length(existing_vars), "/", length(all_self_control_vars), "\n")
cat("Variables requiring reverse coding:", length(sc3_vars_reverse), "(10 total)\n")
cat("Variables with normal coding:", length(sc3_vars_normal), "(11 total)\n")
cat("Successfully processed variables:", length(existing_recoded), "\n")

if(length(missing_vars) > 0) {
  cat("\nMissing variables that need to be checked:\n")
  for(var in missing_vars) {
    cat("- ", var, "\n")
  }
}

cat("\n=== VARIABLE MAPPING ===\n")
cat("Variables that need reverse coding (higher original score = lower self-control):\n")
reverse_mapping <- data.frame(
  Original = c("bmsdsta0", "bmsdgfa0", "bmsdlca0", "bmsdpfa0", 
               "bmsdsra0", "bmsdhua0", "bmsdkya0", "bmsdvha0", "bmsdtea0", "bmsdora0"),
  New = c("sc3_think_act", "sc3_good_friend", "sc3_liked_children",
          "sc3_considerate", "sc3_sharing", "sc3_helpful", "sc3_kind_younger", "sc3_volunteer_help", 
          "sc3_task_completion", "sc3_obedient"),
  Description = c("Think things out before acting", "Having at least one good friend",
                  "Generally liked by other children",
                  "Being considerate of feelings", "Sharing readily with children",
                  "Being helpful if someone hurt", "Being kind to younger children",
                  "Often volunteering to help others", "Sees tasks through to end", "Generally obedient")
)
print(reverse_mapping)

cat("\nVariables with normal coding (higher score = lower self-control):\n")
normal_mapping <- data.frame(
  Original = c("bmsdspa0", "bmsddca0", "bmsdgba0", "bmsdtta0", "bmsdmwa0",
               "bmsduda0", "bmsdnca0", "bmsdfea0", "bmsdpba0", "bmsdfsa0", "bmsdoaa0"),
  New = c("sc3_solitary", "sc3_distracted", "sc3_better_adults", "sc3_temper", "sc3_worries",
          "sc3_unhappy", "sc3_nervous", "sc3_fears", "sc3_restless", "sc3_fidgeting", "sc3_lying"),
  Description = c("Being rather solitary", "Easily distracted", "Getting on better with adults",
                  "Often has temper tantrums", "Having many worries", "Often unhappy/tearful",
                  "Nervous in new situations", "Having many fears", "Restless/overactive",
                  "Constantly fidgeting", "Lying or cheating")
)
print(normal_mapping)

cat("\nNote: All age 3 self-control variables are now processed in this script.\n")
cat("All recoded variables have prefix 'sc3_' to distinguish from other age variables.\n")
cat("Negative values (-2, -1) and value 4 are coded as missing (NA).\n")
cat("Reverse coding: 1->2, 2->1, 3->0 (lower final score = higher self-control)\n")
cat("Normal coding: 1->0, 2->1, 3->2 (higher final score = lower self-control)\n")

