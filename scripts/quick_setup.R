# =============================================================================
# QUICK SETUP SCRIPT
# Run this BEFORE measurement invariance testing
# =============================================================================

cat("=== QUICK SETUP FOR MEASUREMENT INVARIANCE ===\n")

# Load data if not already loaded
if(!exists("merged_data")) {
  cat("Loading data...\n")
  data_path <- "/home/<USER>/dissertation_folder/data"
  merged_data <- readRDS(file.path(data_path, "merged1203.rds"))
  cat("Data loaded. Dimensions:", dim(merged_data), "\n")
}

# Run recoding scripts in order
cat("\n1. Recoding Age 11 variables...\n")
source("scripts/recode_self_control.R")

cat("\n2. Recoding Age 14 variables...\n") 
source("scripts/recode_self_control_age14.R")

cat("\n3. Recoding Age 17 variables...\n")
source("scripts/recode_self_control_age17.R")

# Verify variables were created
cat("\n=== VERIFICATION ===\n")
sc11_vars <- paste0("sc11_", c("think_act", "considerate", "sharing", "helpful", 
                               "volunteer_help", "task_completion", "obedient", 
                               "distracted", "temper", "restless", "fidgeting", "lying"))
sc14_vars <- paste0("sc14_", c("think_act", "considerate", "sharing", "helpful", 
                               "volunteer_help", "task_completion", "obedient", 
                               "distracted", "temper", "restless", "fidgeting", "lying"))
sc17_vars <- paste0("sc17_", c("think_act", "considerate", "sharing", "helpful", 
                               "volunteer_help", "task_completion", "obedient", 
                               "distracted", "temper", "restless", "fidgeting", "lying"))

cat("Age 11 variables created:", sum(sc11_vars %in% names(merged_data)), "of", length(sc11_vars), "\n")
cat("Age 14 variables created:", sum(sc14_vars %in% names(merged_data)), "of", length(sc14_vars), "\n")
cat("Age 17 variables created:", sum(sc17_vars %in% names(merged_data)), "of", length(sc17_vars), "\n")

if(all(c(sc11_vars, sc14_vars, sc17_vars) %in% names(merged_data))) {
  cat("\n✅ SUCCESS: All variables created successfully!\n")
  
  # Save the updated dataset with recoded variables
  cat("Saving updated dataset with recoded variables...\n")
  saveRDS(merged_data, file.path(data_path, "merged1203_with_recoded.rds"))
  cat("Dataset saved as: merged1203_with_recoded.rds\n")
  
  cat("You can now run measurement invariance testing.\n")
} else {
  cat("\n❌ Some variables missing - check recoding scripts for errors\n")
}

cat("\n=== SETUP COMPLETE ===\n") 