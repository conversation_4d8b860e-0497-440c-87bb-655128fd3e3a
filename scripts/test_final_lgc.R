# =============================================================================
# TEST: FINAL CORRECTED SECOND-ORDER LGC
# Verify that proper identification produces meaningful growth parameters
# =============================================================================

library(pacman)
p_load(lavaan, dplyr)

cat("=== TESTING FINAL CORRECTED LGC APPROACH ===\n")

# Load data
data_path <- "/home/<USER>/dissertation_folder/data"
if(file.exists(file.path(data_path, "merged1203.rds"))) {
  merged_data <- readRDS(file.path(data_path, "merged1203.rds"))
  cat("Data loaded successfully\n")
  
  # Run recoding if needed
  if(!"sc11_task_completion" %in% names(merged_data)) {
    cat("Running recoding scripts...\n")
    source("scripts/recode_self_control.R")
    source("scripts/recode_self_control_age14.R") 
    source("scripts/recode_self_control_age17.R")
  }
  
  # Test with Executive Control
  executive_items <- c("task_completion", "distracted", "think_act")
  exec_vars <- c(paste0("sc11_", executive_items),
                 paste0("sc14_", executive_items), 
                 paste0("sc17_", executive_items))
  
  test_data <- merged_data[, exec_vars, drop = FALSE]
  complete_rows <- rowSums(!is.na(test_data)) > 0
  test_data <- test_data[complete_rows, ]
  
  cat("Test sample size:", nrow(test_data), "\n")
  
  # FINAL CORRECTED Model
  final_corrected_model <- '
  # First level: Executive factors
  Executive11 =~ sc11_task_completion + sc11_distracted + sc11_think_act
  Executive14 =~ sc14_task_completion + sc14_distracted + sc14_think_act
  Executive17 =~ sc17_task_completion + sc17_distracted + sc17_think_act
  
  # Second level: Growth factors  
  iExecutive =~ 1*Executive11 + 1*Executive14 + 1*Executive17
  sExecutive =~ 0*Executive11 + lambda1*Executive14 + 2*Executive17
  
  # NO constraints on first-order factor means (they are FREE)
  # NO manual indicator intercept constraints (strict invariance handles this)
  # Identification via std.lv = TRUE (fixes factor variances to 1)
  '
  
  cat("\nFitting final corrected model...\n")
  fit_final <- tryCatch({
    sem(
      model = final_corrected_model,
      data = test_data,
      estimator = "WLSMV",
      ordered = exec_vars,
      missing = "pairwise",
      std.lv = TRUE,      # Fix factor variances to 1 for identification
      meanstructure = TRUE # Enable mean structure modeling
    )
  }, error = function(e) {
    cat("Error:", e$message, "\n")
    return(NULL)
  })
  
  if(!is.null(fit_final)) {
    converged <- lavInspect(fit_final, "converged")
    cat("Model converged:", converged, "\n")
    
    if(converged) {
      cat("✅ Final corrected model converged successfully!\n")
      
      # Check for negative variances (Heywood cases)
      param_table <- parameterEstimates(fit_final)
      variances <- param_table[param_table$op == "~~" & param_table$lhs == param_table$rhs, ]
      negative_vars <- variances[variances$est < 0, ]
      
      if(nrow(negative_vars) > 0) {
        cat("⚠️ WARNING: Negative variances detected:\n")
        print(negative_vars[, c("lhs", "est", "pvalue")])
      } else {
        cat("✅ No negative variances (Heywood cases)\n")
      }
      
      # Check growth parameters
      intercept_mean <- param_table[param_table$lhs == "iExecutive" & param_table$op == "~1", ]
      slope_mean <- param_table[param_table$lhs == "sExecutive" & param_table$op == "~1", ]
      lambda1_param <- param_table[param_table$label == "lambda1", ]
      
      cat("\n🎯 GROWTH PARAMETER RESULTS:\n")
      if(nrow(intercept_mean) > 0) {
        cat("Intercept mean:", round(intercept_mean$est, 4), 
            "(SE =", round(intercept_mean$se, 4), ")\n")
        if(!is.na(intercept_mean$pvalue)) {
          cat("  p-value:", round(intercept_mean$pvalue, 4), "\n")
        } else {
          cat("  p-value: NA (problematic!)\n")
        }
      }
      
      if(nrow(slope_mean) > 0) {
        cat("Slope mean:", round(slope_mean$est, 4), 
            "(SE =", round(slope_mean$se, 4), ")\n")
        if(!is.na(slope_mean$pvalue)) {
          cat("  p-value:", round(slope_mean$pvalue, 4), "\n")
          if(slope_mean$pvalue < 0.05) {
            if(slope_mean$est > 0) {
              cat("  → Significant INCREASE over time ✅\n")
            } else {
              cat("  → Significant DECREASE over time ✅\n")
            }
          } else {
            cat("  → No significant change\n")
          }
        } else {
          cat("  p-value: NA (problematic!)\n")
        }
      }
      
      if(nrow(lambda1_param) > 0) {
        cat("Lambda1 (empirical middle time):", round(lambda1_param$est, 4), "\n")
        cat("  Expected for linear: 1.0, Actual:", round(lambda1_param$est, 4), "\n")
        if(abs(lambda1_param$est - 1.0) > 0.3) {
          cat("  → Non-linear growth pattern detected\n")
        } else {
          cat("  → Linear growth pattern supported\n")
        }
      }
      
      # Model fit
      fit_measures <- fitMeasures(fit_final, c("cfi.scaled", "tli.scaled", "rmsea.scaled", "srmr"))
      cat("\n📈 MODEL FIT:\n")
      cat("CFI =", round(fit_measures["cfi.scaled"], 3), "\n")
      cat("TLI =", round(fit_measures["tli.scaled"], 3), "\n")
      cat("RMSEA =", round(fit_measures["rmsea.scaled"], 3), "\n")
      cat("SRMR =", round(fit_measures["srmr"], 3), "\n")
      
      if(fit_measures["cfi.scaled"] >= 0.95 && fit_measures["rmsea.scaled"] <= 0.06) {
        cat("✅ GOOD model fit achieved!\n")
      } else if(fit_measures["cfi.scaled"] >= 0.90 && fit_measures["rmsea.scaled"] <= 0.08) {
        cat("✅ ACCEPTABLE model fit achieved!\n")
      } else {
        cat("⚠️ Poor model fit - may need further specification\n")
      }
      
      cat("\n✅ FINAL CORRECTED APPROACH WORKING!\n")
      
    } else {
      cat("❌ Model did not converge\n")
      
      # Check lavaan warnings/errors
      warnings <- lavInspect(fit_final, "post.check")
      if(length(warnings) > 0) {
        cat("Model warnings:\n")
        print(warnings)
      }
    }
    
  } else {
    cat("❌ Model fitting failed completely\n")
  }
  
} else {
  cat("❌ Data file not found\n")
}

cat("\n=== FINAL TEST COMPLETE ===\n") 