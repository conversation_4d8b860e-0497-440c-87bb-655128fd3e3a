# =============================================================================
 # Recode Self-Control age 7 variables
# =============================================================================

# Load packages
library(pacman)
p_load(psych, corrplot, lavaan, semTools, VIM)

# =============================================================================
# 1. SELF-CONTROL VARIABLES DEFINITION
# =============================================================================

# Variables that NEED reverse coding
self_control_reverse <- c(
  "dmsdsta0",  # SDST: Think things out before acting
  "dmsdgfa0",  # Having at least one good friend
  "dmsdlca0",   # Generally liked by other children
  "dmsdpfa0",  # Being considerate of other people's feelings
  "dmsdsra0",  # Sharing readily with other children
  "dmsdhua0",  # Being helpful if someone is hurt
  "dmsdkya0",  # Being kind to younger children
  "dmsdvha0",  # Often volunteering to help others
  "dmsdtea0",  # Sees tasks through to the end, good attention span
  "dmsdora0"   # Generally obedient
)

# Variables that do NOT need reverse coding
self_control_normal <- c(
  "dmsdspa0",  # Being rather solitary and tending to play alone
  "dmsddca0",  # Is easily distracted, concentration wanders
  "dmsdgba0",  # Getting on better with adults than other children
  "dmsdtta0",  # Often has temper tantrums or hot tempers
  "dmsdmwa0",  # Having many worries
  "dmsduda0",  # Being often unhappy, down-hearted, or tearful
  "dmsdnca0",  # Being nervous or clingy in new situations
  "dmsdfea0",  # Having many fears, being easily scared
  "dmsdpba0",  # Child is restless, overactive, cannot stay still for long
  "dmsdfsa0",  # Child is constantly fidgeting or squirming
  "dmsdoaa0"   # Lying or cheating
)

# Combined list of all self-control variables
all_self_control_vars <- c(self_control_reverse, self_control_normal)

cat("=== SELF-CONTROL VARIABLES PROCESSING (AGE 7) ===\n")
cat("Variables requiring reverse coding:", length(self_control_reverse), "\n")
cat("Variables with normal coding:", length(self_control_normal), "\n")
cat("Total variables:", length(all_self_control_vars), "\n\n")

# =============================================================================
# 2. CHECK EXISTING VARIABLES
# =============================================================================

# Check which variables exist in the data
existing_vars <- all_self_control_vars[all_self_control_vars %in% names(merged_data)]
missing_vars <- all_self_control_vars[!all_self_control_vars %in% names(merged_data)]

cat("=== VARIABLE AVAILABILITY ===\n")
cat("Found variables (", length(existing_vars), "):", paste(existing_vars, collapse = ", "), "\n")
if(length(missing_vars) > 0) {
  cat("Missing variables (", length(missing_vars), "):", paste(missing_vars, collapse = ", "), "\n")
}
cat("\n")

# =============================================================================
# 3. DISPLAY FREQUENCY TABLES FOR EXISTING VARIABLES
# =============================================================================

cat("=== ORIGINAL VARIABLE DISTRIBUTIONS ===\n")
for(var in existing_vars) {
  cat("\n--- ", var, " ---\n")
  var_numeric <- as.vector(merged_data[[var]])
  print(table(var_numeric, useNA = "ifany"))
}

# =============================================================================
# 4. RECODE VARIABLES THAT NEED REVERSE CODING
# =============================================================================

cat("\n=== REVERSE CODING VARIABLES ===\n")

# SDST: Think things out before acting (reverse code)
if("dmsdsta0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$dmsdsta0)
  merged_data$sc7_think_act <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                      ifelse(var_numeric == 1, 2,
                                             ifelse(var_numeric == 2, 1,
                                                    ifelse(var_numeric == 3, 0, var_numeric))))
  cat("✓ Reverse coded: dmsdsta0 -> sc7_think_act\n")
}

# Having at least one good friend (reverse code)
if("dmsdgfa0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$dmsdgfa0)
  merged_data$sc7_good_friend <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                        ifelse(var_numeric == 1, 2,
                                               ifelse(var_numeric == 2, 1,
                                                      ifelse(var_numeric == 3, 0, var_numeric))))
  cat("✓ Reverse coded: dmsdgfa0 -> sc7_good_friend\n")
}

# Generally liked by other children (reverse code)
if("dmsdlca0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$dmsdlca0)
  merged_data$sc7_liked_children <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                           ifelse(var_numeric == 1, 2,
                                                  ifelse(var_numeric == 2, 1,
                                                         ifelse(var_numeric == 3, 0, var_numeric))))
  cat("✓ Reverse coded: dmsdlca0 -> sc7_liked_children\n")
}

# Being considerate of other people's feelings (reverse code)
if("dmsdpfa0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$dmsdpfa0)
  merged_data$sc7_considerate <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                        ifelse(var_numeric == 1, 2,
                                               ifelse(var_numeric == 2, 1,
                                                      ifelse(var_numeric == 3, 0, var_numeric))))
  cat("✓ Reverse coded: dmsdpfa0 -> sc7_considerate\n")
}

# Sharing readily with other children (reverse code)
if("dmsdsra0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$dmsdsra0)
  merged_data$sc7_sharing <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                    ifelse(var_numeric == 1, 2,
                                           ifelse(var_numeric == 2, 1,
                                                  ifelse(var_numeric == 3, 0, var_numeric))))
  cat("✓ Reverse coded: dmsdsra0 -> sc7_sharing\n")
}

# Being helpful if someone is hurt (reverse code)
if("dmsdhua0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$dmsdhua0)
  merged_data$sc7_helpful <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                    ifelse(var_numeric == 1, 2,
                                           ifelse(var_numeric == 2, 1,
                                                  ifelse(var_numeric == 3, 0, var_numeric))))
  cat("✓ Reverse coded: dmsdhua0 -> sc7_helpful\n")
}

# Being kind to younger children (reverse code)
if("dmsdkya0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$dmsdkya0)
  merged_data$sc7_kind_younger <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                         ifelse(var_numeric == 1, 2,
                                                ifelse(var_numeric == 2, 1,
                                                       ifelse(var_numeric == 3, 0, var_numeric))))
  cat("✓ Reverse coded: dmsdkya0 -> sc7_kind_younger\n")
}

# Often volunteering to help others (reverse code)
if("dmsdvha0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$dmsdvha0)
  merged_data$sc7_volunteer_help <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                           ifelse(var_numeric == 1, 2,
                                                  ifelse(var_numeric == 2, 1,
                                                         ifelse(var_numeric == 3, 0, var_numeric))))
  cat("✓ Reverse coded: dmsdvha0 -> sc7_volunteer_help\n")
}

# Sees tasks through to the end, good attention span (reverse code)
if("dmsdtea0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$dmsdtea0)
  merged_data$sc7_task_completion <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                            ifelse(var_numeric == 1, 2,
                                                   ifelse(var_numeric == 2, 1,
                                                          ifelse(var_numeric == 3, 0, var_numeric))))
  cat("✓ Reverse coded: dmsdtea0 -> sc7_task_completion\n")
}

# Generally obedient (reverse code)
if("dmsdora0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$dmsdora0)
  merged_data$sc7_obedient <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                     ifelse(var_numeric == 1, 2,
                                            ifelse(var_numeric == 2, 1,
                                                   ifelse(var_numeric == 3, 0, var_numeric))))
  cat("✓ Reverse coded: dmsdora0 -> sc7_obedient\n")
}

# =============================================================================
# 5. RECODE VARIABLES THAT DO NOT NEED REVERSE CODING
# =============================================================================

cat("\n=== NORMAL CODING VARIABLES ===\n")

# Being rather solitary and tending to play alone (normal coding)
if("dmsdspa0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$dmsdspa0)
  merged_data$sc7_solitary <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                     ifelse(var_numeric == 1, 0,
                                            ifelse(var_numeric == 2, 1,
                                                   ifelse(var_numeric == 3, 2, var_numeric))))
  cat("✓ Normal coded: dmsdspa0 -> sc7_solitary\n")
}

# Is easily distracted, concentration wanders (normal coding)
if("dmsddca0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$dmsddca0)
  merged_data$sc7_distracted <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                       ifelse(var_numeric == 1, 0,
                                              ifelse(var_numeric == 2, 1,
                                                     ifelse(var_numeric == 3, 2, var_numeric))))
  cat("✓ Normal coded: dmsddca0 -> sc7_distracted\n")
}

# Getting on better with adults than other children (normal coding)
if("dmsdgba0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$dmsdgba0)
  merged_data$sc7_better_adults <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                          ifelse(var_numeric == 1, 0,
                                                 ifelse(var_numeric == 2, 1,
                                                        ifelse(var_numeric == 3, 2, var_numeric))))
  cat("✓ Normal coded: dmsdgba0 -> sc7_better_adults\n")
}

# Often has temper tantrums or hot tempers (normal coding)
if("dmsdtta0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$dmsdtta0)
  merged_data$sc7_temper <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                   ifelse(var_numeric == 1, 0,
                                          ifelse(var_numeric == 2, 1,
                                                 ifelse(var_numeric == 3, 2, var_numeric))))
  cat("✓ Normal coded: dmsdtta0 -> sc7_temper\n")
}

# Having many worries (normal coding)
if("dmsdmwa0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$dmsdmwa0)
  merged_data$sc7_worries <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                    ifelse(var_numeric == 1, 0,
                                           ifelse(var_numeric == 2, 1,
                                                  ifelse(var_numeric == 3, 2, var_numeric))))
  cat("✓ Normal coded: dmsdmwa0 -> sc7_worries\n")
}

# Being often unhappy, down-hearted, or tearful (normal coding)
if("dmsduda0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$dmsduda0)
  merged_data$sc7_unhappy <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                    ifelse(var_numeric == 1, 0,
                                           ifelse(var_numeric == 2, 1,
                                                  ifelse(var_numeric == 3, 2, var_numeric))))
  cat("✓ Normal coded: dmsduda0 -> sc7_unhappy\n")
}

# Being nervous or clingy in new situations (normal coding)
if("dmsdnca0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$dmsdnca0)
  merged_data$sc7_nervous <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                    ifelse(var_numeric == 1, 0,
                                           ifelse(var_numeric == 2, 1,
                                                  ifelse(var_numeric == 3, 2, var_numeric))))
  cat("✓ Normal coded: dmsdnca0 -> sc7_nervous\n")
}

# Having many fears, being easily scared (normal coding)
if("dmsdfea0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$dmsdfea0)
  merged_data$sc7_fears <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                  ifelse(var_numeric == 1, 0,
                                         ifelse(var_numeric == 2, 1,
                                                ifelse(var_numeric == 3, 2, var_numeric))))
  cat("✓ Normal coded: dmsdfea0 -> sc7_fears\n")
}

# Child is restless, overactive, cannot stay still for long (normal coding)
if("dmsdpba0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$dmsdpba0)
  merged_data$sc7_restless <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                     ifelse(var_numeric == 1, 0,
                                            ifelse(var_numeric == 2, 1,
                                                   ifelse(var_numeric == 3, 2, var_numeric))))
  cat("✓ Normal coded: dmsdpba0 -> sc7_restless\n")
}

# Child is constantly fidgeting or squirming (normal coding)
if("dmsdfsa0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$dmsdfsa0)
  merged_data$sc7_fidgeting <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                      ifelse(var_numeric == 1, 0,
                                             ifelse(var_numeric == 2, 1,
                                                    ifelse(var_numeric == 3, 2, var_numeric))))
  cat("✓ Normal coded: dmsdfsa0 -> sc7_fidgeting\n")
}

# Lying or cheating (normal coding)
if("dmsdoaa0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$dmsdoaa0)
  merged_data$sc7_lying <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                  ifelse(var_numeric == 1, 0,
                                         ifelse(var_numeric == 2, 1,
                                                ifelse(var_numeric == 3, 2, var_numeric))))
  cat("✓ Normal coded: dmsdoaa0 -> sc7_lying\n")
}

# =============================================================================
# 6. CHECK RECODED VARIABLES
# =============================================================================

# List of all new self-control variables
sc7_vars_reverse <- c("sc7_think_act", "sc7_good_friend", "sc7_liked_children",
                      "sc7_considerate", "sc7_sharing", "sc7_helpful", "sc7_kind_younger", "sc7_volunteer_help",
                      "sc7_task_completion", "sc7_obedient")

sc7_vars_normal <- c("sc7_solitary", "sc7_distracted", "sc7_better_adults", "sc7_temper",
                     "sc7_worries", "sc7_unhappy", "sc7_nervous", "sc7_fears", "sc7_restless", "sc7_fidgeting", "sc7_lying")

all_sc7_vars <- c(sc7_vars_reverse, sc7_vars_normal)

# Check which recoded variables exist
existing_recoded <- all_sc7_vars[all_sc7_vars %in% names(merged_data)]

cat("\n=== RECODED VARIABLE DISTRIBUTIONS ===\n")
cat("Successfully recoded variables (", length(existing_recoded), "):\n")

for(var in existing_recoded) {
  cat("\n--- ", var, " ---\n")
  print(table(merged_data[[var]], useNA = "ifany"))
}

# =============================================================================
# 7. SUMMARY
# =============================================================================

cat("\n=== PROCESSING SUMMARY ===\n")
cat("Original variables found:", length(existing_vars), "/", length(all_self_control_vars), "\n")
cat("Variables requiring reverse coding:", length(sc7_vars_reverse), "(10 total)\n")
cat("Variables with normal coding:", length(sc7_vars_normal), "(11 total)\n")
cat("Successfully processed variables:", length(existing_recoded), "\n")

if(length(missing_vars) > 0) {
  cat("\nMissing variables that need to be checked:\n")
  for(var in missing_vars) {
    cat("- ", var, "\n")
  }
}

cat("\n=== VARIABLE MAPPING ===\n")
cat("Variables that need reverse coding (higher original score = lower self-control):\n")
reverse_mapping <- data.frame(
  Original = c("dmsdsta0", "dmsdgfa0", "dmsdlca0", "dmsdpfa0", 
               "dmsdsra0", "dmsdhua0", "dmsdkya0", "dmsdvha0", "dmsdtea0", "dmsdora0"),
  New = c("sc7_think_act", "sc7_good_friend", "sc7_liked_children",
          "sc7_considerate", "sc7_sharing", "sc7_helpful", "sc7_kind_younger", "sc7_volunteer_help", 
          "sc7_task_completion", "sc7_obedient"),
  Description = c("Think things out before acting", "Having at least one good friend",
                  "Generally liked by other children",
                  "Being considerate of feelings", "Sharing readily with children",
                  "Being helpful if someone hurt", "Being kind to younger children",
                  "Often volunteering to help others", "Sees tasks through to end", "Generally obedient")
)
print(reverse_mapping)

cat("\nVariables with normal coding (higher score = lower self-control):\n")
normal_mapping <- data.frame(
  Original = c("dmsdspa0", "dmsddca0", "dmsdgba0", "dmsdtta0", "dmsdmwa0",
               "dmsduda0", "dmsdnca0", "dmsdfea0", "dmsdpba0", "dmsdfsa0", "dmsdoaa0"),
  New = c("sc7_solitary", "sc7_distracted", "sc7_better_adults", "sc7_temper", "sc7_worries",
          "sc7_unhappy", "sc7_nervous", "sc7_fears", "sc7_restless", "sc7_fidgeting", "sc7_lying"),
  Description = c("Being rather solitary", "Easily distracted", "Getting on better with adults",
                  "Often has temper tantrums", "Having many worries", "Often unhappy/tearful",
                  "Nervous in new situations", "Having many fears", "Restless/overactive",
                  "Constantly fidgeting", "Lying or cheating")
)
print(normal_mapping)

cat("\nNote: All age 7 self-control variables are now processed in this script.\n")
cat("All recoded variables have prefix 'sc7_' to distinguish from other age variables.\n")
cat("All negative values (-9, -8, -1) and value 4 are coded as missing (NA).\n")
cat("Reverse coding: 1->2, 2->1, 3->0 (lower final score = higher self-control)\n")
cat("Normal coding: 1->0, 2->1, 3->2 (higher final score = lower self-control)\n")

