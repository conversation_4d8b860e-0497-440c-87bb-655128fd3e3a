# =============================================================================
 # Recode Self-Control age 5 variables
# =============================================================================

# Load packages
library(pacman)
p_load(psych, corrplot, lavaan, semTools, VIM)

# =============================================================================
# 1. SELF-CONTROL VARIABLES DEFINITION
# =============================================================================

# Variables that NEED reverse coding
self_control_reverse <- c(
  "cmsdsta0",  # SDST: Think things out before acting
  "cmsdgfa0",  # Having at least one good friend
  "cmsdlca0",   # Generally liked by other children
  "cmsdpfa0",  # Being considerate of other people's feelings
  "cmsdsra0",  # Sharing readily with other children
  "cmsdhua0",  # Being helpful if someone is hurt
  "cmsdkya0",  # Being kind to younger children
  "cmsdvha0",  # Often volunteering to help others
  "cmsdtea0",  # Sees tasks through to the end, good attention span
  "cmsdora0"   # Generally obedient
)

# Variables that do NOT need reverse coding
self_control_normal <- c(
  "cmsdspa0",  # Being rather solitary and tending to play alone
  "cmsddca0",  # Is easily distracted, concentration wanders
  "cmsdgba0",  # Getting on better with adults than other children
  "cmsdtta0",  # Often has temper tantrums or hot tempers
  "cmsdmwa0",  # Having many worries
  "cmsduda0",  # Being often unhappy, down-hearted, or tearful
  "cmsdnca0",  # Being nervous or clingy in new situations
  "cmsdfea0",  # Having many fears, being easily scared
  "cmsdpba0",  # Child is restless, overactive, cannot stay still for long
  "cmsdfsa0",  # Child is constantly fidgeting or squirming
  "cmsdoaa0"   # Lying or cheating
)

# Combined list of all self-control variables
all_self_control_vars <- c(self_control_reverse, self_control_normal)

# Note: epsdoa00 (lying/cheating) and epsdor00 (generally obedient) are handled in measurement_risk_taking.R

cat("=== SELF-CONTROL VARIABLES PROCESSING (AGE 5) ===\n")
cat("Variables requiring reverse coding:", length(self_control_reverse), "\n")
cat("Variables with normal coding:", length(self_control_normal), "\n")
cat("Total variables:", length(all_self_control_vars), "\n\n")

# =============================================================================
# 2. CHECK EXISTING VARIABLES
# =============================================================================

# Check which variables exist in the data
existing_vars <- all_self_control_vars[all_self_control_vars %in% names(merged_data)]
missing_vars <- all_self_control_vars[!all_self_control_vars %in% names(merged_data)]

cat("=== VARIABLE AVAILABILITY ===\n")
cat("Found variables (", length(existing_vars), "):", paste(existing_vars, collapse = ", "), "\n")
if(length(missing_vars) > 0) {
  cat("Missing variables (", length(missing_vars), "):", paste(missing_vars, collapse = ", "), "\n")
}
cat("\n")

# =============================================================================
# 3. DISPLAY FREQUENCY TABLES FOR EXISTING VARIABLES
# =============================================================================

cat("=== ORIGINAL VARIABLE DISTRIBUTIONS ===\n")
for(var in existing_vars) {
  cat("\n--- ", var, " ---\n")
  var_numeric <- as.vector(merged_data[[var]])
  print(table(var_numeric, useNA = "ifany"))
}

# =============================================================================
# 4. RECODE VARIABLES THAT NEED REVERSE CODING
# =============================================================================

cat("\n=== REVERSE CODING VARIABLES ===\n")

# SDST: Think things out before acting (reverse code)
if("cmsdsta0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$cmsdsta0)
  merged_data$sc5_think_act <- ifelse(var_numeric %in% c(-9, -8, -1, 4), NA,
                                      ifelse(var_numeric == 1, 2,
                                             ifelse(var_numeric == 2, 1,
                                                    ifelse(var_numeric == 3, 0, var_numeric))))
  cat("✓ Reverse coded: cmsdsta0 -> sc5_think_act\n")
}

# Having at least one good friend (reverse code)
if("cmsdgfa0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$cmsdgfa0)
  merged_data$sc5_good_friend <- ifelse(var_numeric %in% c(-9, -8, -1, 4), NA,
                                        ifelse(var_numeric == 1, 2,
                                               ifelse(var_numeric == 2, 1,
                                                      ifelse(var_numeric == 3, 0, var_numeric))))
  cat("✓ Reverse coded: cmsdgfa0 -> sc5_good_friend\n")
}

# Generally liked by other children (reverse code)
if("cmsdlca0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$cmsdlca0)
  merged_data$sc5_liked_children <- ifelse(var_numeric %in% c(-9, -8, -1, 4), NA,
                                           ifelse(var_numeric == 1, 2,
                                                  ifelse(var_numeric == 2, 1,
                                                         ifelse(var_numeric == 3, 0, var_numeric))))
  cat("✓ Reverse coded: cmsdlca0 -> sc5_liked_children\n")
}

# Being considerate of other people's feelings (reverse code)
if("cmsdpfa0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$cmsdpfa0)
  merged_data$sc5_considerate <- ifelse(var_numeric %in% c(-9, -8, -1, 4), NA,
                                        ifelse(var_numeric == 1, 2,
                                               ifelse(var_numeric == 2, 1,
                                                      ifelse(var_numeric == 3, 0, var_numeric))))
  cat("✓ Reverse coded: cmsdpfa0 -> sc5_considerate\n")
}

# Sharing readily with other children (reverse code)
if("cmsdsra0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$cmsdsra0)
  merged_data$sc5_sharing <- ifelse(var_numeric %in% c(-9, -8, -1, 4), NA,
                                    ifelse(var_numeric == 1, 2,
                                           ifelse(var_numeric == 2, 1,
                                                  ifelse(var_numeric == 3, 0, var_numeric))))
  cat("✓ Reverse coded: cmsdsra0 -> sc5_sharing\n")
}

# Being helpful if someone is hurt (reverse code)
if("cmsdhua0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$cmsdhua0)
  merged_data$sc5_helpful <- ifelse(var_numeric %in% c(-9, -8, -1, 4), NA,
                                    ifelse(var_numeric == 1, 2,
                                           ifelse(var_numeric == 2, 1,
                                                  ifelse(var_numeric == 3, 0, var_numeric))))
  cat("✓ Reverse coded: cmsdhua0 -> sc5_helpful\n")
}

# Being kind to younger children (reverse code)
if("cmsdkya0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$cmsdkya0)
  merged_data$sc5_kind_younger <- ifelse(var_numeric %in% c(-9, -8, -1, 4), NA,
                                         ifelse(var_numeric == 1, 2,
                                                ifelse(var_numeric == 2, 1,
                                                       ifelse(var_numeric == 3, 0, var_numeric))))
  cat("✓ Reverse coded: cmsdkya0 -> sc5_kind_younger\n")
}

# Often volunteering to help others (reverse code)
if("cmsdvha0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$cmsdvha0)
  merged_data$sc5_volunteer_help <- ifelse(var_numeric %in% c(-9, -8, -1, 4), NA,
                                           ifelse(var_numeric == 1, 2,
                                                  ifelse(var_numeric == 2, 1,
                                                         ifelse(var_numeric == 3, 0, var_numeric))))
  cat("✓ Reverse coded: cmsdvha0 -> sc5_volunteer_help\n")
}

# Sees tasks through to the end, good attention span (reverse code)
if("cmsdtea0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$cmsdtea0)
  merged_data$sc5_task_completion <- ifelse(var_numeric %in% c(-9, -8, -1, 4), NA,
                                            ifelse(var_numeric == 1, 2,
                                                   ifelse(var_numeric == 2, 1,
                                                          ifelse(var_numeric == 3, 0, var_numeric))))
  cat("✓ Reverse coded: cmsdtea0 -> sc5_task_completion\n")
}

# Generally obedient (reverse code)
if("cmsdora0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$cmsdora0)
  merged_data$sc5_obedient <- ifelse(var_numeric %in% c(-9, -8, -1, 4), NA,
                                     ifelse(var_numeric == 1, 2,
                                            ifelse(var_numeric == 2, 1,
                                                   ifelse(var_numeric == 3, 0, var_numeric))))
  cat("✓ Reverse coded: cmsdora0 -> sc5_obedient\n")
}

# =============================================================================
# 5. RECODE VARIABLES THAT DO NOT NEED REVERSE CODING
# =============================================================================

cat("\n=== NORMAL CODING VARIABLES ===\n")

# Being rather solitary and tending to play alone (normal coding)
if("cmsdspa0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$cmsdspa0)
  merged_data$sc5_solitary <- ifelse(var_numeric %in% c(-9, -8, -1, 4), NA,
                                     ifelse(var_numeric == 1, 0,
                                            ifelse(var_numeric == 2, 1,
                                                   ifelse(var_numeric == 3, 2, var_numeric))))
  cat("✓ Normal coded: cmsdspa0 -> sc5_solitary\n")
}

# Is easily distracted, concentration wanders (normal coding)
if("cmsddca0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$cmsddca0)
  merged_data$sc5_distracted <- ifelse(var_numeric %in% c(-9, -8, -1, 4), NA,
                                       ifelse(var_numeric == 1, 0,
                                              ifelse(var_numeric == 2, 1,
                                                     ifelse(var_numeric == 3, 2, var_numeric))))
  cat("✓ Normal coded: cmsddca0 -> sc5_distracted\n")
}

# Getting on better with adults than other children (normal coding)
if("cmsdgba0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$cmsdgba0)
  merged_data$sc5_better_adults <- ifelse(var_numeric %in% c(-9, -8, -1, 4), NA,
                                          ifelse(var_numeric == 1, 0,
                                                 ifelse(var_numeric == 2, 1,
                                                        ifelse(var_numeric == 3, 2, var_numeric))))
  cat("✓ Normal coded: cmsdgba0 -> sc5_better_adults\n")
}

# Often has temper tantrums or hot tempers (normal coding)
if("cmsdtta0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$cmsdtta0)
  merged_data$sc5_temper <- ifelse(var_numeric %in% c(-9, -8, -1, 4), NA,
                                   ifelse(var_numeric == 1, 0,
                                          ifelse(var_numeric == 2, 1,
                                                 ifelse(var_numeric == 3, 2, var_numeric))))
  cat("✓ Normal coded: cmsdtta0 -> sc5_temper\n")
}

# Having many worries (normal coding)
if("cmsdmwa0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$cmsdmwa0)
  merged_data$sc5_worries <- ifelse(var_numeric %in% c(-9, -8, -1, 4), NA,
                                    ifelse(var_numeric == 1, 0,
                                           ifelse(var_numeric == 2, 1,
                                                  ifelse(var_numeric == 3, 2, var_numeric))))
  cat("✓ Normal coded: cmsdmwa0 -> sc5_worries\n")
}

# Being often unhappy, down-hearted, or tearful (normal coding)
if("cmsduda0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$cmsduda0)
  merged_data$sc5_unhappy <- ifelse(var_numeric %in% c(-9, -8, -1, 4), NA,
                                    ifelse(var_numeric == 1, 0,
                                           ifelse(var_numeric == 2, 1,
                                                  ifelse(var_numeric == 3, 2, var_numeric))))
  cat("✓ Normal coded: cmsduda0 -> sc5_unhappy\n")
}

# Being nervous or clingy in new situations (normal coding)
if("cmsdnca0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$cmsdnca0)
  merged_data$sc5_nervous <- ifelse(var_numeric %in% c(-9, -8, -1, 4), NA,
                                    ifelse(var_numeric == 1, 0,
                                           ifelse(var_numeric == 2, 1,
                                                  ifelse(var_numeric == 3, 2, var_numeric))))
  cat("✓ Normal coded: cmsdnca0 -> sc5_nervous\n")
}

# Having many fears, being easily scared (normal coding)
if("cmsdfea0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$cmsdfea0)
  merged_data$sc5_fears <- ifelse(var_numeric %in% c(-9, -8, -1, 4), NA,
                                  ifelse(var_numeric == 1, 0,
                                         ifelse(var_numeric == 2, 1,
                                                ifelse(var_numeric == 3, 2, var_numeric))))
  cat("✓ Normal coded: cmsdfea0 -> sc5_fears\n")
}

# Child is restless, overactive, cannot stay still for long (normal coding)
if("cmsdpba0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$cmsdpba0)
  merged_data$sc5_restless <- ifelse(var_numeric %in% c(-9, -8, -1, 4), NA,
                                     ifelse(var_numeric == 1, 0,
                                            ifelse(var_numeric == 2, 1,
                                                   ifelse(var_numeric == 3, 2, var_numeric))))
  cat("✓ Normal coded: cmsdpba0 -> sc5_restless\n")
}

# Child is constantly fidgeting or squirming (normal coding)
if("cmsdfsa0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$cmsdfsa0)
  merged_data$sc5_fidgeting <- ifelse(var_numeric %in% c(-9, -8, -1, 4), NA,
                                      ifelse(var_numeric == 1, 0,
                                             ifelse(var_numeric == 2, 1,
                                                    ifelse(var_numeric == 3, 2, var_numeric))))
  cat("✓ Normal coded: cmsdfsa0 -> sc5_fidgeting\n")
}

# Lying or cheating (normal coding)
if("cmsdoaa0" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$cmsdoaa0)
  merged_data$sc5_lying <- ifelse(var_numeric %in% c(-9, -8, -1, 4), NA,
                                  ifelse(var_numeric == 1, 0,
                                         ifelse(var_numeric == 2, 1,
                                                ifelse(var_numeric == 3, 2, var_numeric))))
  cat("✓ Normal coded: cmsdoaa0 -> sc5_lying\n")
}

# =============================================================================
# 6. CHECK RECODED VARIABLES
# =============================================================================

# List of all new self-control variables
sc5_vars_reverse <- c("sc5_think_act", "sc5_good_friend", "sc5_liked_children",
                      "sc5_considerate", "sc5_sharing", "sc5_helpful", "sc5_kind_younger", "sc5_volunteer_help",
                      "sc5_task_completion", "sc5_obedient")

sc5_vars_normal <- c("sc5_solitary", "sc5_distracted", "sc5_better_adults", "sc5_temper",
                     "sc5_worries", "sc5_unhappy", "sc5_nervous", "sc5_fears", "sc5_restless", "sc5_fidgeting", "sc5_lying")

all_sc5_vars <- c(sc5_vars_reverse, sc5_vars_normal)

# Check which recoded variables exist
existing_recoded <- all_sc5_vars[all_sc5_vars %in% names(merged_data)]

cat("\n=== RECODED VARIABLE DISTRIBUTIONS ===\n")
cat("Successfully recoded variables (", length(existing_recoded), "):\n")

for(var in existing_recoded) {
  cat("\n--- ", var, " ---\n")
  print(table(merged_data[[var]], useNA = "ifany"))
}

# =============================================================================
# 7. SUMMARY
# =============================================================================

cat("\n=== PROCESSING SUMMARY ===\n")
cat("Original variables found:", length(existing_vars), "/", length(all_self_control_vars), "\n")
cat("Variables requiring reverse coding:", length(sc5_vars_reverse), "(10 total)\n")
cat("Variables with normal coding:", length(sc5_vars_normal), "(11 total)\n")
cat("Successfully processed variables:", length(existing_recoded), "\n")

if(length(missing_vars) > 0) {
  cat("\nMissing variables that need to be checked:\n")
  for(var in missing_vars) {
    cat("- ", var, "\n")
  }
}

cat("\n=== VARIABLE MAPPING ===\n")
cat("Variables that need reverse coding (higher original score = lower self-control):\n")
reverse_mapping <- data.frame(
  Original = c("cmsdsta0", "cmsdgfa0", "cmsdlca0", "cmsdpfa0", 
               "cmsdsra0", "cmsdhua0", "cmsdkya0", "cmsdvha0", "cmsdtea0", "cmsdora0"),
  New = c("sc5_think_act", "sc5_good_friend", "sc5_liked_children",
          "sc5_considerate", "sc5_sharing", "sc5_helpful", "sc5_kind_younger", "sc5_volunteer_help", 
          "sc5_task_completion", "sc5_obedient"),
  Description = c("Think things out before acting", "Having at least one good friend",
                  "Generally liked by other children",
                  "Being considerate of feelings", "Sharing readily with children",
                  "Being helpful if someone hurt", "Being kind to younger children",
                  "Often volunteering to help others", "Sees tasks through to end", "Generally obedient")
)
print(reverse_mapping)

cat("\nVariables with normal coding (higher score = lower self-control):\n")
normal_mapping <- data.frame(
  Original = c("cmsdspa0", "cmsddca0", "cmsdgba0", "cmsdtta0", "cmsdmwa0",
               "cmsduda0", "cmsdnca0", "cmsdfea0", "cmsdpba0", "cmsdfsa0", "cmsdoaa0"),
  New = c("sc5_solitary", "sc5_distracted", "sc5_better_adults", "sc5_temper", "sc5_worries",
          "sc5_unhappy", "sc5_nervous", "sc5_fears", "sc5_restless", "sc5_fidgeting", "sc5_lying"),
  Description = c("Being rather solitary", "Easily distracted", "Getting on better with adults",
                  "Often has temper tantrums", "Having many worries", "Often unhappy/tearful",
                  "Nervous in new situations", "Having many fears", "Restless/overactive",
                  "Constantly fidgeting", "Lying or cheating")
)
print(normal_mapping)

cat("\nNote: All age 5 self-control variables are now processed in this script.\n")
cat("All recoded variables have prefix 'sc5_' to distinguish from other age variables.\n")
cat("Negative values (-2, -1) and value 4 are coded as missing (NA).\n")
cat("Reverse coding: 1->2, 2->1, 3->0 (lower final score = higher self-control)\n")
cat("Normal coding: 1->0, 2->1, 3->2 (higher final score = lower self-control)\n")
