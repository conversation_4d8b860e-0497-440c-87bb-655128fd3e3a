library(pacman)
p_load(corrplot, psych, lavaan, semTools, VIM, Rcsdp)

# =============================================================================
# 2. IDENTIFY IMPULSIVITY VARIABLES
# =============================================================================

# Original impulsivity variables
imp_vars_original <- c("imp5_tem", "imp5_res", "imp5_fid", "imp5_dis", "imp5_thi", "imp5_tas")

# Check which variables exist
existing_original <- imp_vars_original[imp_vars_original %in% names(merged_data)]

cat("\nOriginal impulsivity variables found:", paste(existing_original, collapse = ", "), "\n")

# =============================================================================
# 3. INTERACTIVE FUNCTION FOR RELIABILITY ANALYSIS
# =============================================================================

examine_reliability <- function(variables, data = merged_data, scale_name = "Impulsivity Scale") {
  
  cat("\n" , rep("=", 60), "\n")
  cat("RELIABILITY ANALYSIS FOR:", scale_name, "\n")
  cat(rep("=", 60), "\n")
  
  # Check if variables exist
  available_vars <- variables[variables %in% names(data)]
  missing_vars <- variables[!variables %in% names(data)]
  
  if(length(missing_vars) > 0) {
    cat("WARNING: Missing variables:", paste(missing_vars, collapse = ", "), "\n")
  }
  
  if(length(available_vars) < 2) {
    cat("ERROR: Need at least 2 variables for reliability analysis\n")
    return(NULL)
  }
  
  # Extract data for analysis
  reliability_data <- data[, available_vars, drop = FALSE]
  
  # Remove rows with all missing values
  complete_cases <- complete.cases(reliability_data)
  reliability_data_clean <- reliability_data[complete_cases, ]
  
  cat("\nVariables included:", paste(available_vars, collapse = ", "), "\n")
  cat("Sample size (complete cases):", nrow(reliability_data_clean), "\n")
  cat("Missing data patterns:\n")
  print(VIM::aggr(reliability_data, plot = FALSE, sortVars = TRUE)$missings)
  
  # Descriptive statistics
  cat("\n--- DESCRIPTIVE STATISTICS ---\n")
  print(describe(reliability_data))
  
  # Correlation matrix
  cat("\n--- CORRELATION MATRIX ---\n")
  cor_matrix <- cor(reliability_data, use = "pairwise.complete.obs")
  print(round(cor_matrix, 3))
  
  # Plot correlation matrix
  corrplot(cor_matrix, method = "color", type = "upper", 
           order = "hclust", tl.cex = 0.8, tl.col = "black")
  
  # =============================================================================
  # RELIABILITY MEASURES
  # =============================================================================
  
  cat("\n--- RELIABILITY MEASURES ---\n")
  
  # 1. Cronbach's Alpha
  cat("\n1. CRONBACH'S ALPHA:\n")
  alpha_result <- psych::alpha(reliability_data, na.rm = TRUE)
  cat("Cronbach's Alpha:", round(alpha_result$total$raw_alpha, 4), "\n")
  cat("Standardized Alpha:", round(alpha_result$total$std.alpha, 4), "\n")
  cat("95% CI:", round(alpha_result$total$raw_alpha - 1.96*alpha_result$total$ase, 4), 
      "to", round(alpha_result$total$raw_alpha + 1.96*alpha_result$total$ase, 4), "\n")
  
  # 2. McDonald's Omega
  cat("\n2. MCDONALD'S OMEGA:\n")
  tryCatch({
    omega_result <- psych::omega(reliability_data, nfactors = 1, plot = FALSE)
    cat("Omega Total:", round(omega_result$omega.tot, 4), "\n")
    cat("Omega Hierarchical:", round(omega_result$omega_h, 4), "\n")
    cat("Omega Subscale:", round(omega_result$omega.lim, 4), "\n")
  }, error = function(e) {
    cat("Error calculating Omega:", e$message, "\n")
  })
  
  # 3. Greatest Lower Bound (GLB)
  cat("\n3. GREATEST LOWER BOUND (GLB):\n")
  tryCatch({
    glb_result <- psych::glb.algebraic(cor_matrix)
    cat("GLB:", round(glb_result$glb, 4), "\n")
  }, error = function(e) {
    cat("Error calculating GLB:", e$message, "\n")
  })
  
  # 4. Coefficient H (using fa function)
  cat("\n4. COEFFICIENT H:\n")
  tryCatch({
    fa_result <- psych::fa(reliability_data, nfactors = 1, rotate = "none", fm = "ml")
    # Calculate H using the factor loadings
    loadings <- as.vector(fa_result$loadings)
    communalities <- loadings^2
    coeff_h <- sum(communalities)^2 / (sum(communalities)^2 + sum(1 - communalities))
    cat("Coefficient H:", round(coeff_h, 4), "\n")
  }, error = function(e) {
    cat("Error calculating Coefficient H:", e$message, "\n")
  })
  
  # 5. Composite Reliability (using lavaan)
  cat("\n5. COMPOSITE RELIABILITY:\n")
  tryCatch({
    # Create lavaan model syntax
    var_names <- paste(available_vars, collapse = " + ")
    model_syntax <- paste("factor =~", var_names)
    
    # Fit CFA model
    fit <- lavaan::cfa(model_syntax, data = reliability_data, missing = "ML")
    
    # Calculate composite reliability
    comp_rel <- semTools::compRelSEM(fit)
    cat("Composite Reliability:", round(comp_rel, 4), "\n")
    
    # Model fit indices
    cat("\nModel Fit Indices:\n")
    fit_measures <- fitMeasures(fit, c("chisq", "df", "pvalue", "cfi", "tli", "rmsea", "srmr"))
    print(round(fit_measures, 4))
    
  }, error = function(e) {
    cat("Error calculating Composite Reliability:", e$message, "\n")
  })
  
  # Item-total correlations
  cat("\n--- ITEM-TOTAL CORRELATIONS ---\n")
  print(round(alpha_result$item.stats[, c("r.drop", "r.cor")], 3))
  
  # Alpha if item deleted
  cat("\n--- ALPHA IF ITEM DELETED ---\n")
  print(round(alpha_result$alpha.drop[, "raw_alpha"], 4))
  
  return(list(
    alpha = alpha_result,
    correlation_matrix = cor_matrix,
    descriptives = describe(reliability_data)
  ))
}

# =============================================================================
# 4. EXAMINE ORIGINAL IMPULSIVITY VARIABLES
# =============================================================================

# Examine original variables (if available)
if(length(existing_original) >= 2) {
  cat("\n\nEXAMINING ORIGINAL IMPULSIVITY VARIABLES:")
  original_results <- examine_reliability(existing_original, scale_name = "Original Impulsivity Scale")
}

