# =============================================================================
# Recode Self-Control age 14 variables
# =============================================================================

# Load packages
library(pacman)
p_load(psych, corrplot, lavaan, semTools, VIM)

# =============================================================================
# 1. SELF-CONTROL VARIABLES DEFINITION
# =============================================================================

# Variables that NEED reverse coding
self_control_reverse <- c(
  "fpsdst00",  # SDST: Think things out before acting
  "fpsdgf00",  # Having at least one good friend
  "fpsdlc00",   # Generally liked by other children
  "fpsdpf00",  # Being considerate of other people's feelings
  "fpsdsr00",  # Sharing readily with other children
  "fpsdhu00",  # Being helpful if someone is hurt
  "fpsdky00",  # Being kind to younger children
  "fpsdvh00",  # Often volunteering to help others
  "fpsdte00",  # Sees tasks through to the end, good attention span
  "fpsdor00"   # Generally obedient
)

# Variables that do NOT need reverse coding
self_control_normal <- c(
  "fpsdsp00",  # Being rather solitary and tending to play alone
  "fpsddc00",  # Is easily distracted, concentration wanders
  "fpsdgb00",  # Getting on better with adults than other children
  "fpsdtt00",  # Often has temper tantrums or hot tempers
  "fpsdmw00",  # Having many worries
  "fpsdud00",  # Being often unhappy, down-hearted, or tearful
  "fpsdnc00",  # Being nervous or clingy in new situations
  "fpsdfe00",  # Having many fears, being easily scared
  "fpsdpb00",  # Child is restless, overactive, cannot stay still for long
  "fpsdfs00",  # Child is constantly fidgeting or squirming
  "fpsdoa00"   # Lying or cheating
)

# Combined list of all self-control variables
all_self_control_vars <- c(self_control_reverse, self_control_normal)

cat("=== SELF-CONTROL VARIABLES PROCESSING (AGE 14) ===\n")
cat("Variables requiring reverse coding:", length(self_control_reverse), "\n")
cat("Variables with normal coding:", length(self_control_normal), "\n")
cat("Total variables:", length(all_self_control_vars), "\n\n")

# =============================================================================
# 2. CHECK EXISTING VARIABLES
# =============================================================================

# Check which variables exist in the data
existing_vars <- all_self_control_vars[all_self_control_vars %in% names(merged_data)]
missing_vars <- all_self_control_vars[!all_self_control_vars %in% names(merged_data)]

cat("=== VARIABLE AVAILABILITY ===\n")
cat("Found variables (", length(existing_vars), "):", paste(existing_vars, collapse = ", "), "\n")
if(length(missing_vars) > 0) {
  cat("Missing variables (", length(missing_vars), "):", paste(missing_vars, collapse = ", "), "\n")
}
cat("\n")

# =============================================================================
# 3. DISPLAY FREQUENCY TABLES FOR EXISTING VARIABLES
# =============================================================================

cat("=== ORIGINAL VARIABLE DISTRIBUTIONS ===\n")
for(var in existing_vars) {
  cat("\n--- ", var, " ---\n")
  var_numeric <- as.vector(merged_data[[var]])
  print(table(var_numeric, useNA = "ifany"))
}

# =============================================================================
# 4. RECODE VARIABLES THAT NEED REVERSE CODING
# =============================================================================

cat("\n=== REVERSE CODING VARIABLES ===\n")

# SDST: Think things out before acting (reverse code)
if("fpsdst00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$fpsdst00)
  merged_data$sc14_think_act <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                       ifelse(var_numeric == 1, 2,
                                              ifelse(var_numeric == 2, 1,
                                                     ifelse(var_numeric == 3, 0, NA))))
  cat("✓ Reverse coded: fpsdst00 -> sc14_think_act\n")
}

# Having at least one good friend (reverse code)
if("fpsdgf00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$fpsdgf00)
  merged_data$sc14_good_friend <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                         ifelse(var_numeric == 1, 2,
                                                ifelse(var_numeric == 2, 1,
                                                       ifelse(var_numeric == 3, 0, NA))))
  cat("✓ Reverse coded: fpsdgf00 -> sc14_good_friend\n")
}

# Generally liked by other children (reverse code)
if("fpsdlc00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$fpsdlc00)
  merged_data$sc14_liked_children <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                            ifelse(var_numeric == 1, 2,
                                                   ifelse(var_numeric == 2, 1,
                                                          ifelse(var_numeric == 3, 0, NA))))
  cat("✓ Reverse coded: fpsdlc00 -> sc14_liked_children\n")
}

# Being considerate of other people's feelings (reverse code)
if("fpsdpf00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$fpsdpf00)
  merged_data$sc14_considerate <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                         ifelse(var_numeric == 1, 2,
                                                ifelse(var_numeric == 2, 1,
                                                       ifelse(var_numeric == 3, 0, NA))))
  cat("✓ Reverse coded: fpsdpf00 -> sc14_considerate\n")
}

# Sharing readily with other children (reverse code)
if("fpsdsr00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$fpsdsr00)
  merged_data$sc14_sharing <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                     ifelse(var_numeric == 1, 2,
                                            ifelse(var_numeric == 2, 1,
                                                   ifelse(var_numeric == 3, 0, NA))))
  cat("✓ Reverse coded: fpsdsr00 -> sc14_sharing\n")
}

# Being helpful if someone is hurt (reverse code)
if("fpsdhu00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$fpsdhu00)
  merged_data$sc14_helpful <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                     ifelse(var_numeric == 1, 2,
                                            ifelse(var_numeric == 2, 1,
                                                   ifelse(var_numeric == 3, 0, NA))))
  cat("✓ Reverse coded: fpsdhu00 -> sc14_helpful\n")
}

# Being kind to younger children (reverse code)
if("fpsdky00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$fpsdky00)
  merged_data$sc14_kind_younger <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                          ifelse(var_numeric == 1, 2,
                                                 ifelse(var_numeric == 2, 1,
                                                        ifelse(var_numeric == 3, 0, NA))))
  cat("✓ Reverse coded: fpsdky00 -> sc14_kind_younger\n")
}

# Often volunteering to help others (reverse code)
if("fpsdvh00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$fpsdvh00)
  merged_data$sc14_volunteer_help <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                            ifelse(var_numeric == 1, 2,
                                                   ifelse(var_numeric == 2, 1,
                                                          ifelse(var_numeric == 3, 0, NA))))
  cat("✓ Reverse coded: fpsdvh00 -> sc14_volunteer_help\n")
}

# Sees tasks through to the end, good attention span (reverse code)
if("fpsdte00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$fpsdte00)
  merged_data$sc14_task_completion <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                             ifelse(var_numeric == 1, 2,
                                                    ifelse(var_numeric == 2, 1,
                                                           ifelse(var_numeric == 3, 0, NA))))
  cat("✓ Reverse coded: fpsdte00 -> sc14_task_completion\n")
}

# Generally obedient (reverse code)
if("fpsdor00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$fpsdor00)
  merged_data$sc14_obedient <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                      ifelse(var_numeric == 1, 2,
                                             ifelse(var_numeric == 2, 1,
                                                    ifelse(var_numeric == 3, 0, NA))))
  cat("✓ Reverse coded: fpsdor00 -> sc14_obedient\n")
}

# =============================================================================
# 5. RECODE VARIABLES THAT DO NOT NEED REVERSE CODING
# =============================================================================

cat("\n=== NORMAL CODING VARIABLES ===\n")

# Being rather solitary and tending to play alone (normal coding)
if("fpsdsp00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$fpsdsp00)
  merged_data$sc14_solitary <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                      ifelse(var_numeric == 1, 0,
                                             ifelse(var_numeric == 2, 1,
                                                    ifelse(var_numeric == 3, 2, NA))))
  cat("✓ Normal coded: fpsdsp00 -> sc14_solitary\n")
}

# Is easily distracted, concentration wanders (normal coding)
if("fpsddc00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$fpsddc00)
  merged_data$sc14_distracted <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                        ifelse(var_numeric == 1, 0,
                                               ifelse(var_numeric == 2, 1,
                                                      ifelse(var_numeric == 3, 2, NA))))
  cat("✓ Normal coded: fpsddc00 -> sc14_distracted\n")
}

# Getting on better with adults than other children (normal coding)
if("fpsdgb00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$fpsdgb00)
  merged_data$sc14_better_adults <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                           ifelse(var_numeric == 1, 0,
                                                  ifelse(var_numeric == 2, 1,
                                                         ifelse(var_numeric == 3, 2, NA))))
  cat("✓ Normal coded: fpsdgb00 -> sc14_better_adults\n")
}

# Often has temper tantrums or hot tempers (normal coding)
if("fpsdtt00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$fpsdtt00)
  merged_data$sc14_temper <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                    ifelse(var_numeric == 1, 0,
                                           ifelse(var_numeric == 2, 1,
                                                  ifelse(var_numeric == 3, 2, NA))))
  cat("✓ Normal coded: fpsdtt00 -> sc14_temper\n")
}

# Having many worries (normal coding)
if("fpsdmw00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$fpsdmw00)
  merged_data$sc14_worries <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                     ifelse(var_numeric == 1, 0,
                                            ifelse(var_numeric == 2, 1,
                                                   ifelse(var_numeric == 3, 2, NA))))
  cat("✓ Normal coded: fpsdmw00 -> sc14_worries\n")
}

# Being often unhappy, down-hearted, or tearful (normal coding)
if("fpsdud00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$fpsdud00)
  merged_data$sc14_unhappy <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                     ifelse(var_numeric == 1, 0,
                                            ifelse(var_numeric == 2, 1,
                                                   ifelse(var_numeric == 3, 2, NA))))
  cat("✓ Normal coded: fpsdud00 -> sc14_unhappy\n")
}

# Being nervous or clingy in new situations (normal coding)
if("fpsdnc00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$fpsdnc00)
  merged_data$sc14_nervous <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                     ifelse(var_numeric == 1, 0,
                                            ifelse(var_numeric == 2, 1,
                                                   ifelse(var_numeric == 3, 2, NA))))
  cat("✓ Normal coded: fpsdnc00 -> sc14_nervous\n")
}

# Having many fears, being easily scared (normal coding)
if("fpsdfe00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$fpsdfe00)
  merged_data$sc14_fears <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                   ifelse(var_numeric == 1, 0,
                                          ifelse(var_numeric == 2, 1,
                                                 ifelse(var_numeric == 3, 2, NA))))
  cat("✓ Normal coded: fpsdfe00 -> sc14_fears\n")
}

# Child is restless, overactive, cannot stay still for long (normal coding)
if("fpsdpb00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$fpsdpb00)
  merged_data$sc14_restless <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                      ifelse(var_numeric == 1, 0,
                                             ifelse(var_numeric == 2, 1,
                                                    ifelse(var_numeric == 3, 2, NA))))
  cat("✓ Normal coded: fpsdpb00 -> sc14_restless\n")
}

# Child is constantly fidgeting or squirming (normal coding)
if("fpsdfs00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$fpsdfs00)
  merged_data$sc14_fidgeting <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                       ifelse(var_numeric == 1, 0,
                                              ifelse(var_numeric == 2, 1,
                                                     ifelse(var_numeric == 3, 2, NA))))
  cat("✓ Normal coded: fpsdfs00 -> sc14_fidgeting\n")
}

# Lying or cheating (normal coding)
if("fpsdoa00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$fpsdoa00)
  merged_data$sc14_lying <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                   ifelse(var_numeric == 1, 0,
                                          ifelse(var_numeric == 2, 1,
                                                 ifelse(var_numeric == 3, 2, NA))))
  cat("✓ Normal coded: fpsdoa00 -> sc14_lying\n")
}

# =============================================================================
# 6. CHECK RECODED VARIABLES
# =============================================================================

# List of all new self-control variables
sc14_vars_reverse <- c("sc14_think_act", "sc14_good_friend", "sc14_liked_children",
                       "sc14_considerate", "sc14_sharing", "sc14_helpful", "sc14_kind_younger", "sc14_volunteer_help",
                       "sc14_task_completion", "sc14_obedient")

sc14_vars_normal <- c("sc14_solitary", "sc14_distracted", "sc14_better_adults", "sc14_temper",
                      "sc14_worries", "sc14_unhappy", "sc14_nervous", "sc14_fears", "sc14_restless", "sc14_fidgeting", "sc14_lying")

all_sc14_vars <- c(sc14_vars_reverse, sc14_vars_normal)

# Check which recoded variables exist
existing_recoded <- all_sc14_vars[all_sc14_vars %in% names(merged_data)]

cat("\n=== RECODED VARIABLE DISTRIBUTIONS ===\n")
cat("Successfully recoded variables (", length(existing_recoded), "):\n")

for(var in existing_recoded) {
  cat("\n--- ", var, " ---\n")
  print(table(merged_data[[var]], useNA = "ifany"))
}

# =============================================================================
# 7. SUMMARY
# =============================================================================

cat("\n=== PROCESSING SUMMARY ===\n")
cat("Original variables found:", length(existing_vars), "/", length(all_self_control_vars), "\n")
cat("Variables requiring reverse coding:", length(sc14_vars_reverse), "(10 total)\n")
cat("Variables with normal coding:", length(sc14_vars_normal), "(11 total)\n")
cat("Successfully processed variables:", length(existing_recoded), "\n")

if(length(missing_vars) > 0) {
  cat("\nMissing variables that need to be checked:\n")
  for(var in missing_vars) {
    cat("- ", var, "\n")
  }
}

cat("\n=== VARIABLE MAPPING ===\n")
cat("Variables that need reverse coding (higher original score = lower self-control):\n")
reverse_mapping <- data.frame(
  Original = c("fpsdst00", "fpsdgf00", "fpsdlc00", "fpsdpf00", 
               "fpsdsr00", "fpsdhu00", "fpsdky00", "fpsdvh00", "fpsdte00", "fpsdor00"),
  New = c("sc14_think_act", "sc14_good_friend", "sc14_liked_children",
          "sc14_considerate", "sc14_sharing", "sc14_helpful", "sc14_kind_younger", "sc14_volunteer_help", 
          "sc14_task_completion", "sc14_obedient"),
  Description = c("Think things out before acting", "Having at least one good friend",
                  "Generally liked by other children",
                  "Being considerate of feelings", "Sharing readily with children",
                  "Being helpful if someone hurt", "Being kind to younger children",
                  "Often volunteering to help others", "Sees tasks through to end", "Generally obedient")
)
print(reverse_mapping)

cat("\nVariables with normal coding (higher score = lower self-control):\n")
normal_mapping <- data.frame(
  Original = c("fpsdsp00", "fpsddc00", "fpsdgb00", "fpsdtt00", "fpsdmw00",
               "fpsdud00", "fpsdnc00", "fpsdfe00", "fpsdpb00", "fpsdfs00", "fpsdoa00"),
  New = c("sc14_solitary", "sc14_distracted", "sc14_better_adults", "sc14_temper", "sc14_worries",
          "sc14_unhappy", "sc14_nervous", "sc14_fears", "sc14_restless", "sc14_fidgeting", "sc14_lying"),
  Description = c("Being rather solitary", "Easily distracted", "Getting on better with adults",
                  "Often has temper tantrums", "Having many worries", "Often unhappy/tearful",
                  "Nervous in new situations", "Having many fears", "Restless/overactive",
                  "Constantly fidgeting", "Lying or cheating")
)
print(normal_mapping)

cat("\nNote: All age 14 self-control variables are now processed in this script.\n")
cat("All recoded variables have prefix 'sc14_' to distinguish from age 11 variables.\n") 