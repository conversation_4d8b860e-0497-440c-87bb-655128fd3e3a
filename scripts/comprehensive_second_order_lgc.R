# =============================================================================
# COMPREHENSIVE SECOND-ORDER LATENT GROWTH CURVE MODEL (CURVE-OF-FACTORS)
# WITH PARALLEL PROCESS FOR FIGHTING BEHAVIOR
# Self-Control: Configural → Metric → Scalar → Second-Order → Growth Factors
# + Fighting Behavior Growth Curve + Cross-Construct Correlations
# Ages 11, 14, and 17
# Expert-level implementation with measurement invariance progression
# =============================================================================

# ### 0. Preparation
library(pacman)
p_load(lavaan, semTools, dplyr, psych)

cat("=== COMPREHENSIVE SECOND-ORDER LGC + PARALLEL PROCESS MODEL ===\n")
cat("Implementation: Self-Control (Configural → Metric → Scalar → Second-Order → Growth)\n")
cat("               + Fighting Behavior (Parallel Growth Process)\n\n")

# SemTools aids automation of invariance tests and fit extraction.

# =============================================================================
# DATA PREPARATION
# =============================================================================

# Load data and ensure self-control variables exist
data_path <- "/home/<USER>/dissertation_folder/data"
merged_data <- readRDS(file.path(data_path, "merged1203.rds"))

# Ensure self-control recoding is complete
if(!all(c("sc11_think_act", "sc11_considerate") %in% names(merged_data))) {
  source("scripts/recode_self_control.R")
  source("scripts/recode_self_control_age14.R") 
  source("scripts/recode_self_control_age17.R")
}

# =============================================================================
# FIGHTING VARIABLES PREPARATION
# =============================================================================

cat("=== FIGHTING VARIABLES PREPARATION ===\n")

# Original fight variable names from the dataset
fight_vars_original <- c(
  "epsdfb00",  # Age 11: fight with others
  "fpsdfb00",  # Age 14: fight with others  
  "gpsdfb00"   # Age 17: fight with others
)

# Check availability
fight_vars_exist <- fight_vars_original[fight_vars_original %in% names(merged_data)]
cat("Original fight variables found:", paste(fight_vars_exist, collapse = ", "), "\n")

# Recode fight variables following established pattern:
# Original coding: 1=never, 2=sometimes, 3=often, -1/4/-9=missing
# New coding: 0=never, 1=sometimes, 2=often, NA=missing

if("epsdfb00" %in% names(merged_data)) {
  # Age 11: recode (-1 4 = .) (1=0) (2=1) (3=2)
  merged_data$agr11_fight <- ifelse(merged_data$epsdfb00 %in% c(-1, 4), NA,
                                    ifelse(merged_data$epsdfb00 == 1, 0,
                                           ifelse(merged_data$epsdfb00 == 2, 1,
                                                  ifelse(merged_data$epsdfb00 == 3, 2, NA))))
  cat("Age 11 fight variable recoded: agr11_fight\n")
}

if("fpsdfb00" %in% names(merged_data)) {
  # Age 14: recode (-1 -9 = .) (1=0) (2=1) (3=2)
  merged_data$agr14_fight <- ifelse(merged_data$fpsdfb00 %in% c(-1, -9), NA,
                                    ifelse(merged_data$fpsdfb00 == 1, 0,
                                           ifelse(merged_data$fpsdfb00 == 2, 1,
                                                  ifelse(merged_data$fpsdfb00 == 3, 2, NA))))
  cat("Age 14 fight variable recoded: agr14_fight\n")
}

if("gpsdfb00" %in% names(merged_data)) {
  # Age 17: recode (-1 4 = .) (1=0) (2=1) (3=2)
  merged_data$agr17_fight <- ifelse(merged_data$gpsdfb00 %in% c(-1, 4), NA,
                                    ifelse(merged_data$gpsdfb00 == 1, 0,
                                           ifelse(merged_data$gpsdfb00 == 2, 1,
                                                  ifelse(merged_data$gpsdfb00 == 3, 2, NA))))
  cat("Age 17 fight variable recoded: agr17_fight\n")
}

# Check recoded variables
fight_recoded_vars <- c("agr11_fight", "agr14_fight", "agr17_fight")
fight_available <- fight_recoded_vars[fight_recoded_vars %in% names(merged_data)]

cat("Recoded fight variables available:", paste(fight_available, collapse = ", "), "\n")

# Descriptive statistics for fight variables
if(length(fight_available) > 0) {
  cat("\nDescriptive statistics for fight variables:\n")
  for(var in fight_available) {
    if(var %in% names(merged_data)) {
      cat("\n", var, ":\n")
      print(table(merged_data[[var]], useNA = "ifany"))
      cat("Mean (excluding NA):", round(mean(merged_data[[var]], na.rm = TRUE), 3), "\n")
    }
  }
}

# ### 1. Variable naming assumptions
# Your observed indicators:
# - Executive function (5 items) at ages 11,14,17: sc11_task_completion, ..., sc17_restless
# - Self-centeredness (prosocial reverse) (4 items)
# - Temper (3 items)
# - Fighting behavior (1 item each wave): agr11_fight, agr14_fight, agr17_fight
# Ensure all are continuous (approx); if not, specify ordered= for categorical thresholds.

# Define the three-factor structure
executive_items <- c("task_completion", "distracted", "fidgeting", "think_act", "restless")
selfcent_items <- c("considerate", "sharing", "helpful", "volunteer_help")
temper_items <- c("temper", "obedient", "lying")

# Create variable lists for each wave
all_items <- c(executive_items, selfcent_items, temper_items)
sc11_vars <- paste0("sc11_", all_items)
sc14_vars <- paste0("sc14_", all_items)  
sc17_vars <- paste0("sc17_", all_items)

# Create analysis dataset including both self-control and fighting variables
analysis_vars <- c(sc11_vars, sc14_vars, sc17_vars, fight_available)
available_vars <- analysis_vars[analysis_vars %in% names(merged_data)]
dat <- merged_data[, available_vars, drop = FALSE]
dat$id <- 1:nrow(dat)

# Remove cases with no data
complete_check <- rowSums(!is.na(dat[, available_vars])) > 0
dat <- dat[complete_check, ]

cat("\nAnalysis dataset: N =", nrow(dat), "\n")
cat("Variables included:", length(available_vars), "/", length(analysis_vars), "\n")
cat("Self-control variables:", length(available_vars) - length(fight_available), "\n")
cat("Fighting variables:", length(fight_available), "\n\n")

# =============================================================================
# ### 2. Configural (baseline) longitudinal CFA for first-order factors only
# =============================================================================

cat("=== STEP 1: CONFIGURAL INVARIANCE ===\n")
# We start by not imposing equality constraints; scale each latent by fixing first loading = 1 (default).
# Later we will re-specify with labeled loadings for constraints.

cfa_configural <- '
# First-order factors (wave-specific)
Executive11 =~ sc11_task_completion + sc11_distracted + sc11_fidgeting + sc11_think_act + sc11_restless
Executive14 =~ sc14_task_completion + sc14_distracted + sc14_fidgeting + sc14_think_act + sc14_restless
Executive17 =~ sc17_task_completion + sc17_distracted + sc17_fidgeting + sc17_think_act + sc17_restless

SelfCent11  =~ sc11_considerate + sc11_sharing + sc11_helpful + sc11_volunteer_help
SelfCent14  =~ sc14_considerate + sc14_sharing + sc14_helpful + sc14_volunteer_help
SelfCent17  =~ sc17_considerate + sc17_sharing + sc17_helpful + sc17_volunteer_help

Temper11    =~ sc11_temper + sc11_obedient + sc11_lying
Temper14    =~ sc14_temper + sc14_obedient + sc14_lying
Temper17    =~ sc17_temper + sc17_obedient + sc17_lying
'

fit_cfa_configural <- cfa(cfa_configural, data = dat, meanstructure = TRUE, estimator = "MLR")

cat("Configural model fitted\n")
if(lavInspect(fit_cfa_configural, "converged")) {
  fit_config <- fitMeasures(fit_cfa_configural, c("chisq", "df", "pvalue", "cfi", "tli", "rmsea", "srmr"))
  cat("✓ Converged | CFI =", round(fit_config["cfi"], 3), "| RMSEA =", round(fit_config["rmsea"], 3), "\n")
} else {
  cat("❌ Failed to converge\n")
}

# Configural invariance establishes the same pattern of loadings across time.

# =============================================================================
# ### 3. Metric (weak) invariance: constrain corresponding loadings equal across waves
# =============================================================================

cat("\n=== STEP 2: METRIC INVARIANCE ===\n")
# Use labels (a1, a2, ...) repeated across waves within each first-order factor.

cfa_metric <- '
Executive11 =~ 1*sc11_task_completion + a1*sc11_distracted + a2*sc11_fidgeting + a3*sc11_think_act + a4*sc11_restless
Executive14 =~ 1*sc14_task_completion + a1*sc14_distracted + a2*sc14_fidgeting + a3*sc14_think_act + a4*sc14_restless
Executive17 =~ 1*sc17_task_completion + a1*sc17_distracted + a2*sc17_fidgeting + a3*sc17_think_act + a4*sc17_restless

SelfCent11  =~ 1*sc11_considerate + b1*sc11_sharing + b2*sc11_helpful + b3*sc11_volunteer_help
SelfCent14  =~ 1*sc14_considerate + b1*sc14_sharing + b2*sc14_helpful + b3*sc14_volunteer_help
SelfCent17  =~ 1*sc17_considerate + b1*sc17_sharing + b2*sc17_helpful + b3*sc17_volunteer_help

Temper11    =~ 1*sc11_temper + c1*sc11_obedient + c2*sc11_lying
Temper14    =~ 1*sc14_temper + c1*sc14_obedient + c2*sc14_lying
Temper17    =~ 1*sc17_temper + c1*sc17_obedient + c2*sc17_lying
'

fit_cfa_metric <- cfa(cfa_metric, data = dat, meanstructure = TRUE, estimator = "MLR")

cat("Metric model fitted\n")
if(lavInspect(fit_cfa_metric, "converged")) {
  fit_metric <- fitMeasures(fit_cfa_metric, c("chisq", "df", "pvalue", "cfi", "tli", "rmsea", "srmr"))
  cat("✓ Converged | CFI =", round(fit_metric["cfi"], 3), "| RMSEA =", round(fit_metric["rmsea"], 3), "\n")
  
  # Model comparison
  comparison_config_metric <- anova(fit_cfa_configural, fit_cfa_metric)
  delta_cfi <- fit_config["cfi"] - fit_metric["cfi"]
  delta_rmsea <- fit_metric["rmsea"] - fit_config["rmsea"]
  
  cat("Configural vs Metric: ΔCFI =", round(delta_cfi, 3), "| ΔRMSEA =", round(delta_rmsea, 3), "\n")
  if(delta_cfi < 0.01 && delta_rmsea < 0.015) {
    cat("✓ Metric invariance supported\n")
  } else {
    cat("⚠ Metric invariance questionable\n")
  }
} else {
  cat("❌ Failed to converge\n")
}

# Metric invariance constrains factor loadings, enabling comparison of (latent) variances and covariances
# and prerequisite for scalar tests.

# =============================================================================
# ### 4. Scalar (strong) invariance: add equal item intercepts
# =============================================================================

cat("\n=== STEP 3: SCALAR INVARIANCE ===\n")
# Add item intercept labels (i_ prefixes) mirroring indicator sets; loadings retained.

cfa_scalar <- '
# Loadings (same as metric)
Executive11 =~ 1*sc11_task_completion + a1*sc11_distracted + a2*sc11_fidgeting + a3*sc11_think_act + a4*sc11_restless
Executive14 =~ 1*sc14_task_completion + a1*sc14_distracted + a2*sc14_fidgeting + a3*sc14_think_act + a4*sc14_restless
Executive17 =~ 1*sc17_task_completion + a1*sc17_distracted + a2*sc17_fidgeting + a3*sc17_think_act + a4*sc17_restless
SelfCent11  =~ 1*sc11_considerate + b1*sc11_sharing + b2*sc11_helpful + b3*sc11_volunteer_help
SelfCent14  =~ 1*sc14_considerate + b1*sc14_sharing + b2*sc14_helpful + b3*sc14_volunteer_help
SelfCent17  =~ 1*sc17_considerate + b1*sc17_sharing + b2*sc17_helpful + b3*sc17_volunteer_help
Temper11    =~ 1*sc11_temper + c1*sc11_obedient + c2*sc11_lying
Temper14    =~ 1*sc14_temper + c1*sc14_obedient + c2*sc14_lying
Temper17    =~ 1*sc17_temper + c1*sc17_obedient + c2*sc17_lying

# Indicator intercepts (scalar invariance)
sc11_task_completion ~ i_tc*1; sc14_task_completion ~ i_tc*1; sc17_task_completion ~ i_tc*1
sc11_distracted ~ i_d1*1; sc14_distracted ~ i_d1*1; sc17_distracted ~ i_d1*1
sc11_fidgeting ~ i_f1*1; sc14_fidgeting ~ i_f1*1; sc17_fidgeting ~ i_f1*1
sc11_think_act ~ i_ta*1;  sc14_think_act ~ i_ta*1;  sc17_think_act ~ i_ta*1
sc11_restless ~ i_r1*1;   sc14_restless ~ i_r1*1;   sc17_restless ~ i_r1*1

sc11_considerate ~ i_con*1; sc14_considerate ~ i_con*1; sc17_considerate ~ i_con*1
sc11_sharing ~ i_sha*1;     sc14_sharing ~ i_sha*1;     sc17_sharing ~ i_sha*1
sc11_helpful ~ i_help*1;    sc14_helpful ~ i_help*1;    sc17_helpful ~ i_help*1
sc11_volunteer_help ~ i_vol*1; sc14_volunteer_help ~ i_vol*1; sc17_volunteer_help ~ i_vol*1

sc11_temper ~ i_temp*1; sc14_temper ~ i_temp*1; sc17_temper ~ i_temp*1
sc11_obedient ~ i_ob*1; sc14_obedient ~ i_ob*1; sc17_obedient ~ i_ob*1
sc11_lying ~ i_ly*1;    sc14_lying ~ i_ly*1;    sc17_lying ~ i_ly*1
'

fit_cfa_scalar <- cfa(cfa_scalar, data = dat, meanstructure = TRUE, estimator = "MLR")

cat("Scalar model fitted\n")
if(lavInspect(fit_cfa_scalar, "converged")) {
  fit_scalar <- fitMeasures(fit_cfa_scalar, c("chisq", "df", "pvalue", "cfi", "tli", "rmsea", "srmr"))
  cat("✓ Converged | CFI =", round(fit_scalar["cfi"], 3), "| RMSEA =", round(fit_scalar["rmsea"], 3), "\n")
  
  # Model comparison
  comparison_metric_scalar <- anova(fit_cfa_metric, fit_cfa_scalar)
  delta_cfi <- fit_metric["cfi"] - fit_scalar["cfi"]
  delta_rmsea <- fit_scalar["rmsea"] - fit_metric["rmsea"]
  
  cat("Metric vs Scalar: ΔCFI =", round(delta_cfi, 3), "| ΔRMSEA =", round(delta_rmsea, 3), "\n")
  if(delta_cfi < 0.01 && delta_rmsea < 0.015) {
    cat("✓ Scalar invariance supported\n")
  } else {
    cat("⚠ Scalar invariance questionable - consider partial invariance\n")
  }
} else {
  cat("❌ Failed to converge\n")
}

# Scalar invariance allows comparison of latent means/change; if rejected, move to partial invariance.

# =============================================================================
# ### 5. Add second-order (wave-specific) SelfControl factors with equal second-order loadings
# =============================================================================

cat("\n=== STEP 4: SECOND-ORDER STRUCTURE ===\n")
# Impose equality across time on second-order loadings (b_exec, b_selfcent, b_temp).

second_order_scalar <- '
# First-order metric + scalar constraints
Executive11 =~ 1*sc11_task_completion + a1*sc11_distracted + a2*sc11_fidgeting + a3*sc11_think_act + a4*sc11_restless
Executive14 =~ 1*sc14_task_completion + a1*sc14_distracted + a2*sc14_fidgeting + a3*sc14_think_act + a4*sc14_restless
Executive17 =~ 1*sc17_task_completion + a1*sc17_distracted + a2*sc17_fidgeting + a3*sc17_think_act + a4*sc17_restless
SelfCent11  =~ 1*sc11_considerate + b1*sc11_sharing + b2*sc11_helpful + b3*sc11_volunteer_help
SelfCent14  =~ 1*sc14_considerate + b1*sc14_sharing + b2*sc14_helpful + b3*sc14_volunteer_help
SelfCent17  =~ 1*sc17_considerate + b1*sc17_sharing + b2*sc17_helpful + b3*sc17_volunteer_help
Temper11    =~ 1*sc11_temper + c1*sc11_obedient + c2*sc11_lying
Temper14    =~ 1*sc14_temper + c1*sc14_obedient + c2*sc14_lying
Temper17    =~ 1*sc17_temper + c1*sc17_obedient + c2*sc17_lying

# Second-order (equal loadings over waves)
SelfControl11 =~ 1*Executive11 + b_self*SelfCent11 + b_temp*Temper11
SelfControl14 =~ 1*Executive14 + b_self*SelfCent14 + b_temp*Temper14
SelfControl17 =~ 1*Executive17 + b_self*SelfCent17 + b_temp*Temper17

# Indicator intercept constraints (from scalar model)
sc11_task_completion ~ i_tc*1; sc14_task_completion ~ i_tc*1; sc17_task_completion ~ i_tc*1
sc11_distracted ~ i_d1*1; sc14_distracted ~ i_d1*1; sc17_distracted ~ i_d1*1
sc11_fidgeting ~ i_f1*1; sc14_fidgeting ~ i_f1*1; sc17_fidgeting ~ i_f1*1
sc11_think_act ~ i_ta*1;  sc14_think_act ~ i_ta*1;  sc17_think_act ~ i_ta*1
sc11_restless ~ i_r1*1;   sc14_restless ~ i_r1*1;   sc17_restless ~ i_r1*1
sc11_considerate ~ i_con*1; sc14_considerate ~ i_con*1; sc17_considerate ~ i_con*1
sc11_sharing ~ i_sha*1;     sc14_sharing ~ i_sha*1;     sc17_sharing ~ i_sha*1
sc11_helpful ~ i_help*1;    sc14_helpful ~ i_help*1;    sc17_helpful ~ i_help*1
sc11_volunteer_help ~ i_vol*1; sc14_volunteer_help ~ i_vol*1; sc17_volunteer_help ~ i_vol*1
sc11_temper ~ i_temp*1; sc14_temper ~ i_temp*1; sc17_temper ~ i_temp*1
sc11_obedient ~ i_ob*1; sc14_obedient ~ i_ob*1; sc17_obedient ~ i_ob*1
sc11_lying ~ i_ly*1;    sc14_lying ~ i_ly*1;    sc17_lying ~ i_ly*1

# Identification: fix residual means of second-order factors implicitly at 0 by growth factors later
'

fit_second_scalar <- cfa(second_order_scalar, data = dat, meanstructure = TRUE, estimator = "MLR")

cat("Second-order model fitted\n")
if(lavInspect(fit_second_scalar, "converged")) {
  fit_second <- fitMeasures(fit_second_scalar, c("chisq", "df", "pvalue", "cfi", "tli", "rmsea", "srmr"))
  cat("✓ Converged | CFI =", round(fit_second["cfi"], 3), "| RMSEA =", round(fit_second["rmsea"], 3), "\n")
  
  # Second-order loadings
  second_params <- standardizedSolution(fit_second_scalar)
  second_loadings <- second_params[second_params$op == "=~" & grepl("SelfControl", second_params$lhs), ]
  cat("Second-order loadings (standardized):\n")
  for(i in 1:nrow(second_loadings)) {
    cat("  ", second_loadings$lhs[i], "->", second_loadings$rhs[i], ":", 
        round(second_loadings$est.std[i], 3), "\n")
  }
} else {
  cat("❌ Failed to converge\n")
}

# Equal second-order loadings ensure that higher-order construct meaning is time-invariant,
# necessary before imposing growth factors atop second-order factors.

# =============================================================================
# ### 6. Add growth factors (intercept & linear slope) over second-order factors
# =============================================================================

cat("\n=== STEP 5: CURVE-OF-FACTORS GROWTH MODEL ===\n")
# Set time scores 0,1,2 for slope; fix wave-specific second-order factor means to 0 so growth means capture them;
# free variances of growth factors and their covariance.

curve_of_factors <- paste0(second_order_scalar, '

# Growth (curve-of-factors) specification
iSC =~ 1*SelfControl11 + 1*SelfControl14 + 1*SelfControl17
sSC =~ 0*SelfControl11 + 1*SelfControl14 + 2*SelfControl17

# Constrain wave-specific second-order latent means to 0 for identification
SelfControl11 ~ 0*1
SelfControl14 ~ 0*1
SelfControl17 ~ 0*1

# Estimate growth means
iSC ~ 1
sSC ~ 1

# Optional: Allow residual covariances among same indicators across waves
sc11_task_completion ~~ sc14_task_completion + sc17_task_completion
sc14_task_completion ~~ sc17_task_completion
sc11_distracted ~~ sc14_distracted + sc17_distracted
sc14_distracted ~~ sc17_distracted
sc11_fidgeting ~~ sc14_fidgeting + sc17_fidgeting
sc14_fidgeting ~~ sc17_fidgeting
sc11_think_act ~~ sc14_think_act + sc17_think_act
sc14_think_act ~~ sc17_think_act
sc11_restless ~~ sc14_restless + sc17_restless
sc14_restless ~~ sc17_restless

sc11_considerate ~~ sc14_considerate + sc17_considerate
sc14_considerate ~~ sc17_considerate
sc11_sharing ~~ sc14_sharing + sc17_sharing
sc14_sharing ~~ sc17_sharing
sc11_helpful ~~ sc14_helpful + sc17_helpful
sc14_helpful ~~ sc17_helpful
sc11_volunteer_help ~~ sc14_volunteer_help + sc17_volunteer_help
sc14_volunteer_help ~~ sc17_volunteer_help

sc11_temper ~~ sc14_temper + sc17_temper
sc14_temper ~~ sc17_temper
sc11_obedient ~~ sc14_obedient + sc17_obedient
sc14_obedient ~~ sc17_obedient
sc11_lying ~~ sc14_lying + sc17_lying
sc14_lying ~~ sc17_lying

# Optional: Correlated disturbances among same first-order factors across waves
Executive11 ~~ Executive14 + Executive17
Executive14 ~~ Executive17
SelfCent11 ~~ SelfCent14 + SelfCent17
SelfCent14 ~~ SelfCent17
Temper11 ~~ Temper14 + Temper17
Temper14 ~~ Temper17
')

fit_curve <- sem(curve_of_factors, data = dat, meanstructure = TRUE, estimator = "MLR")

cat("Curve-of-factors model fitted\n")
if(lavInspect(fit_curve, "converged")) {
  fit_growth <- fitMeasures(fit_curve, c("chisq", "df", "pvalue", "cfi", "tli", "rmsea", "srmr", "aic", "bic"))
  cat("✓ Converged | CFI =", round(fit_growth["cfi"], 3), "| RMSEA =", round(fit_growth["rmsea"], 3), "\n")
  
  # Growth parameters
  growth_params <- parameterEstimates(fit_curve)
  intercept_mean <- growth_params[growth_params$lhs == "iSC" & growth_params$op == "~1", ]
  slope_mean <- growth_params[growth_params$lhs == "sSC" & growth_params$op == "~1", ]
  
  cat("\n=== SELF-CONTROL GROWTH PARAMETERS ===\n")
  if(nrow(intercept_mean) > 0) {
    cat("Intercept mean =", round(intercept_mean$est, 3), 
        "(SE =", round(intercept_mean$se, 3), ", p =", round(intercept_mean$pvalue, 3), ")\n")
  }
  if(nrow(slope_mean) > 0) {
    cat("Slope mean =", round(slope_mean$est, 3), 
        "(SE =", round(slope_mean$se, 3), ", p =", round(slope_mean$pvalue, 3), ")\n")
    if(!is.na(slope_mean$pvalue) && slope_mean$pvalue < 0.05) {
      if(slope_mean$est > 0) {
        cat("→ Self-control problems INCREASE over time\n")
      } else {
        cat("→ Self-control problems DECREASE over time (improvement)\n")
      }
    } else {
      cat("→ No significant linear change in self-control\n")
    }
  }
  
} else {
  cat("❌ Failed to converge\n")
}

# This yields intercept (baseline level) and linear change in the common self-control construct,
# controlling for indicator-specific stability via correlated uniquenesses when included.

# =============================================================================
# ### 7. PARALLEL PROCESS MODEL: ADD FIGHTING BEHAVIOR GROWTH CURVE
# =============================================================================

cat("\n=== STEP 6: PARALLEL PROCESS WITH FIGHTING BEHAVIOR ===\n")

# Check which fighting variables are available
available_fight_vars <- fight_available[fight_available %in% names(dat)]

if(length(available_fight_vars) >= 2) {
  cat("Adding fighting behavior growth curve with", length(available_fight_vars), "time points\n")
  
  # Create parallel process syntax
  parallel_process_syntax <- paste0(curve_of_factors, '

# Fighting behavior growth curve (parallel process)
iFight =~ 1*agr11_fight + 1*agr14_fight + 1*agr17_fight
sFight =~ 0*agr11_fight + 1*agr14_fight + 2*agr17_fight

# Fighting growth means
iFight ~ 1
sFight ~ 1

# CROSS-CONSTRUCT CORRELATIONS (key feature of parallel process models)
# Correlate intercepts (baseline levels)
iSC ~~ iFight

# Correlate slopes (rates of change) 
sSC ~~ sFight

# Correlate intercept of one with slope of other (developmental coupling)
iSC ~~ sFight
iFight ~~ sSC

# Optional: Cross-lagged effects for stronger coupling
# Uncomment these for cross-lagged parallel process model:
# SelfControl11 ~ iFight  # Baseline fighting predicts initial self-control
# agr14_fight ~ iSC       # Baseline self-control predicts fighting at age 14
')

  # Fit parallel process model
  fit_parallel <- sem(parallel_process_syntax, data = dat, meanstructure = TRUE, estimator = "MLR")
  
  cat("Parallel process model fitted\n")
  if(lavInspect(fit_parallel, "converged")) {
    fit_parallel_measures <- fitMeasures(fit_parallel, c("chisq", "df", "pvalue", "cfi", "tli", "rmsea", "srmr", "aic", "bic"))
    cat("✓ Converged | CFI =", round(fit_parallel_measures["cfi"], 3), "| RMSEA =", round(fit_parallel_measures["rmsea"], 3), "\n")
    
    # Extract parallel process parameters
    parallel_params <- parameterEstimates(fit_parallel)
    
    # Fighting growth parameters
    fight_intercept <- parallel_params[parallel_params$lhs == "iFight" & parallel_params$op == "~1", ]
    fight_slope <- parallel_params[parallel_params$lhs == "sFight" & parallel_params$op == "~1", ]
    
    cat("\n=== FIGHTING BEHAVIOR GROWTH PARAMETERS ===\n")
    if(nrow(fight_intercept) > 0) {
      cat("Fighting intercept mean =", round(fight_intercept$est, 3), 
          "(SE =", round(fight_intercept$se, 3), ", p =", round(fight_intercept$pvalue, 3), ")\n")
    }
    if(nrow(fight_slope) > 0) {
      cat("Fighting slope mean =", round(fight_slope$est, 3), 
          "(SE =", round(fight_slope$se, 3), ", p =", round(fight_slope$pvalue, 3), ")\n")
      if(!is.na(fight_slope$pvalue) && fight_slope$pvalue < 0.05) {
        if(fight_slope$est > 0) {
          cat("→ Fighting behavior INCREASES over time\n")
        } else {
          cat("→ Fighting behavior DECREASES over time\n")
        }
      } else {
        cat("→ No significant linear change in fighting behavior\n")
      }
    }
    
    # Cross-construct correlations
    cat("\n=== CROSS-CONSTRUCT CORRELATIONS ===\n")
    
    # Intercept correlations
    ic_corr <- parallel_params[parallel_params$lhs == "iSC" & parallel_params$op == "~~" & parallel_params$rhs == "iFight", ]
    if(nrow(ic_corr) > 0) {
      cat("Intercept correlation (baseline levels): r =", round(ic_corr$est, 3), 
          "(p =", round(ic_corr$pvalue, 3), ")\n")
      if(!is.na(ic_corr$pvalue) && ic_corr$pvalue < 0.05) {
        if(ic_corr$est > 0) {
          cat("→ Higher self-control problems associated with MORE fighting at baseline\n")
        } else {
          cat("→ Higher self-control problems associated with LESS fighting at baseline\n")
        }
      }
    }
    
    # Slope correlations  
    sc_corr <- parallel_params[parallel_params$lhs == "sSC" & parallel_params$op == "~~" & parallel_params$rhs == "sFight", ]
    if(nrow(sc_corr) > 0) {
      cat("Slope correlation (change rates): r =", round(sc_corr$est, 3), 
          "(p =", round(sc_corr$pvalue, 3), ")\n")
      if(!is.na(sc_corr$pvalue) && sc_corr$pvalue < 0.05) {
        if(sc_corr$est > 0) {
          cat("→ Increases in self-control problems coupled with INCREASES in fighting\n")
        } else {
          cat("→ Increases in self-control problems coupled with DECREASES in fighting\n")
        }
      }
    }
    
    # Cross-lagged correlations
    cross1 <- parallel_params[parallel_params$lhs == "iSC" & parallel_params$op == "~~" & parallel_params$rhs == "sFight", ]
    if(nrow(cross1) > 0) {
      cat("Cross-correlation (SC intercept - Fight slope): r =", round(cross1$est, 3), 
          "(p =", round(cross1$pvalue, 3), ")\n")
    }
    
    cross2 <- parallel_params[parallel_params$lhs == "iFight" & parallel_params$op == "~~" & parallel_params$rhs == "sSC", ]
    if(nrow(cross2) > 0) {
      cat("Cross-correlation (Fight intercept - SC slope): r =", round(cross2$est, 3), 
          "(p =", round(cross2$pvalue, 3), ")\n")
    }
    
    # Model fit comparison
    cat("\n=== PARALLEL PROCESS MODEL FIT ===\n")
    cat("χ² =", round(fit_parallel_measures["chisq"], 2), ", df =", fit_parallel_measures["df"], ", p =", round(fit_parallel_measures["pvalue"], 3), "\n")
    cat("CFI =", round(fit_parallel_measures["cfi"], 3), ", TLI =", round(fit_parallel_measures["tli"], 3), "\n")
    cat("RMSEA =", round(fit_parallel_measures["rmsea"], 3), ", SRMR =", round(fit_parallel_measures["srmr"], 3), "\n")
    cat("AIC =", round(fit_parallel_measures["aic"], 0), ", BIC =", round(fit_parallel_measures["bic"], 0), "\n")
    
    # Compare models
    if(exists("fit_growth")) {
      cat("\nModel comparison (Self-Control Only vs Parallel Process):\n")
      cat("ΔAIC =", round(fit_parallel_measures["aic"] - fit_growth["aic"], 2), "\n")
      cat("ΔBIC =", round(fit_parallel_measures["bic"] - fit_growth["bic"], 2), "\n")
      
      if(fit_parallel_measures["aic"] < fit_growth["aic"]) {
        cat("→ Parallel process model preferred by AIC\n")
      } else {
        cat("→ Self-control only model preferred by AIC\n")
      }
    }
    
  } else {
    cat("❌ Parallel process model failed to converge\n")
    cat("Consider simplified models or different estimation approach\n")
  }
  
} else {
  cat("❌ Insufficient fighting variables (", length(available_fight_vars), ") for growth modeling\n")
  cat("Need at least 2 time points for growth curve\n")
}

summary(fit_parallel, fit.measures = TRUE, standardized = TRUE)

# =============================================================================
# ### 8. OPTIONAL EXTENSIONS FOR PARALLEL PROCESS
# =============================================================================

cat("\n=== OPTIONAL PARALLEL PROCESS EXTENSIONS ===\n")

# ### 8a. Cross-Lagged Parallel Process
cat("Cross-lagged effects can be added to model:\n")
cat("- SelfControl11 ~ iFight  # Baseline fighting → initial self-control\n")
cat("- agr14_fight ~ iSC       # Baseline self-control → fighting at age 14\n")
cat("- SelfControl14 ~ agr11_fight  # Age 11 fighting → age 14 self-control\n")

# ### 8b. Conditional Parallel Process (with covariates)
cat("\nConditional models can include:\n")
cat("- iSC ~ gender + ses      # Predictors of initial self-control\n") 
cat("- iFight ~ gender + ses   # Predictors of initial fighting\n")
cat("- sSC ~ gender + ses      # Predictors of self-control change\n")
cat("- sFight ~ gender + ses   # Predictors of fighting change\n")

# ### 8c. Nonlinear Growth in Parallel Process
nonlinear_parallel_syntax <- '
# Latent basis models for both constructs
iSC =~ 1*SelfControl11 + 1*SelfControl14 + 1*SelfControl17
sSC =~ 0*SelfControl11 + 1*SelfControl14 + lambda_sc*SelfControl17

iFight =~ 1*agr11_fight + 1*agr14_fight + 1*agr17_fight  
sFight =~ 0*agr11_fight + 1*agr14_fight + lambda_fight*agr17_fight

# Cross-construct correlations remain the same
'

cat("\nNonlinear growth (latent basis) estimates lambda parameters for curvature\n")

# ### 8d. Alternative Time Coding
cat("\nAlternative time codings:\n")
cat("- Ages as actual years: 0*Age11 + 3*Age14 + 6*Age17 (3-year intervals)\n")
cat("- Centered on Age 14: -1*Age11 + 0*Age14 + 1*Age17 (effect coding)\n")

# =============================================================================
# ### 9. MODEL SUMMARY AND INTERPRETATION
# =============================================================================

cat("\n=== COMPREHENSIVE PARALLEL PROCESS SUMMARY ===\n")

# Create comparison table of all fitted models
model_names <- c("Configural", "Metric", "Scalar", "Second-Order", "Self-Control Growth")
model_converged <- c(
  ifelse(exists("fit_cfa_configural") && lavInspect(fit_cfa_configural, "converged"), "Yes", "No"),
  ifelse(exists("fit_cfa_metric") && lavInspect(fit_cfa_metric, "converged"), "Yes", "No"),
  ifelse(exists("fit_cfa_scalar") && lavInspect(fit_cfa_scalar, "converged"), "Yes", "No"),
  ifelse(exists("fit_second_scalar") && lavInspect(fit_second_scalar, "converged"), "Yes", "No"),
  ifelse(exists("fit_curve") && lavInspect(fit_curve, "converged"), "Yes", "No")
)
model_cfi <- c(
  ifelse(exists("fit_config"), round(fit_config["cfi"], 3), NA),
  ifelse(exists("fit_metric"), round(fit_metric["cfi"], 3), NA),
  ifelse(exists("fit_scalar"), round(fit_scalar["cfi"], 3), NA),
  ifelse(exists("fit_second"), round(fit_second["cfi"], 3), NA),
  ifelse(exists("fit_growth"), round(fit_growth["cfi"], 3), NA)
)
model_rmsea <- c(
  ifelse(exists("fit_config"), round(fit_config["rmsea"], 3), NA),
  ifelse(exists("fit_metric"), round(fit_metric["rmsea"], 3), NA),
  ifelse(exists("fit_scalar"), round(fit_scalar["rmsea"], 3), NA),
  ifelse(exists("fit_second"), round(fit_second["rmsea"], 3), NA),
  ifelse(exists("fit_growth"), round(fit_growth["rmsea"], 3), NA)
)

# Add parallel process if available
if(exists("fit_parallel") && lavInspect(fit_parallel, "converged")) {
  model_names <- c(model_names, "Parallel Process")
  model_converged <- c(model_converged, "Yes")
  model_cfi <- c(model_cfi, round(fit_parallel_measures["cfi"], 3))
  model_rmsea <- c(model_rmsea, round(fit_parallel_measures["rmsea"], 3))
}

model_comparison <- data.frame(
  Model = model_names,
  Converged = model_converged,
  CFI = model_cfi,
  RMSEA = model_rmsea
)

print(model_comparison)

cat("\n=== THEORETICAL INTERPRETATION ===\n")
cat("This comprehensive parallel process model provides:\n")
cat("1. Measurement invariance for self-control across time (configural → metric → scalar)\n")
cat("2. Second-order factor structure validation for self-control\n")
cat("3. Growth trajectories for both self-control and fighting behavior\n")
cat("4. Cross-construct correlations revealing developmental coupling\n")
cat("5. Individual differences in baseline levels and change rates for both constructs\n")
cat("6. Control for indicator-specific effects via residual correlations\n\n")

cat("=== PARALLEL PROCESS INTERPRETATION GUIDE ===\n")
cat("• Intercept correlation: Association between baseline levels\n")
cat("• Slope correlation: Coupling of developmental trajectories\n") 
cat("• Cross-correlations: How initial levels predict rates of change\n")
cat("• Positive correlations: Constructs develop in same direction\n")
cat("• Negative correlations: Compensatory or antagonistic development\n\n")

cat("=== NEXT STEPS ===\n")
cat("1. Test nonlinear growth functions (quadratic, latent basis)\n")
cat("2. Add cross-lagged effects for stronger developmental coupling\n")
cat("3. Include time-varying or time-invariant predictors\n")
cat("4. Test moderation by gender, SES, or other grouping variables\n")
cat("5. Examine modification indices for model improvement\n")
cat("6. Validate findings with alternative model specifications\n\n")

cat("=== COMPREHENSIVE PARALLEL PROCESS ANALYSIS COMPLETE ===\n")

# Save models for further analysis
if(exists("fit_parallel") && lavInspect(fit_parallel, "converged")) {
  cat("Final parallel process model saved as 'fit_parallel'\n")
  cat("Self-control only model saved as 'fit_curve'\n")
  cat("Use summary(fit_parallel, fit.measures = TRUE, standardized = TRUE) for detailed output\n")
} else if(exists("fit_curve") && lavInspect(fit_curve, "converged")) {
  cat("Self-control growth model saved as 'fit_curve'\n")
  cat("Use summary(fit_curve, fit.measures = TRUE, standardized = TRUE) for detailed output\n")
} 
 