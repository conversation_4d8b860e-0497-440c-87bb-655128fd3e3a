# =============================================================================
# TEST: CORRECTED IDENTIFICATION WITH EQUAL RESIDUAL VARIANCES
# Verify the proper identification strategy produces meaningful growth parameters
# =============================================================================

library(pacman)
p_load(lavaan, dplyr)

cat("=== TESTING CORRECTED IDENTIFICATION APPROACH ===\n")

# Load data
data_path <- "/home/<USER>/dissertation_folder/data"
if(file.exists(file.path(data_path, "merged1203.rds"))) {
  merged_data <- readRDS(file.path(data_path, "merged1203.rds"))
  cat("Data loaded successfully\n")
  
  # Run recoding if needed
  if(!"sc11_task_completion" %in% names(merged_data)) {
    cat("Running recoding scripts...\n")
    source("scripts/recode_self_control.R")
    source("scripts/recode_self_control_age14.R") 
    source("scripts/recode_self_control_age17.R")
  }
  
  # Test with Executive Control (3 items for speed)
  executive_items <- c("task_completion", "distracted", "think_act")
  exec_vars <- c(paste0("sc11_", executive_items),
                 paste0("sc14_", executive_items), 
                 paste0("sc17_", executive_items))
  
  test_data <- merged_data[, exec_vars, drop = FALSE]
  complete_rows <- rowSums(!is.na(test_data)) > 0
  test_data <- test_data[complete_rows, ]
  
  cat("Test sample size:", nrow(test_data), "\n")
  
  # CORRECTED Model with proper identification
  corrected_model <- '
  # First level: Executive factors
  Executive11 =~ sc11_task_completion + sc11_distracted + sc11_think_act
  Executive14 =~ sc14_task_completion + sc14_distracted + sc14_think_act
  Executive17 =~ sc17_task_completion + sc17_distracted + sc17_think_act
  
  # Second level: Growth factors  
  iExecutive =~ 1*Executive11 + 1*Executive14 + 1*Executive17
  sExecutive =~ 0*Executive11 + 1*Executive14 + 2*Executive17
  
  # === CONSTRAINTS FOR IDENTIFICATION ===
  # Fix residual variances of first-order factors to be equal
  Executive11 ~~ resvar*Executive11
  Executive14 ~~ resvar*Executive14
  Executive17 ~~ resvar*Executive17
  
  # Intercepts of first-order factors fixed to 0
  Executive11 ~ 0*1
  Executive14 ~ 0*1
  Executive17 ~ 0*1
  
  # Free means of growth factors (THIS IS KEY!)
  iExecutive ~ 1
  sExecutive ~ 1
  '
  
  cat("\nFitting model with corrected identification...\n")
  fit_corrected <- tryCatch({
    sem(
      model = corrected_model,
      data = test_data,
      estimator = "WLSMV",
      ordered = exec_vars,
      missing = "pairwise",
      std.lv = FALSE,  # Important: Use FALSE, not TRUE
      meanstructure = TRUE
    )
  }, error = function(e) {
    cat("Error:", e$message, "\n")
    return(NULL)
  })
  
  if(!is.null(fit_corrected)) {
    converged <- lavInspect(fit_corrected, "converged")
    cat("Model converged:", converged, "\n")
    
    if(converged) {
      cat("✅ Corrected model converged successfully!\n")
      
      # Check for negative variances first
      param_table <- parameterEstimates(fit_corrected)
      variances <- param_table[param_table$op == "~~" & param_table$lhs == param_table$rhs, ]
      negative_vars <- variances[variances$est < 0, ]
      
      if(nrow(negative_vars) > 0) {
        cat("⚠️ WARNING: Negative variances still detected:\n")
        for(i in 1:nrow(negative_vars)) {
          cat("  ", negative_vars$lhs[i], ":", round(negative_vars$est[i], 4), "\n")
        }
      } else {
        cat("✅ No negative variances (Heywood cases avoided)!\n")
      }
      
      # Check growth parameters - THE CRITICAL TEST
      intercept_mean <- param_table[param_table$lhs == "iExecutive" & param_table$op == "~1", ]
      slope_mean <- param_table[param_table$lhs == "sExecutive" & param_table$op == "~1", ]
      
      cat("\n🎯 GROWTH PARAMETER RESULTS:\n")
      if(nrow(intercept_mean) > 0) {
        cat("INTERCEPT MEAN:\n")
        cat("  Estimate:", round(intercept_mean$est, 4))
        if(!is.na(intercept_mean$se)) {
          cat(" (SE =", round(intercept_mean$se, 4), ")")
        }
        if(!is.na(intercept_mean$pvalue)) {
          cat(", p =", round(intercept_mean$pvalue, 4))
          if(intercept_mean$est != 0 && !is.na(intercept_mean$pvalue)) {
            cat(" ✅ NON-ZERO with VALID p-value!")
          } else {
            cat(" ❌ Still zero or invalid p-value")
          }
        } else {
          cat(", p = NA ❌ STILL PROBLEMATIC")
        }
        cat("\n")
      }
      
      if(nrow(slope_mean) > 0) {
        cat("SLOPE MEAN:\n")
        cat("  Estimate:", round(slope_mean$est, 4))
        if(!is.na(slope_mean$se)) {
          cat(" (SE =", round(slope_mean$se, 4), ")")
        }
        if(!is.na(slope_mean$pvalue)) {
          cat(", p =", round(slope_mean$pvalue, 4))
          if(slope_mean$est != 0 && !is.na(slope_mean$pvalue)) {
            cat(" ✅ NON-ZERO with VALID p-value!")
            if(slope_mean$pvalue < 0.05) {
              if(slope_mean$est > 0) {
                cat("\n  → SIGNIFICANT INCREASE over time! 🎉")
              } else {
                cat("\n  → SIGNIFICANT DECREASE over time! 🎉")
              }
            } else {
              cat("\n  → No significant change detected")
            }
          } else {
            cat(" ❌ Still zero or invalid p-value")
          }
        } else {
          cat(", p = NA ❌ STILL PROBLEMATIC")
        }
        cat("\n")
      }
      
      # Check residual variance constraint
      resvar_estimates <- param_table[param_table$label == "resvar", ]
      if(nrow(resvar_estimates) > 0) {
        cat("\nRESIDUAL VARIANCE (equal constraint):\n")
        cat("  resvar =", round(resvar_estimates$est[1], 4), "\n")
        if(resvar_estimates$est[1] > 0) {
          cat("  ✅ Positive residual variance\n")
        } else {
          cat("  ❌ Negative residual variance\n")
        }
      }
      
      # Model fit
      fit_measures <- fitMeasures(fit_corrected, c("cfi.scaled", "tli.scaled", "rmsea.scaled", "srmr"))
      cat("\n📈 MODEL FIT:\n")
      cat("CFI =", round(fit_measures["cfi.scaled"], 3))
      if(fit_measures["cfi.scaled"] >= 0.95) {
        cat(" ✅ EXCELLENT")
      } else if(fit_measures["cfi.scaled"] >= 0.90) {
        cat(" ✅ ACCEPTABLE")
      } else {
        cat(" ❌ POOR")
      }
      cat("\n")
      
      cat("TLI =", round(fit_measures["tli.scaled"], 3), "\n")
      cat("RMSEA =", round(fit_measures["rmsea.scaled"], 3))
      if(fit_measures["rmsea.scaled"] <= 0.06) {
        cat(" ✅ EXCELLENT")
      } else if(fit_measures["rmsea.scaled"] <= 0.08) {
        cat(" ✅ ACCEPTABLE")
      } else {
        cat(" ❌ POOR")
      }
      cat("\n")
      cat("SRMR =", round(fit_measures["srmr"], 3), "\n")
      
      # Overall assessment
      growth_meaningful <- (!is.na(intercept_mean$pvalue) && !is.na(slope_mean$pvalue) && 
                           intercept_mean$est != 0 && slope_mean$est != 0)
      
      if(growth_meaningful && nrow(negative_vars) == 0) {
        cat("\n🎉 SUCCESS: Corrected identification approach WORKING!\n")
        cat("✅ Meaningful growth parameters with valid p-values\n")
        cat("✅ No negative variances (Heywood cases)\n")
        cat("✅ Ready for hypothesis testing!\n")
      } else {
        cat("\n⚠️ PARTIAL SUCCESS: Some issues remain\n")
        if(!growth_meaningful) {
          cat("❌ Growth parameters still problematic\n")
        }
        if(nrow(negative_vars) > 0) {
          cat("❌ Negative variances present\n")
        }
      }
      
    } else {
      cat("❌ Model did not converge\n")
      
      # Check warnings
      warnings <- lavInspect(fit_corrected, "post.check")
      if(length(warnings) > 0) {
        cat("Convergence warnings:\n")
        print(warnings)
      }
    }
    
  } else {
    cat("❌ Model fitting failed completely\n")
  }
  
} else {
  cat("❌ Data file not found\n")
}

cat("\n=== CORRECTED IDENTIFICATION TEST COMPLETE ===\n") 