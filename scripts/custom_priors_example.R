# Custom Priors for Bayesian LGC Model
# This script shows how to specify custom priors in blavaan
#################

library(blavaan)
library(lavaan)

# Example of how to specify custom priors using blavaan syntax
# Method 1: Using dpriors() with correct parameter types

# Default priors that b<PERSON><PERSON> uses:
default_priors <- dpriors(
  # Factor loadings (lambda parameters)
  lambda = "normal(0, 10)",
  
  # Intercepts (nu parameters) 
  nu = "normal(0, 10)",
  
  # Factor means (alpha parameters)
  alpha = "normal(0, 10)",
  
  # Factor variances and covariances (psi parameters)
  psi = "gamma(1, 0.5)",
  
  # Residual variances (theta parameters)
  theta = "gamma(1, 0.5)"
)

# Method 2: More informative custom priors
custom_priors <- dpriors(
  # Factor loadings: more informative for positive loadings
  lambda = "normal(0.7, 0.5)",
  
  # Intercepts: centered around small positive values
  nu = "normal(0.2, 0.3)",
  
  # Factor means: weakly informative
  alpha = "normal(0, 2)",
  
  # Variances: slightly more informative
  psi = "gamma(2, 1)",
  theta = "gamma(2, 1)"
)

# Method 3: Specifying priors directly in model syntax (advanced)
# You can add prior() specifications directly to parameters
model_with_priors <- '
# Measurement model with priors on specific parameters
SC3 =~ 1*sc3_task_completion + 
       prior("normal(0.7, 0.3)")*sc3_distracted + 
       prior("normal(0.7, 0.3)")*sc3_fidgeting +
       prior("normal(0.7, 0.3)")*sc3_think_act + 
       prior("normal(0.7, 0.3)")*sc3_restless + 
       prior("normal(0.7, 0.3)")*sc3_temper +
       prior("normal(0.7, 0.3)")*sc3_obedient + 
       prior("normal(0.7, 0.3)")*sc3_lying

# Growth factors with priors on means
INTERCEPT =~ 1*SC3 + 1*SC5 + 1*SC7 + 1*SC11 + 1*SC14 + 1*SC17
SLOPE =~ 0*SC3 + 2*SC5 + 4*SC7 + 8*SC11 + 11*SC14 + 14*SC17

# Priors on growth factor means
INTERCEPT ~ prior("normal(1.0, 0.5)")*1
SLOPE ~ prior("normal(-0.02, 0.02)")*1

# Priors on variances
INTERCEPT ~~ prior("gamma(2, 1)")*INTERCEPT
SLOPE ~~ prior("gamma(1, 10)")*SLOPE
'

cat("=== PRIOR SPECIFICATION OPTIONS ===\n")
cat("1. Default priors: Use bsem() without dp argument\n")
cat("2. Custom priors: Use dpriors() with dp argument\n") 
cat("3. Specific priors: Use prior() in model syntax\n\n")

cat("=== RECOMMENDED APPROACH ===\n")
cat("For your self-control LGC model, I recommend starting with\n")
cat("the default priors, as they are:\n")
cat("- Weakly informative (don't dominate the data)\n")
cat("- Computationally stable\n") 
cat("- Appropriate for most SEM applications\n\n")

cat("If you want more control, you can use custom_priors above\n")
cat("or specify priors on individual parameters using prior().\n")
