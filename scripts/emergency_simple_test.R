# =============================================================================
# EMERGENCY SIMPLE MODEL TESTING 
# Use this if the main script gets stuck in infinite loops
# =============================================================================

library(lavaan)

# Load data
cat("Loading data...\n")
data_path <- "/home/<USER>/dissertation_folder/data"

# Try to load dataset with recoded variables first
if(file.exists(file.path(data_path, "merged1203_with_recoded.rds"))) {
  merged_data <- readRDS(file.path(data_path, "merged1203_with_recoded.rds"))
  cat("Loaded dataset with recoded variables. Dimensions:", dim(merged_data), "\n")
} else {
  merged_data <- readRDS(file.path(data_path, "merged1203.rds"))
  cat("Loaded original dataset. Dimensions:", dim(merged_data), "\n")
  cat("Note: You may need to run scripts/quick_setup.R first to create recoded variables\n")
}

# Quick function to test if basic models work
test_simple_model <- function(age, age_vars) {
  cat("\n=== TESTING AGE", age, "SIMPLE MODEL ===\n")
  
  # Get data
  test_data <- merged_data[, age_vars, drop = FALSE]
  complete_data <- na.omit(test_data)
  
  cat("Sample size:", nrow(complete_data), "\n")
  
  if(nrow(complete_data) < 50) {
    cat("❌ INSUFFICIENT DATA\n")
    return(FALSE)
  }
  
  # Test 1: Very simple 1-factor model with ML estimator
  cat("Testing simple 1-factor model...\n")
  simple_model <- paste0("f1 =~ ", paste(age_vars, collapse = " + "))
  
  fit1 <- tryCatch({
    cfa(simple_model, data = complete_data, estimator = "ML")
  }, error = function(e) {
    cat("1-factor ML failed:", e$message, "\n")
    return(NULL)
  })
  
  if(!is.null(fit1)) {
    converged1 <- lavInspect(fit1, "converged")
    cat("1-factor ML converged:", converged1, "\n")
    if(converged1) {
      fits1 <- fitMeasures(fit1, c("cfi", "rmsea"))
      cat("CFI:", round(fits1["cfi"], 3), "RMSEA:", round(fits1["rmsea"], 3), "\n")
    }
  }
  
  # Test 2: Simple 1-factor with WLSMV (if ML worked)
  if(!is.null(fit1)) {
    cat("Testing WLSMV estimator...\n")
    fit2 <- tryCatch({
      cfa(simple_model, data = complete_data, estimator = "WLSMV", 
          ordered = age_vars)
    }, error = function(e) {
      cat("WLSMV failed:", e$message, "\n")
      return(NULL)
    })
    
    if(!is.null(fit2)) {
      converged2 <- lavInspect(fit2, "converged")
      cat("1-factor WLSMV converged:", converged2, "\n")
    }
  }
  
  return(TRUE)
}

# Test each wave
cat("=== EMERGENCY SIMPLE TESTING ===\n")
cat("Testing basic model convergence...\n\n")

# Define variables (should match your main script)
sc11_vars <- paste0("sc11_", c("think_act", "considerate", "sharing", "helpful", 
                               "volunteer_help", "task_completion", "obedient", 
                               "distracted", "temper", "restless", "fidgeting", "lying"))

sc14_vars <- paste0("sc14_", c("think_act", "considerate", "sharing", "helpful", 
                               "volunteer_help", "task_completion", "obedient", 
                               "distracted", "temper", "restless", "fidgeting", "lying"))

sc17_vars <- paste0("sc17_", c("think_act", "considerate", "sharing", "helpful", 
                               "volunteer_help", "task_completion", "obedient", 
                               "distracted", "temper", "restless", "fidgeting", "lying"))

# Filter to available variables
sc11_available <- sc11_vars[sc11_vars %in% names(merged_data)]
sc14_available <- sc14_vars[sc14_vars %in% names(merged_data)]
sc17_available <- sc17_vars[sc17_vars %in% names(merged_data)]

cat("Available variables:\n")
cat("Age 11:", length(sc11_available), "of", length(sc11_vars), "\n")
cat("Age 14:", length(sc14_available), "of", length(sc14_vars), "\n") 
cat("Age 17:", length(sc17_available), "of", length(sc17_vars), "\n\n")

# Test each wave
result11 <- test_simple_model("11", sc11_available)
result14 <- test_simple_model("14", sc14_available)
result17 <- test_simple_model("17", sc17_available)

cat("\n=== EMERGENCY TESTING COMPLETE ===\n")
cat("Results: Age 11 =", result11, ", Age 14 =", result14, ", Age 17 =", result17, "\n")

if(all(c(result11, result14, result17))) {
  cat("✓ Basic models work - data quality is OK\n")
  cat("The issue is with the complex bifactor model specification\n")
  cat("Recommendation: Use simpler models or different approach\n")
} else {
  cat("❌ Even simple models have issues - check data quality\n")
} 
