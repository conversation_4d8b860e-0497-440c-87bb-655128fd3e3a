# =============================================================================
# RANDOM INTERCEPT CROSS-LAGGED PANEL MODEL (RI-CLPM)
# Testing Stability of Self-Control Factors Across Ages 11, 14, and 17
# =============================================================================

library(pacman)
p_load(lavaan, semTools, dplyr, psych, ggplot2, knitr, haven, mice)

cat("=== RANDOM INTERCEPT CROSS-LAGGED PANEL MODEL ===\n")
cat("Testing stability of self-control factors across three time points\n\n")

# =============================================================================
# STEP 1: DATA LOADING AND PREPARATION
# =============================================================================

# Load data
data_path <- "/home/<USER>/dissertation_folder/data"
merged_data <- readRDS(file.path(data_path, "merged1203.rds"))

# Run recoding if needed
sc11_test_vars <- c("sc11_think_act", "sc11_considerate", "sc11_sharing", "sc11_helpful")
if(!all(sc11_test_vars %in% names(merged_data))) {
  cat("Running recoding scripts...\n")
  source("scripts/recode_self_control.R")
  source("scripts/recode_self_control_age14.R") 
  source("scripts/recode_self_control_age17.R")
}

# Define factor structure (consistent with three-factor analysis)
executive_items <- c("task_completion", "distracted", "fidgeting", "think_act", "restless")
selfcent_items <- c("considerate", "sharing", "helpful", "volunteer_help")
temper_items <- c("temper", "obedient", "lying")

# Create variable names for each wave
sc11_vars <- paste0("sc11_", c(executive_items, selfcent_items, temper_items))
sc14_vars <- paste0("sc14_", c(executive_items, selfcent_items, temper_items))
sc17_vars <- paste0("sc17_", c(executive_items, selfcent_items, temper_items))

# Create analysis dataset
all_vars <- c(sc11_vars, sc14_vars, sc17_vars)
available_vars <- all_vars[all_vars %in% names(merged_data)]
wide_data <- merged_data[, available_vars, drop = FALSE]

cat("Analysis dataset created with", nrow(wide_data), "observations\n")
cat("Available variables:", length(available_vars), "/", length(all_vars), "\n\n")

# =============================================================================
# STEP 2: CREATE FACTOR SCORES FOR RI-CLPM
# =============================================================================

cat("=== CREATING FACTOR SCORES ===\n")

# Function to create factor scores using measurement model
create_factor_scores <- function(data, age_suffix) {
  
  # Define items for this age
  exec_items_age <- paste0("sc", age_suffix, "_", executive_items)
  selfcent_items_age <- paste0("sc", age_suffix, "_", selfcent_items)
  temper_items_age <- paste0("sc", age_suffix, "_", temper_items)
  
  # Check which items are available
  available_exec <- exec_items_age[exec_items_age %in% names(data)]
  available_selfcent <- selfcent_items_age[selfcent_items_age %in% names(data)]
  available_temper <- temper_items_age[temper_items_age %in% names(data)]
  
  # Create measurement model syntax
  model_syntax <- paste0(
    "Executive =~ ", paste(available_exec, collapse = " + "), "\n",
    "SelfCent =~ ", paste(available_selfcent, collapse = " + "), "\n",
    "Temper =~ ", paste(available_temper, collapse = " + "), "\n"
  )
  
  # Fit measurement model
  fit <- cfa(model_syntax, data = data, missing = "ML")
  
  # Extract factor scores
  scores <- predict(fit)
  colnames(scores) <- paste0(c("Executive", "SelfCent", "Temper"), age_suffix)
  
  return(scores)
}

# Create factor scores for each wave
cat("Creating factor scores for each wave...\n")
scores_11 <- create_factor_scores(wide_data, "11")
scores_14 <- create_factor_scores(wide_data, "14")
scores_17 <- create_factor_scores(wide_data, "17")

# Combine factor scores
factor_data <- data.frame(scores_11, scores_14, scores_17)

# Check for missing data
missing_summary <- factor_data %>%
  summarise_all(~sum(is.na(.))) %>%
  pivot_longer(everything(), names_to = "Variable", values_to = "Missing")

cat("Missing data summary:\n")
print(missing_summary)

# =============================================================================
# STEP 3: BASIC DESCRIPTIVE STATISTICS
# =============================================================================

cat("\n=== DESCRIPTIVE STATISTICS ===\n")

# Descriptive statistics
desc_stats <- factor_data %>%
  summarise_all(list(
    mean = ~mean(., na.rm = TRUE),
    sd = ~sd(., na.rm = TRUE),
    min = ~min(., na.rm = TRUE),
    max = ~max(., na.rm = TRUE)
  ))

cat("Descriptive statistics for factor scores:\n")
print(round(desc_stats, 3))

# Correlation matrix
cat("\nCorrelation matrix:\n")
cor_matrix <- cor(factor_data, use = "pairwise.complete.obs")
print(round(cor_matrix, 3))

# =============================================================================
# STEP 4: RI-CLPM MODEL SPECIFICATION
# =============================================================================

cat("\n=== RI-CLPM MODEL SPECIFICATION ===\n")

# RI-CLPM for three factors across three time points
riclpm_syntax <- "
# Random intercepts (between-person stable differences)
RI_Executive =~ 1*Executive11 + 1*Executive14 + 1*Executive17
RI_SelfCent =~ 1*SelfCent11 + 1*SelfCent14 + 1*SelfCent17
RI_Temper =~ 1*Temper11 + 1*Temper14 + 1*Temper17

# Autoregressive paths (stability within factors)
Executive14 ~ a1*Executive11
Executive17 ~ a2*Executive14

SelfCent14 ~ b1*SelfCent11
SelfCent17 ~ b2*SelfCent14

Temper14 ~ c1*Temper11
Temper17 ~ c2*Temper14

# Cross-lagged paths (cross-factor influences)
# Executive -> SelfCent
SelfCent14 ~ d1*Executive11
SelfCent17 ~ d2*Executive14

# Executive -> Temper
Temper14 ~ e1*Executive11
Temper17 ~ e2*Executive14

# SelfCent -> Executive
Executive14 ~ f1*SelfCent11
Executive17 ~ f2*SelfCent14

# SelfCent -> Temper
Temper14 ~ g1*SelfCent11
Temper17 ~ g2*SelfCent14

# Temper -> Executive
Executive14 ~ h1*Temper11
Executive17 ~ h2*Temper14

# Temper -> SelfCent
SelfCent14 ~ i1*Temper11
SelfCent17 ~ i2*Temper17

# Within-time correlations (residual correlations)
Executive11 ~~ SelfCent11 + Temper11
SelfCent11 ~~ Temper11

Executive14 ~~ SelfCent14 + Temper14
SelfCent14 ~~ Temper14

Executive17 ~~ SelfCent17 + Temper17
SelfCent17 ~~ Temper17

# Random intercept correlations
RI_Executive ~~ RI_SelfCent + RI_Temper
RI_SelfCent ~~ RI_Temper

# Constrain residual variances to be equal across time (optional)
Executive11 ~~ eq_var_exec*Executive11
Executive14 ~~ eq_var_exec*Executive14
Executive17 ~~ eq_var_exec*Executive17

SelfCent11 ~~ eq_var_self*SelfCent11
SelfCent14 ~~ eq_var_self*SelfCent14
SelfCent17 ~~ eq_var_self*SelfCent17

Temper11 ~~ eq_var_temp*Temper11
Temper14 ~~ eq_var_temp*Temper14
Temper17 ~~ eq_var_temp*Temper17
"

# =============================================================================
# STEP 5: MODEL FITTING AND EVALUATION
# =============================================================================

cat("=== FITTING RI-CLPM MODEL ===\n")

# Fit the RI-CLPM
riclpm_fit <- sem(riclpm_syntax, data = factor_data, missing = "ML")

# Model summary
cat("Model fitting completed. Summary:\n")
summary(riclpm_fit, fit.measures = TRUE, standardized = TRUE)

# =============================================================================
# STEP 6: STABILITY ANALYSIS
# =============================================================================

cat("\n=== STABILITY ANALYSIS ===\n")

# Extract parameter estimates
params <- parameterEstimates(riclpm_fit, standardized = TRUE)

# Autoregressive (stability) coefficients
stability_params <- params[params$label %in% c("a1", "a2", "b1", "b2", "c1", "c2"), ]

cat("Autoregressive (Stability) Coefficients:\n")
stability_summary <- stability_params %>%
  select(label, est, se, pvalue, std.all) %>%
  mutate(
    Factor = case_when(
      label %in% c("a1", "a2") ~ "Executive",
      label %in% c("b1", "b2") ~ "SelfCent",
      label %in% c("c1", "c2") ~ "Temper"
    ),
    Period = case_when(
      label %in% c("a1", "b1", "c1") ~ "Age 11 -> 14",
      label %in% c("a2", "b2", "c2") ~ "Age 14 -> 17"
    )
  ) %>%
  arrange(Factor, Period)

print(stability_summary)

# Test for equal stability across time periods
if(all(c("a1", "a2", "b1", "b2", "c1", "c2") %in% params$label)) {
  
  # Model with constrained stability
  riclpm_constrained <- "
  # [Previous model syntax with constraints]
  RI_Executive =~ 1*Executive11 + 1*Executive14 + 1*Executive17
  RI_SelfCent =~ 1*SelfCent11 + 1*SelfCent14 + 1*SelfCent17
  RI_Temper =~ 1*Temper11 + 1*Temper14 + 1*Temper17
  
  # Constrained autoregressive paths
  Executive14 ~ a*Executive11
  Executive17 ~ a*Executive14
  
  SelfCent14 ~ b*SelfCent11
  SelfCent17 ~ b*SelfCent14
  
  Temper14 ~ c*Temper11
  Temper17 ~ c*Temper14
  
  # Cross-lagged paths
  SelfCent14 ~ d1*Executive11
  SelfCent17 ~ d2*Executive14
  Temper14 ~ e1*Executive11
  Temper17 ~ e2*Executive14
  Executive14 ~ f1*SelfCent11
  Executive17 ~ f2*SelfCent14
  Temper14 ~ g1*SelfCent11
  Temper17 ~ g2*SelfCent14
  Executive14 ~ h1*Temper11
  Executive17 ~ h2*Temper14
  SelfCent14 ~ i1*Temper11
  SelfCent17 ~ i2*Temper17
  
  # Correlations
  Executive11 ~~ SelfCent11 + Temper11
  SelfCent11 ~~ Temper11
  Executive14 ~~ SelfCent14 + Temper14
  SelfCent14 ~~ Temper14
  Executive17 ~~ SelfCent17 + Temper17
  SelfCent17 ~~ Temper17
  RI_Executive ~~ RI_SelfCent + RI_Temper
  RI_SelfCent ~~ RI_Temper
  "
  
  riclpm_constrained_fit <- sem(riclpm_constrained, data = factor_data, missing = "ML")

  summary(riclpm_constrained_fit, fit.measures = TRUE, standardized = TRUE)
  
  # Model comparison
  cat("\n=== MODEL COMPARISON: STABILITY CONSTRAINTS ===\n")
  stability_comparison <- anova(riclpm_fit, riclpm_constrained_fit)
  print(stability_comparison)
}

# =============================================================================
# STEP 7: VISUALIZATION
# =============================================================================

cat("\n=== CREATING VISUALIZATIONS ===\n")

# Plot stability coefficients
stability_plot_data <- stability_summary %>%
  filter(!is.na(est))

if(nrow(stability_plot_data) > 0) {
  
  p_stability <- ggplot(stability_plot_data, aes(x = Period, y = std.all, fill = Factor)) +
    geom_col(position = "dodge", alpha = 0.7) +
    geom_errorbar(aes(ymin = std.all - 1.96*se, ymax = std.all + 1.96*se), 
                  position = position_dodge(0.9), width = 0.2) +
    labs(title = "Autoregressive (Stability) Coefficients Across Time",
         subtitle = "Standardized estimates with 95% confidence intervals",
         x = "Time Period", y = "Standardized Coefficient", fill = "Factor") +
    theme_minimal() +
    theme(axis.text.x = element_text(angle = 45, hjust = 1)) +
    geom_hline(yintercept = 0, linetype = "dashed", alpha = 0.5)
  
  print(p_stability)
  
  # Save plot
  ggsave("stability_coefficients_riclpm.png", p_stability, width = 10, height = 6, dpi = 300)
  cat("Stability plot saved as 'stability_coefficients_riclpm.png'\n")
}

# =============================================================================
# STEP 8: INTERPRETATION AND SUMMARY
# =============================================================================

cat("\n=== INTERPRETATION SUMMARY ===\n")

# Model fit
fit_measures <- fitMeasures(riclpm_fit, c("chisq", "df", "pvalue", "cfi", "tli", "rmsea", "srmr"))

cat("Model Fit Statistics:\n")
cat("Chi-square:", round(fit_measures["chisq"], 3), "(df =", fit_measures["df"], ", p =", round(fit_measures["pvalue"], 3), ")\n")
cat("CFI:", round(fit_measures["cfi"], 3), "\n")
cat("TLI:", round(fit_measures["tli"], 3), "\n")
cat("RMSEA:", round(fit_measures["rmsea"], 3), "\n")
cat("SRMR:", round(fit_measures["srmr"], 3), "\n\n")

# Stability interpretation
cat("Stability Interpretation:\n")
for(factor in c("Executive", "SelfCent", "Temper")) {
  factor_stability <- stability_summary[stability_summary$Factor == factor, ]
  if(nrow(factor_stability) > 0) {
    avg_stability <- mean(factor_stability$std.all, na.rm = TRUE)
    cat(factor, "factor average stability:", round(avg_stability, 3), "\n")
    
    if(avg_stability > 0.7) {
      cat("  -> High stability across time\n")
    } else if(avg_stability > 0.5) {
      cat("  -> Moderate stability across time\n") 
    } else {
      cat("  -> Low stability across time\n")
    }
  }
}

cat("\nRI-CLPM Analysis Complete!\n")
cat("This model separates between-person stable differences (random intercepts)\n")
cat("from within-person changes and cross-factor influences over time.\n") 

