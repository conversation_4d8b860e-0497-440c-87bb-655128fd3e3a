# =============================================================================
# Recode Risk age 11 variables
# =============================================================================


# Age 11 risk-taking variables
risk_vars <- c("ecq45x00", "ecq25x00", "cgtriskt", "epsdoa00", "epsdor00", "ecq27x00")

# Check existing variables
existing_vars <- risk_vars[risk_vars %in% names(merged_data)]
missing_vars <- risk_vars[!risk_vars %in% names(merged_data)]

cat("Found:", paste(existing_vars, collapse = ", "), "\n")
if(length(missing_vars) > 0) cat("Missing:", paste(missing_vars, collapse = ", "), "\n")

# Frequency tables for existing variables
for(var in existing_vars) {
  cat("\n--- ", var, " ---\n")
  print(table(merged_data[[var]], useNA = "ifany"))
}

# Recode variables
if("ecq45x00" %in% names(merged_data)) {
  merged_data$risk5_sch <- ifelse(merged_data$ecq45x00 == -8, NA,
                                  ifelse(merged_data$ecq45x00 == 2, 0,
                                         ifelse(merged_data$ecq45x00 == 1, 1, merged_data$ecq45x00)))
}

if("ecq25x00" %in% names(merged_data)) {
  merged_data$risk5_rude <- ifelse(merged_data$ecq25x00 == -8, NA,
                                   ifelse(merged_data$ecq25x00 == 2, 0,
                                          ifelse(merged_data$ecq25x00 == 1, 1, merged_data$ecq25x00)))
}

if("epsdoa00" %in% names(merged_data)) {
  merged_data$risk5_lie <- ifelse(merged_data$epsdoa00 %in% c(-1, 4), NA,
                                  ifelse(merged_data$epsdoa00 == 1, 0,
                                         ifelse(merged_data$epsdoa00 == 2, 1,
                                                ifelse(merged_data$epsdoa00 == 3, 2, merged_data$epsdoa00))))
}

if("epsdor00" %in% names(merged_data)) {
  merged_data$risk5_obe <- ifelse(merged_data$epsdor00 %in% c(-1, 4), NA,
                                  ifelse(merged_data$epsdor00 == 1, 2,
                                         ifelse(merged_data$epsdor00 == 2, 1,
                                                ifelse(merged_data$epsdor00 == 3, 0, merged_data$epsdor00))))
}

if("ecq27x00" %in% names(merged_data)) {
  merged_data$risk5_wpp <- ifelse(merged_data$ecq27x00 == -8, NA,
                                  ifelse(merged_data$ecq27x00 == 2, 0,
                                         ifelse(merged_data$ecq27x00 == 1, 1, merged_data$ecq27x00)))
}

if("cgtriskt" %in% names(merged_data)) {
  merged_data$risk5_cgtriskt <- ifelse(merged_data$cgtriskt == -9, NA, merged_data$cgtriskt)
}

# Check recoded variables
recoded_vars <- c("risk5_sch", "risk5_rude", "risk5_lie", "risk5_obe", "risk5_wpp", "risk5_cgtriskt")
existing_recoded <- recoded_vars[recoded_vars %in% names(merged_data)]

cat("\nRecoded variables:\n")
for(var in existing_recoded) {
  cat("\n--- ", var, " ---\n")
  print(table(merged_data[[var]], useNA = "ifany"))
}

# =============================================================================
# Recode Impulsivity age 11 variables
# =============================================================================

# Age 11 impulsivity variables to recode
imp_vars <- c("imp5_tem", "imp5_res", "imp5_fid", "imp5_dis", "imp5_thi", "imp5_tas")

# Check which impulsivity variables exist
existing_imp_vars <- imp_vars[imp_vars %in% names(merged_data)]
missing_imp_vars <- imp_vars[!imp_vars %in% names(merged_data)]

cat("\nImpulsivity variables found:", paste(existing_imp_vars, collapse = ", "), "\n")
if(length(missing_imp_vars) > 0) cat("Missing impulsivity variables:", paste(missing_imp_vars, collapse = ", "), "\n")

# Frequency tables for existing impulsivity variables (before recoding)
cat("\nOriginal impulsivity variables:\n")
for(var in existing_imp_vars) {
  cat("\n--- ", var, " ---\n")
  print(table(merged_data[[var]], useNA = "ifany"))
}

# =============================================================================
# 4. RELIABILITY ANALYSIS - Age 11 Risk-Taking Variables
# =============================================================================

# Prepare data
risk_data <- merged_data[, existing_recoded, drop = FALSE]
risk_data_clean <- risk_data[rowSums(!is.na(risk_data)) > 0, ]

cat("\nReliability Analysis - All Variables:")
cat("\nVariables:", ncol(risk_data_clean))
cat("\nObservations:", nrow(risk_data_clean), "\n")

# Cronbach's alpha
alpha_all <- alpha(risk_data_clean, check.keys = TRUE)
cat("\nCronbach's Alpha:", round(alpha_all$total$std.alpha, 3))
cat("\nItems:", alpha_all$nvar)
cat("\nN:", alpha_all$n.obs, "\n")

# Display key item statistics
cat("Available columns:", paste(names(alpha_all$item.stats), collapse = ", "), "\n")
print(round(alpha_all$item.stats, 3))

# =============================================================================
# 5. SIMPLE CORRELATION AND FACTOR ANALYSIS - Age 11 Risk-Taking Variables
# =============================================================================

# Prepare data for analysis
efa_data <- na.omit(risk_data_clean)
cat("\nSimple Analysis - All Variables:")
cat("\nSample size:", nrow(efa_data))
cat("\nVariables:", ncol(efa_data), "\n")

# Correlation matrix
cat("\nCorrelation Matrix:\n")
print(round(cor(efa_data), 3))

# Simple 1-factor analysis
cat("\n--- 1-Factor Solution ---\n")
fa1 <- fa(efa_data, nfactors = 1, rotate = "none", fm = "pa")
print(round(fa1$loadings, 3))
cat("Variance explained:", round(fa1$Vaccounted[2], 3), "\n")

# Simple 2-factor analysis
cat("\n--- 2-Factor Solution ---\n")
fa2 <- fa(efa_data, nfactors = 2, rotate = "varimax", fm = "pa")
print(round(fa2$loadings, 3))
cat("Variance explained:", round(fa2$Vaccounted[2, ], 3), "\n")

# =============================================================================
# 6. ANALYSIS WITHOUT CGTRISKT
# =============================================================================

cat("\n", paste(rep("=", 60), collapse=""), "\n")
cat("ANALYSIS WITHOUT CGTRISKT\n")
cat(paste(rep("=", 60), collapse=""), "\n")

# Prepare data without cgtriskt
vars_no_cgtriskt <- c("risk5_sch", "risk5_rude", "risk5_lie", "risk5_obe", "risk5_wpp")
risk_data_no_cgt <- merged_data[, vars_no_cgtriskt, drop = FALSE]
risk_data_clean_no_cgt <- risk_data_no_cgt[rowSums(!is.na(risk_data_no_cgt)) > 0, ]

cat("\nVariables:", ncol(risk_data_clean_no_cgt))
cat("\nObservations:", nrow(risk_data_clean_no_cgt), "\n")

# =============================================================================
# 7. RELIABILITY ANALYSIS - WITHOUT CGTRISKT
# =============================================================================

# Cronbach's alpha
alpha_no_cgt <- alpha(risk_data_clean_no_cgt, check.keys = TRUE)
cat("\nCronbach's Alpha:", round(alpha_no_cgt$total$std.alpha, 3))
cat("\nItems:", alpha_no_cgt$nvar)
cat("\nN:", alpha_no_cgt$n.obs, "\n")

print(round(alpha_no_cgt$item.stats, 3))

# =============================================================================
# 8. SIMPLE CORRELATION AND FACTOR ANALYSIS - WITHOUT CGTRISKT
# =============================================================================

# Prepare data for analysis
efa_data_no_cgt <- na.omit(risk_data_clean_no_cgt)
cat("\nSimple Analysis - Without CGTRISKT:")
cat("\nSample size:", nrow(efa_data_no_cgt))
cat("\nVariables:", ncol(efa_data_no_cgt), "\n")

# Correlation matrix
cat("\nCorrelation Matrix:\n")
print(round(cor(efa_data_no_cgt), 3))

# Simple 1-factor analysis
cat("\n--- 1-Factor Solution ---\n")
fa1_no_cgt <- fa(efa_data_no_cgt, nfactors = 1, rotate = "none", fm = "pa")
print(round(fa1_no_cgt$loadings, 3))
cat("Variance explained:", round(fa1_no_cgt$Vaccounted[2], 3), "\n")

# Simple 2-factor analysis
cat("\n--- 2-Factor Solution ---\n")
fa2_no_cgt <- fa(efa_data_no_cgt, nfactors = 2, rotate = "varimax", fm = "pa")
print(round(fa2_no_cgt$loadings, 3))
cat("Variance explained:", round(fa2_no_cgt$Vaccounted[2, ], 3), "\n")

# =============================================================================
# 9. SIMPLE COMPARISON SUMMARY
# =============================================================================

cat("\n", paste(rep("=", 60), collapse=""), "\n")
cat("SUMMARY COMPARISON\n")
cat(paste(rep("=", 60), collapse=""), "\n")

# Compare alphas
cat("\nCronbach's Alpha Comparison:")
cat("\nWith cgtriskt:   ", round(alpha_all$total$std.alpha, 3))
cat("\nWithout cgtriskt:", round(alpha_no_cgt$total$std.alpha, 3))
cat("\nChange:          ", round(alpha_no_cgt$total$std.alpha - alpha_all$total$std.alpha, 3), "\n")

cat("\nRecommendation: Use analysis WITHOUT cgtriskt for better psychometric properties\n")


