# =============================================================================
# PARALLEL-PROCESS LATENT GROWTH CURVE MODEL WITH SUMMATED SCORES
# Self-Control (Three Summated Factors → Latent Factor) + Fight with Others
# Ages 11, 14, and 17
# =============================================================================

library(pacman)
p_load(lavaan, semTools, dplyr, psych, ggplot2, knitr, haven)

cat("=== PARALLEL-PROCESS LGC MODEL WITH SUMMATED SCORES ===\n")
cat("Three-factor summated scores loading onto self-control latent factor\n")
cat("Ages 11, 14, and 17\n\n")

# =============================================================================
# STEP 1: DATA LOADING AND PREPARATION
# =============================================================================

cat("Loading data...\n")
data_path <- "/home/<USER>/dissertation_folder/data"
merged_data <- readRDS(file.path(data_path, "merged1203.rds"))

cat("Dataset dimensions:", dim(merged_data), "\n")

# Check if recoded self-control variables exist
sc11_test_vars <- c("sc11_think_act", "sc11_considerate", "sc11_sharing", "sc11_helpful")
if(!all(sc11_test_vars %in% names(merged_data))) {
  cat("Running self-control recoding scripts...\n")
  source("scripts/recode_self_control.R")
  source("scripts/recode_self_control_age14.R") 
  source("scripts/recode_self_control_age17.R")
  cat("Self-control recoding completed.\n")
} else {
  cat("Self-control variables already available.\n")
}

# =============================================================================
# STEP 2: RECODE FIGHT VARIABLES
# =============================================================================

cat("\n=== RECODING FIGHT VARIABLES ===\n")

# Check for fight variables
fight_vars_original <- c("epsdfb00", "fpsdfb00", "gpsdfb00")
fight_vars_exist <- fight_vars_original[fight_vars_original %in% names(merged_data)]
cat("Fight variables found:", paste(fight_vars_exist, collapse = ", "), "\n")

# Apply recoding scheme from user query
if("epsdfb00" %in% names(merged_data)) {
  merged_data$agr5_fig <- ifelse(merged_data$epsdfb00 %in% c(-1, 4), NA,
                                 ifelse(merged_data$epsdfb00 == 1, 0,
                                        ifelse(merged_data$epsdfb00 == 2, 1,
                                               ifelse(merged_data$epsdfb00 == 3, 2, NA))))
  cat("Age 11 fight variable recoded: agr5_fig\n")
}

if("fpsdfb00" %in% names(merged_data)) {
  merged_data$agr6_fig <- ifelse(merged_data$fpsdfb00 %in% c(-1, -9), NA,
                                 ifelse(merged_data$fpsdfb00 == 1, 0,
                                        ifelse(merged_data$fpsdfb00 == 2, 1,
                                               ifelse(merged_data$fpsdfb00 == 3, 2, NA))))
  cat("Age 14 fight variable recoded: agr6_fig\n")
}

if("gpsdfb00" %in% names(merged_data)) {
  merged_data$agr7_fig <- ifelse(merged_data$gpsdfb00 %in% c(-1, 4), NA,
                                 ifelse(merged_data$gpsdfb00 == 1, 0,
                                        ifelse(merged_data$gpsdfb00 == 2, 1,
                                               ifelse(merged_data$gpsdfb00 == 3, 2, NA))))
  cat("Age 17 fight variable recoded: agr7_fig\n")
}

# =============================================================================
# STEP 3: CREATE SUMMATED SCORES FOR SELF-CONTROL FACTORS
# =============================================================================

cat("\n=== CREATING SUMMATED SCORES FOR THREE-FACTOR STRUCTURE ===\n")

# Define the three-factor structure
executive_items <- c("task_completion", "distracted", "fidgeting", "think_act", "restless")
prosocial_items <- c("considerate", "sharing", "helpful", "volunteer_help")
temper_items <- c("temper", "obedient", "lying")

cat("Factor structure:\n")
cat("Executive factor:", paste(executive_items, collapse = ", "), "\n")
cat("Prosocial factor:", paste(prosocial_items, collapse = ", "), "\n")
cat("Temper factor:", paste(temper_items, collapse = ", "), "\n\n")

# Function to create summated scores
create_summated_score <- function(age, items, factor_name) {
  cat("Creating", factor_name, "summated score for age", age, "...\n")
  
  # Get variable names for this age and factor
  age_vars <- paste0("sc", age, "_", items)
  available_vars <- age_vars[age_vars %in% names(merged_data)]
  
  if(length(available_vars) < 2) {
    cat("Warning: Only", length(available_vars), "variables available for", factor_name, "age", age, "\n")
    return(rep(NA, nrow(merged_data)))
  }
  
  # Calculate summated score (mean of available items)
  # Require at least 2 items to be non-missing for a valid score
  factor_data <- merged_data[, available_vars, drop = FALSE]
  summated_scores <- rowMeans(factor_data, na.rm = TRUE)
  
  # Set to NA if fewer than 2 items are available
  valid_items <- rowSums(!is.na(factor_data))
  summated_scores[valid_items < 2] <- NA
  
  valid_n <- sum(!is.na(summated_scores))
  mean_score <- mean(summated_scores, na.rm = TRUE)
  sd_score <- sd(summated_scores, na.rm = TRUE)
  
  cat(paste0("  ", factor_name, " age ", age, ": N = ", valid_n, 
             ", M = ", round(mean_score, 3), ", SD = ", round(sd_score, 3), "\n"))
  
  return(summated_scores)
}

# Create summated scores for each factor at each age
for(age in c(11, 14, 17)) {
  merged_data[[paste0("exec_sum_", age)]] <- create_summated_score(age, executive_items, "Executive")
  merged_data[[paste0("prosocial_sum_", age)]] <- create_summated_score(age, prosocial_items, "Prosocial")
  merged_data[[paste0("temper_sum_", age)]] <- create_summated_score(age, temper_items, "Temper")
}

cat("\nSummated scores created successfully.\n")

# =============================================================================
# STEP 4: CREATE ANALYSIS DATASET
# =============================================================================

cat("\n=== CREATING ANALYSIS DATASET ===\n")

# Variables for analysis
summated_vars <- c("exec_sum_11", "prosocial_sum_11", "temper_sum_11",
                   "exec_sum_14", "prosocial_sum_14", "temper_sum_14",
                   "exec_sum_17", "prosocial_sum_17", "temper_sum_17")
fight_vars <- c("agr5_fig", "agr6_fig", "agr7_fig")
analysis_vars <- c(summated_vars, fight_vars)

# Check availability
available_vars <- analysis_vars[analysis_vars %in% names(merged_data)]
cat("Available analysis variables:", paste(available_vars, collapse = ", "), "\n")

# Create analysis dataset
analysis_data <- merged_data[, available_vars, drop = FALSE]
analysis_data$id <- 1:nrow(analysis_data)

cat("Analysis dataset created with", nrow(analysis_data), "observations\n")

# Data completeness check
cat("\nData completeness:\n")
for(var in available_vars) {
  valid_n <- sum(!is.na(analysis_data[[var]]))
  pct_valid <- round(100 * valid_n / nrow(analysis_data), 1)
  cat(var, ": N =", valid_n, "(", pct_valid, "%)\n")
}

# Check completeness by domain and wave
cat("\nDomain completeness by wave:\n")
for(age in c(11, 14, 17)) {
  age_vars <- paste0(c("exec_sum_", "prosocial_sum_", "temper_sum_"), age)
  available_age_vars <- age_vars[age_vars %in% names(analysis_data)]
  
  if(length(available_age_vars) > 0) {
    complete_cases <- rowSums(!is.na(analysis_data[, available_age_vars, drop = FALSE])) >= 2
    cat("Age", age, ": N =", sum(complete_cases), "with 2+ factors\n")
  }
}

# =============================================================================
# STEP 5: PARALLEL-PROCESS LGC MODEL SPECIFICATION
# =============================================================================

cat("\n=== PARALLEL-PROCESS LGC MODEL SPECIFICATION ===\n")

# Model syntax with summated scores loading onto latent factors
parallel_summated_syntax <- '
# ===== SELF-CONTROL LATENT FACTORS AT EACH WAVE =====
# Summated scores load onto self-control latent factor at each wave
SelfControl11 =~ exec_sum_11 + prosocial_sum_11 + temper_sum_11
SelfControl14 =~ exec_sum_14 + prosocial_sum_14 + temper_sum_14
SelfControl17 =~ exec_sum_17 + prosocial_sum_17 + temper_sum_17

# ===== GROWTH FACTORS =====
# Self-control growth factors
iSC =~ 1*SelfControl11 + 1*SelfControl14 + 1*SelfControl17
sSC =~ 0*SelfControl11 + 1*SelfControl14 + 2*SelfControl17

# Fight behavior growth factors
iFight =~ 1*agr5_fig + 1*agr6_fig + 1*agr7_fig
sFight =~ 0*agr5_fig + 1*agr6_fig + 2*agr7_fig

# ===== GROWTH MEANS =====
iSC ~ 1
sSC ~ 1
iFight ~ 1
sFight ~ 1

# ===== PARALLEL-PROCESS CORRELATIONS =====
# Correlations between intercepts and slopes
iSC ~~ iFight      # Initial levels correlation
sSC ~~ sFight      # Slope correlation
iSC ~~ sFight      # Cross-domain: initial SC with fight change
sSC ~~ iFight      # Cross-domain: SC change with initial fight

# ===== IDENTIFICATION CONSTRAINTS =====
# Fix first loading per latent factor to 1 for identification
# (Already done automatically by lavaan for first indicator)

# ===== RESIDUAL CORRELATIONS (OPTIONAL) =====
# Allow residual correlations between same indicators across time
# exec_sum_11 ~~ exec_sum_14 + exec_sum_17
# exec_sum_14 ~~ exec_sum_17
# prosocial_sum_11 ~~ prosocial_sum_14 + prosocial_sum_17
# prosocial_sum_14 ~~ prosocial_sum_17
# temper_sum_11 ~~ temper_sum_14 + temper_sum_17
# temper_sum_14 ~~ temper_sum_17
'

cat("Model includes:\n")
cat("1. Summated scores for three factors at each wave\n")
cat("2. Self-control latent factors at each wave\n")
cat("3. Growth factors for self-control and fighting\n")
cat("4. Cross-domain correlations\n\n")

# =============================================================================
# STEP 6: FIT THE MODELS
# =============================================================================

cat("=== FITTING MODELS ===\n")

# Model 1: Self-Control LGC only (for comparison)
cat("1. Fitting self-control LGC model (summated approach)...\n")
sc_summated_syntax <- '
# Self-control latent factors at each wave
SelfControl11 =~ exec_sum_11 + prosocial_sum_11 + temper_sum_11
SelfControl14 =~ exec_sum_14 + prosocial_sum_14 + temper_sum_14
SelfControl17 =~ exec_sum_17 + prosocial_sum_17 + temper_sum_17

# Growth factors
iSC =~ 1*SelfControl11 + 1*SelfControl14 + 1*SelfControl17
sSC =~ 0*SelfControl11 + 1*SelfControl14 + 2*SelfControl17

# Growth means
iSC ~ 1
sSC ~ 1
'

fit_sc_summated <- tryCatch({
  sem(
    model = sc_summated_syntax,
    data = analysis_data,
    estimator = "MLR",
    missing = "ML",
    meanstructure = TRUE
  )
}, error = function(e) {
  cat("Error fitting self-control summated LGC:", e$message, "\n")
  return(NULL)
})

if(!is.null(fit_sc_summated) && lavInspect(fit_sc_summated, "converged")) {
  cat("✓ Self-control summated LGC model converged\n")
  
  fit_sc_sum <- fitMeasures(fit_sc_summated, c("cfi", "tli", "rmsea", "srmr"))
  cat("Self-control summated LGC fit: CFI =", round(fit_sc_sum["cfi"], 3), 
      ", TLI =", round(fit_sc_sum["tli"], 3),
      ", RMSEA =", round(fit_sc_sum["rmsea"], 3),
      ", SRMR =", round(fit_sc_sum["srmr"], 3), "\n")
      
  # Growth parameters
  sc_params <- parameterEstimates(fit_sc_summated)
  sc_intercept <- sc_params[sc_params$lhs == "iSC" & sc_params$op == "~1", ]
  sc_slope <- sc_params[sc_params$lhs == "sSC" & sc_params$op == "~1", ]
  
  cat("Self-control development:\n")
  if(nrow(sc_intercept) > 0) {
    cat("  Intercept =", round(sc_intercept$est, 3), "(p =", round(sc_intercept$pvalue, 3), ")\n")
  }
  if(nrow(sc_slope) > 0) {
    cat("  Slope =", round(sc_slope$est, 3), "(p =", round(sc_slope$pvalue, 3), ")\n")
    if(!is.na(sc_slope$pvalue) && sc_slope$pvalue < 0.05) {
      if(sc_slope$est > 0) {
        cat("  → Self-control problems INCREASE over time\n")
      } else {
        cat("  → Self-control problems DECREASE over time (improvement)\n")
      }
    } else {
      cat("  → No significant change in self-control\n")
    }
  }
  
  # Factor loadings
  cat("\nFactor loadings (standardized):\n")
  std_params <- standardizedSolution(fit_sc_summated)
  loadings <- std_params[std_params$op == "=~", ]
  for(i in 1:nrow(loadings)) {
    cat("  ", loadings$lhs[i], "->", loadings$rhs[i], ":", 
        round(loadings$est.std[i], 3), "\n")
  }
  
} else {
  cat("❌ Self-control summated LGC model failed\n")
}

# Model 2: Fight LGC only (for comparison)
cat("\n2. Fitting fight behavior LGC model...\n")
fight_lgc_syntax <- '
# Growth factors for fighting
iFight =~ 1*agr5_fig + 1*agr6_fig + 1*agr7_fig
sFight =~ 0*agr5_fig + 1*agr6_fig + 2*agr7_fig

# Growth means
iFight ~ 1
sFight ~ 1
'

fit_fight_lgc <- tryCatch({
  sem(
    model = fight_lgc_syntax,
    data = analysis_data,
    estimator = "MLR",
    missing = "ML",
    meanstructure = TRUE,
    std.lv=TRUE
  )
}, error = function(e) {
  cat("Error fitting fight LGC:", e$message, "\n")
  return(NULL)
})

if(!is.null(fit_fight_lgc) && lavInspect(fit_fight_lgc, "converged")) {
  cat("✓ Fight LGC model converged\n")
  
  # Growth parameters
  fight_params <- parameterEstimates(fit_fight_lgc)
  fight_intercept <- fight_params[fight_params$lhs == "iFight" & fight_params$op == "~1", ]
  fight_slope <- fight_params[fight_params$lhs == "sFight" & fight_params$op == "~1", ]
  
  cat("Fight behavior development:\n")
  if(nrow(fight_intercept) > 0) {
    cat("  Intercept =", round(fight_intercept$est, 3), "(p =", round(fight_intercept$pvalue, 3), ")\n")
  }
  if(nrow(fight_slope) > 0) {
    cat("  Slope =", round(fight_slope$est, 3), "(p =", round(fight_slope$pvalue, 3), ")\n")
    if(!is.na(fight_slope$pvalue) && fight_slope$pvalue < 0.05) {
      if(fight_slope$est > 0) {
        cat("  → Fighting behavior INCREASES over time\n")
      } else {
        cat("  → Fighting behavior DECREASES over time\n")
      }
    } else {
      cat("  → No significant change in fighting behavior\n")
    }
  }
} else {
  cat("❌ Fight LGC model failed\n")
}

# Model 3: Full Parallel-Process Model
cat("\n3. Fitting parallel-process LGC model (summated approach)...\n")
fit_parallel_summated <- tryCatch({
  sem(
    model = parallel_summated_syntax,
    data = analysis_data,
    estimator = "MLR",
    missing = "ML",
    meanstructure = TRUE
  )
}, error = function(e) {
  cat("Error fitting parallel-process summated model:", e$message, "\n")
  return(NULL)
})

if(!is.null(fit_parallel_summated) && lavInspect(fit_parallel_summated, "converged")) {
  cat("✓ Parallel-process summated model converged\n")
  
  fit_par_sum <- fitMeasures(fit_parallel_summated, c("cfi", "tli", "rmsea", "srmr"))
  cat("Parallel-process summated fit: CFI =", round(fit_par_sum["cfi"], 3), 
      ", TLI =", round(fit_par_sum["tli"], 3),
      ", RMSEA =", round(fit_par_sum["rmsea"], 3),
      ", SRMR =", round(fit_par_sum["srmr"], 3), "\n")
      
  # Cross-domain correlations
  par_sum_params <- parameterEstimates(fit_parallel_summated)
  
  # Find correlation parameters
  intercept_cor <- par_sum_params[par_sum_params$lhs == "iSC" & par_sum_params$op == "~~" & par_sum_params$rhs == "iFight", ]
  slope_cor <- par_sum_params[par_sum_params$lhs == "sSC" & par_sum_params$op == "~~" & par_sum_params$rhs == "sFight", ]
  cross_cor1 <- par_sum_params[par_sum_params$lhs == "iSC" & par_sum_params$op == "~~" & par_sum_params$rhs == "sFight", ]
  cross_cor2 <- par_sum_params[par_sum_params$lhs == "sSC" & par_sum_params$op == "~~" & par_sum_params$rhs == "iFight", ]
  
  cat("\nParallel-process correlations:\n")
  if(nrow(intercept_cor) > 0) {
    cat("  Initial levels correlation (iSC ~~ iFight) =", round(intercept_cor$est, 3), 
        "(p =", round(intercept_cor$pvalue, 3), ")")
    if(!is.na(intercept_cor$pvalue) && intercept_cor$pvalue < 0.05) {
      cat(" [SIGNIFICANT]")
    }
    cat("\n")
  }
  
  if(nrow(slope_cor) > 0) {
    cat("  Change correlation (sSC ~~ sFight) =", round(slope_cor$est, 3), 
        "(p =", round(slope_cor$pvalue, 3), ")")
    if(!is.na(slope_cor$pvalue) && slope_cor$pvalue < 0.05) {
      cat(" [SIGNIFICANT COUPLING]")
    }
    cat("\n")
  }
  
  if(nrow(cross_cor1) > 0) {
    cat("  Initial SC → Fighting change (iSC ~~ sFight) =", round(cross_cor1$est, 3), 
        "(p =", round(cross_cor1$pvalue, 3), ")")
    if(!is.na(cross_cor1$pvalue) && cross_cor1$pvalue < 0.05) {
      cat(" [PREDICTIVE]")
    }
    cat("\n")
  }
  
  if(nrow(cross_cor2) > 0) {
    cat("  Initial Fighting → SC change (sSC ~~ iFight) =", round(cross_cor2$est, 3), 
        "(p =", round(cross_cor2$pvalue, 3), ")")
    if(!is.na(cross_cor2$pvalue) && cross_cor2$pvalue < 0.05) {
      cat(" [PREDICTIVE]")
    }
    cat("\n")
  }
  
  # Model summary
  cat("\n=== DETAILED MODEL SUMMARY ===\n")
  summary(fit_parallel_summated, fit.measures = TRUE, standardized = TRUE)
  
} else {
  cat("❌ Parallel-process summated model failed\n")
}

# =============================================================================
# STEP 7: MODEL COMPARISON AND INTERPRETATION
# =============================================================================

cat("\n=== MODEL COMPARISON AND THEORETICAL INTERPRETATION ===\n")

# Compare fit indices if models converged
converged_models <- c()
fit_measures_list <- list()

if(!is.null(fit_sc_summated) && lavInspect(fit_sc_summated, "converged")) {
  converged_models <- c(converged_models, "Self-Control Summated LGC")
  fit_measures_list[["Self-Control Summated LGC"]] <- fitMeasures(fit_sc_summated, c("chisq", "df", "cfi", "tli", "rmsea", "srmr", "aic", "bic"))
}

if(!is.null(fit_fight_lgc) && lavInspect(fit_fight_lgc, "converged")) {
  converged_models <- c(converged_models, "Fight LGC")
  fit_measures_list[["Fight LGC"]] <- fitMeasures(fit_fight_lgc, c("chisq", "df", "cfi", "tli", "rmsea", "srmr", "aic", "bic"))
}

if(!is.null(fit_parallel_summated) && lavInspect(fit_parallel_summated, "converged")) {
  converged_models <- c(converged_models, "Parallel-Process Summated")
  fit_measures_list[["Parallel-Process Summated"]] <- fitMeasures(fit_parallel_summated, c("chisq", "df", "cfi", "tli", "rmsea", "srmr", "aic", "bic"))
}

if(length(fit_measures_list) > 0) {
  cat("Model comparison table:\n")
  comparison_df <- data.frame(
    Model = names(fit_measures_list),
    ChiSq = sapply(fit_measures_list, function(x) round(x["chisq"], 2)),
    df = sapply(fit_measures_list, function(x) x["df"]),
    CFI = sapply(fit_measures_list, function(x) round(x["cfi"], 3)),
    TLI = sapply(fit_measures_list, function(x) round(x["tli"], 3)),
    RMSEA = sapply(fit_measures_list, function(x) round(x["rmsea"], 3)),
    SRMR = sapply(fit_measures_list, function(x) round(x["srmr"], 3)),
    AIC = sapply(fit_measures_list, function(x) round(x["aic"], 0)),
    BIC = sapply(fit_measures_list, function(x) round(x["bic"], 0))
  )
  print(comparison_df)
}

cat("\n=== ADVANTAGES OF SUMMATED SCORE APPROACH ===\n")
cat("1. More parsimonious than full second-order model\n")
cat("2. Maintains theoretical three-factor structure\n")
cat("3. Better identification and convergence\n")
cat("4. Easier interpretation of factor loadings\n")
cat("5. Reduced model complexity while preserving key relationships\n\n")

cat("=== THEORETICAL IMPLICATIONS ===\n")
cat("The summated score approach tests:\n")
cat("1. Whether the three self-control domains form a coherent latent factor\n")
cat("2. How this general self-control factor develops over time\n")
cat("3. Cross-domain associations with fighting behavior\n")
cat("4. Developmental coupling between self-control and aggression\n\n")

cat("=== NEXT STEPS ===\n")
cat("1. Examine factor loadings to validate three-factor structure\n")
cat("2. Test measurement invariance across waves\n")
cat("3. Compare with full second-order model\n")
cat("4. Add residual correlations if needed for fit improvement\n")
cat("5. Consider moderation by gender, SES, or other factors\n\n")

cat("=== PARALLEL-PROCESS LGC WITH SUMMATED SCORES COMPLETE ===\n") 