# =============================================================================
# RI-CLPM FOR COMPOSITE SELF-CONTROL FACTOR
# Testing Stability of Overall Self-Control Across Ages 11, 14, and 17
# =============================================================================

library(pacman)
p_load(lavaan, semTools, dplyr, psych, ggplot2, knitr, haven)

cat("=== RI-CLPM: COMPOSITE SELF-CONTROL FACTOR ===\n")
cat("Testing stability of overall self-control across three time points\n\n")

# =============================================================================
# STEP 1: DATA LOADING AND PREPARATION
# =============================================================================

# Load data
data_path <- "/home/<USER>/dissertation_folder/data"
merged_data <- readRDS(file.path(data_path, "merged1203.rds"))

# Run recoding if needed
sc11_test_vars <- c("sc11_think_act", "sc11_considerate", "sc11_sharing", "sc11_helpful")
if(!all(sc11_test_vars %in% names(merged_data))) {
  cat("Running recoding scripts...\n")
  source("scripts/recode_self_control.R")
  source("scripts/recode_self_control_age14.R") 
  source("scripts/recode_self_control_age17.R")
}

# Define all self-control items
all_items <- c("task_completion", "distracted", "fidgeting", "think_act", "restless",
               "considerate", "sharing", "helpful", "volunteer_help",
               "temper", "obedient", "lying")

# Create variable names for each wave
sc11_vars <- paste0("sc11_", all_items)
sc14_vars <- paste0("sc14_", all_items)
sc17_vars <- paste0("sc17_", all_items)

# Create analysis dataset
all_vars <- c(sc11_vars, sc14_vars, sc17_vars)
available_vars <- all_vars[all_vars %in% names(merged_data)]
wide_data <- merged_data[, available_vars, drop = FALSE]

cat("Analysis dataset created with", nrow(wide_data), "observations\n")
cat("Available variables:", length(available_vars), "/", length(all_vars), "\n\n")

# =============================================================================
# STEP 2: MEASUREMENT MODEL FOR COMPOSITE SELF-CONTROL
# =============================================================================

cat("=== MEASUREMENT MODEL ===\n")

# Create measurement model syntax for composite self-control at each age
measurement_syntax <- "
# Age 11 Self-Control
SelfControl11 =~ sc11_task_completion + sc11_distracted + sc11_fidgeting + 
                 sc11_think_act + sc11_restless + sc11_considerate + 
                 sc11_sharing + sc11_helpful + sc11_volunteer_help + 
                 sc11_temper + sc11_obedient + sc11_lying

# Age 14 Self-Control  
SelfControl14 =~ sc14_task_completion + sc14_distracted + sc14_fidgeting + 
                 sc14_think_act + sc14_restless + sc14_considerate + 
                 sc14_sharing + sc14_helpful + sc14_volunteer_help + 
                 sc14_temper + sc14_obedient + sc14_lying

# Age 17 Self-Control
SelfControl17 =~ sc17_task_completion + sc17_distracted + sc17_fidgeting + 
                 sc17_think_act + sc17_restless + sc17_considerate + 
                 sc17_sharing + sc17_helpful + sc17_volunteer_help + 
                 sc17_temper + sc17_obedient + sc17_lying
"

# Filter syntax for available variables only
available_sc11 <- sc11_vars[sc11_vars %in% names(wide_data)]
available_sc14 <- sc14_vars[sc14_vars %in% names(wide_data)]
available_sc17 <- sc17_vars[sc17_vars %in% names(wide_data)]

# Create dynamic measurement syntax
measurement_syntax_available <- paste0(
  "SelfControl11 =~ ", paste(available_sc11, collapse = " + "), "\n",
  "SelfControl14 =~ ", paste(available_sc14, collapse = " + "), "\n", 
  "SelfControl17 =~ ", paste(available_sc17, collapse = " + "), "\n"
)

cat("Measurement model syntax created with available variables\n")

# =============================================================================
# STEP 3: RI-CLPM MODEL SPECIFICATION  
# =============================================================================

cat("=== RI-CLPM MODEL SPECIFICATION ===\n")

# Complete RI-CLPM syntax
riclpm_composite_syntax <- paste0(
  measurement_syntax_available,
  "
# Random intercept (between-person stable differences)
RI_SelfControl =~ 1*SelfControl11 + 1*SelfControl14 + 1*SelfControl17

# Autoregressive paths (stability)
SelfControl14 ~ stability1*SelfControl11
SelfControl17 ~ stability2*SelfControl14

# Constrain random intercept mean to zero
RI_SelfControl ~ 0*1

# Residual variances (can be constrained to equality if desired)
SelfControl11 ~~ resvar*SelfControl11
SelfControl14 ~~ resvar*SelfControl14  
SelfControl17 ~~ resvar*SelfControl17
"
)

# =============================================================================
# STEP 4: MODEL FITTING
# =============================================================================

cat("=== FITTING RI-CLPM MODEL ===\n")

# Fit the model
riclpm_composite_fit <- sem(riclpm_composite_syntax, data = wide_data, missing = "ML")

# Model summary
cat("Model fitting completed. Summary:\n")
summary(riclpm_composite_fit, fit.measures = TRUE, standardized = TRUE)

# =============================================================================
# STEP 5: STABILITY ANALYSIS
# =============================================================================

cat("\n=== STABILITY ANALYSIS ===\n")

# Extract stability coefficients
params <- parameterEstimates(riclpm_composite_fit, standardized = TRUE)
stability_params <- params[params$label %in% c("stability1", "stability2"), ]

cat("Stability Coefficients:\n")
stability_summary <- stability_params %>%
  select(label, est, se, pvalue, std.all) %>%
  mutate(
    Period = case_when(
      label == "stability1" ~ "Age 11 -> 14",
      label == "stability2" ~ "Age 14 -> 17"
    )
  )

print(stability_summary)

# Calculate average stability
avg_stability <- mean(stability_summary$std.all, na.rm = TRUE)
cat("\nAverage standardized stability coefficient:", round(avg_stability, 3), "\n")

# Interpret stability
if(avg_stability > 0.7) {
  cat("Interpretation: HIGH stability of self-control across time\n")
} else if(avg_stability > 0.5) {
  cat("Interpretation: MODERATE stability of self-control across time\n")
} else {
  cat("Interpretation: LOW stability of self-control across time\n")
}

# =============================================================================
# STEP 6: TEST STABILITY CONSTRAINTS
# =============================================================================

cat("\n=== TESTING EQUAL STABILITY CONSTRAINTS ===\n")

# Model with equal stability constraints
riclpm_equal_stability <- paste0(
  measurement_syntax_available,
  "
# Random intercept
RI_SelfControl =~ 1*SelfControl11 + 1*SelfControl14 + 1*SelfControl17

# Constrained autoregressive paths (equal stability)
SelfControl14 ~ stability*SelfControl11
SelfControl17 ~ stability*SelfControl14

# Constrain random intercept mean
RI_SelfControl ~ 0*1

# Equal residual variances
SelfControl11 ~~ resvar*SelfControl11
SelfControl14 ~~ resvar*SelfControl14
SelfControl17 ~~ resvar*SelfControl17
"
)

# Fit constrained model
riclpm_constrained_fit <- sem(riclpm_equal_stability, data = wide_data, missing = "ML")

# Model comparison
cat("Comparing models with free vs. constrained stability...\n")
stability_comparison <- anova(riclpm_composite_fit, riclpm_constrained_fit)
print(stability_comparison)

# =============================================================================
# STEP 7: VISUALIZATION
# =============================================================================

cat("\n=== CREATING VISUALIZATION ===\n")

# Plot stability coefficients
if(nrow(stability_summary) > 0) {
  
  p_stability <- ggplot(stability_summary, aes(x = Period, y = std.all)) +
    geom_col(fill = "steelblue", alpha = 0.7, width = 0.6) +
    geom_errorbar(aes(ymin = std.all - 1.96*se, ymax = std.all + 1.96*se), 
                  width = 0.2, color = "darkblue") +
    labs(title = "Self-Control Stability Across Time",
         subtitle = "Standardized autoregressive coefficients with 95% CI",
         x = "Time Period", y = "Standardized Coefficient") +
    theme_minimal() +
    theme(axis.text.x = element_text(size = 12),
          plot.title = element_text(size = 14, face = "bold")) +
    geom_hline(yintercept = 0, linetype = "dashed", alpha = 0.5) +
    ylim(0, 1)
  
  print(p_stability)
  
  # Save plot
  ggsave("composite_selfcontrol_stability.png", p_stability, width = 8, height = 6, dpi = 300)
  cat("Stability plot saved as 'composite_selfcontrol_stability.png'\n")
}

# =============================================================================
# STEP 8: FACTOR SCORES AND DESCRIPTIVE ANALYSIS
# =============================================================================

cat("\n=== FACTOR SCORES ANALYSIS ===\n")

# Extract factor scores
factor_scores <- predict(riclpm_composite_fit)
factor_scores_df <- data.frame(factor_scores)

# Descriptive statistics
cat("Descriptive statistics for self-control factor scores:\n")
desc_stats <- factor_scores_df %>%
  summarise_all(list(
    mean = ~mean(., na.rm = TRUE),
    sd = ~sd(., na.rm = TRUE),
    min = ~min(., na.rm = TRUE),
    max = ~max(., na.rm = TRUE)
  ))

print(round(desc_stats, 3))

# Correlations between time points
cat("\nCorrelations between time points:\n")
cor_matrix <- cor(factor_scores_df, use = "pairwise.complete.obs")
print(round(cor_matrix, 3))

# =============================================================================
# STEP 9: MODEL FIT AND INTERPRETATION
# =============================================================================

cat("\n=== FINAL INTERPRETATION ===\n")

# Model fit statistics
fit_measures <- fitMeasures(riclpm_composite_fit, c("chisq", "df", "pvalue", "cfi", "tli", "rmsea", "srmr"))

cat("Model Fit Statistics:\n")
cat("Chi-square:", round(fit_measures["chisq"], 3), "(df =", fit_measures["df"], ", p =", round(fit_measures["pvalue"], 3), ")\n")
cat("CFI:", round(fit_measures["cfi"], 3), "\n")
cat("TLI:", round(fit_measures["tli"], 3), "\n")
cat("RMSEA:", round(fit_measures["rmsea"], 3), "\n")
cat("SRMR:", round(fit_measures["srmr"], 3), "\n\n")

# Interpretation
cat("INTERPRETATION:\n")
cat("This RI-CLPM separates between-person stable differences in self-control\n")
cat("from within-person changes over time.\n\n")

cat("Stability findings:\n")
for(i in 1:nrow(stability_summary)) {
  period <- stability_summary$Period[i]
  coef <- round(stability_summary$std.all[i], 3)
  pval <- round(stability_summary$pvalue[i], 3)
  
  cat("- ", period, ": β =", coef, "(p =", pval, ")\n")
}

cat("\nOverall stability:", round(avg_stability, 3), "\n")
cat("This indicates", ifelse(avg_stability > 0.7, "high", 
                           ifelse(avg_stability > 0.5, "moderate", "low")), 
    "rank-order stability of self-control across adolescence.\n")

cat("\nRI-CLPM Analysis Complete!\n") 