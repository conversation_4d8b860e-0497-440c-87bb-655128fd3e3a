# Real-time Progress Monitoring for Bayesian MCMC
# Functions to track and display MCMC sampling progress
#################

# Function to create a progress bar
create_progress_bar <- function(width = 50) {
  function(current, total) {
    percent <- current / total
    filled <- round(width * percent)
    bar <- paste0("[", 
                  paste(rep("=", filled), collapse = ""),
                  paste(rep("-", width - filled), collapse = ""),
                  "]")
    return(list(bar = bar, percent = round(percent * 100, 1)))
  }
}

# Function to format time duration
format_duration <- function(seconds) {
  if (seconds < 60) {
    return(paste(round(seconds, 1), "sec"))
  } else if (seconds < 3600) {
    return(paste(round(seconds / 60, 1), "min"))
  } else {
    return(paste(round(seconds / 3600, 1), "hr"))
  }
}

# Enhanced progress display function
show_mcmc_progress <- function(chain_id = NULL, current, total, start_time, 
                              chain_total = NULL, show_bar = TRUE) {
  
  elapsed_sec <- as.numeric(Sys.time() - start_time, units = "secs")
  percent <- round(100 * current / total, 1)
  
  # Calculate rates and ETA
  if (elapsed_sec > 0) {
    rate <- current / elapsed_sec
    eta_sec <- (total - current) / rate
  } else {
    rate <- 0
    eta_sec <- Inf
  }
  
  # Create progress bar if requested
  if (show_bar) {
    progress_bar <- create_progress_bar(30)
    bar_info <- progress_bar(current, total)
    bar_display <- bar_info$bar
  } else {
    bar_display <- ""
  }
  
  # Format the progress message
  if (!is.null(chain_id)) {
    chain_info <- paste0("Chain ", chain_id, " | ")
  } else {
    chain_info <- ""
  }
  
  if (!is.null(chain_total)) {
    chain_info <- paste0(chain_info, "Overall: ", chain_total, " chains | ")
  }
  
  message <- paste0("\r", chain_info,
                   if(show_bar) paste0(bar_display, " ") else "",
                   percent, "% ",
                   "(", current, "/", total, ") | ",
                   "Rate: ", round(rate, 1), " iter/sec | ",
                   "Elapsed: ", format_duration(elapsed_sec), " | ",
                   "ETA: ", if(is.finite(eta_sec)) format_duration(eta_sec) else "∞")
  
  cat(message)
  flush.console()
}

# Function to monitor blavaan fitting progress
monitor_blavaan_progress <- function(n_chains, burnin, sample, thin = 1) {
  
  total_per_chain <- burnin + sample * thin
  total_iterations <- n_chains * total_per_chain
  
  cat("=== MCMC PROGRESS MONITORING ===\n")
  cat("Configuration:\n")
  cat("- Chains:", n_chains, "\n")
  cat("- Burnin:", burnin, "\n") 
  cat("- Samples:", sample, "\n")
  cat("- Thinning:", thin, "\n")
  cat("- Total iterations per chain:", total_per_chain, "\n")
  cat("- Total iterations (all chains):", total_iterations, "\n")
  cat(paste(rep("=", 60), collapse = ""), "\n")
  
  return(list(
    total_per_chain = total_per_chain,
    total_iterations = total_iterations,
    start_time = Sys.time()
  ))
}

# Function to display final progress summary
show_final_summary <- function(start_time, end_time, n_chains, total_iterations, 
                              n_cores) {
  
  elapsed_time <- end_time - start_time
  elapsed_sec <- as.numeric(elapsed_time, units = "secs")
  
  cat("\n", paste(rep("=", 60), collapse = ""), "\n")
  cat("=== MCMC SAMPLING COMPLETED ===\n")
  cat("Start time:", format(start_time), "\n")
  cat("End time:", format(end_time), "\n")
  cat("Total elapsed time:", format_duration(elapsed_sec), "\n")
  
  # Performance metrics
  if (elapsed_sec > 0) {
    total_rate <- total_iterations / elapsed_sec
    per_chain_rate <- total_rate / n_chains
    per_core_rate <- total_rate / n_cores
    
    cat("\nPerformance metrics:\n")
    cat("- Overall sampling rate:", round(total_rate, 1), "iterations/sec\n")
    cat("- Per-chain rate:", round(per_chain_rate, 1), "iterations/sec\n")
    cat("- Per-core efficiency:", round(per_core_rate, 1), "iterations/sec\n")
    
    # Parallel efficiency estimate
    theoretical_max <- per_chain_rate * n_cores
    actual_efficiency <- (total_rate / theoretical_max) * 100
    cat("- Parallel efficiency:", round(actual_efficiency, 1), "%\n")
  }
  
  cat(paste(rep("=", 60), collapse = ""), "\n\n")
}

# Function to create a simple spinning progress indicator
create_spinner <- function() {
  chars <- c("|", "/", "-", "\\")
  counter <- 0
  
  function() {
    counter <<- (counter %% length(chars)) + 1
    return(chars[counter])
  }
}

# Example usage function
demo_progress_monitor <- function() {
  cat("=== PROGRESS MONITOR DEMO ===\n")
  
  # Simulate MCMC progress
  total <- 100
  start_time <- Sys.time()
  
  for (i in 1:total) {
    Sys.sleep(0.05)  # Simulate computation time
    show_mcmc_progress(chain_id = 1, current = i, total = total, 
                      start_time = start_time, chain_total = 3)
  }
  
  cat("\nDemo completed!\n")
}

cat("Progress monitoring functions loaded successfully!\n")
cat("Available functions:\n")
cat("- show_mcmc_progress(): Display real-time progress\n")
cat("- monitor_blavaan_progress(): Setup monitoring for blavaan\n")
cat("- show_final_summary(): Display completion summary\n")
cat("- demo_progress_monitor(): Run a demonstration\n\n")
