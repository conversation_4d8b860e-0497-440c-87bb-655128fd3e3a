# =============================================================================
# THREE-FACTOR MEASUREMENT INVARIANCE TESTING
# Self-Control Scale Across Ages 11, 14, and 17
# Simple 3-Factor Structure (No Bifactor)
# =============================================================================

library(pacman)
p_load(lavaan, semTools, dplyr, psych, ggplot2, knitr, haven)

# =============================================================================
# 0. DATA LOADING AND PREPARATION
# =============================================================================

cat("=== THREE-FACTOR MEASUREMENT INVARIANCE TESTING ===\n")
cat("Self-Control Scale: Ages 11, 14, 17\n")
cat("Simple 3-factor structure (no bifactor)\n\n")

# Load data
cat("Loading data...\n")
data_path <- "/home/<USER>/dissertation_folder/data"

# Load original data and run recoding scripts
merged_data <- readRDS(file.path(data_path, "merged1203.rds"))
cat("Loaded original dataset. Dimensions:", dim(merged_data), "\n")

# Check if recoded variables already exist
sc11_test_vars <- c("sc11_think_act", "sc11_considerate", "sc11_sharing", "sc11_helpful")
if(!all(sc11_test_vars %in% names(merged_data))) {
  cat("Running recoding scripts...\n")
  source("scripts/recode_self_control.R")
  source("scripts/recode_self_control_age14.R") 
  source("scripts/recode_self_control_age17.R")
  cat("Recoding completed.\n")
} else {
  cat("Recoded variables already available.\n")
}

# Define the 12 consistent self-control variables across all waves
core_items <- c(
  "think_act",      # Think things out before acting
  "considerate",    # Being considerate of other people's feelings  
  "sharing",        # Sharing readily with other children
  "helpful",        # Being helpful if someone is hurt
  "volunteer_help", # Often volunteering to help others
  "task_completion",# Sees tasks through to the end, good attention span
  "obedient",       # Generally obedient
  "distracted",     # Is easily distracted, concentration wanders
  "temper",         # Often has temper tantrums or hot tempers
  "restless",       # Child is restless, overactive, cannot stay still for long
  "fidgeting",      # Child is constantly fidgeting or squirming
  "lying"           # Lying or cheating
)

# Create variable names for each wave
sc11_vars <- paste0("sc11_", core_items)
sc14_vars <- paste0("sc14_", core_items)
sc17_vars <- paste0("sc17_", core_items)

# Check availability and create analysis dataset
all_vars <- c(sc11_vars, sc14_vars, sc17_vars)
available_vars <- all_vars[all_vars %in% names(merged_data)]

cat("Variable availability:", length(available_vars), "/", length(all_vars), "\n")

# Create wide format dataset
wide_data <- merged_data[, available_vars, drop = FALSE]
cat("Analysis dataset created with", nrow(wide_data), "observations\n\n")

# =============================================================================
# 1. DEFINE THREE-FACTOR MODEL STRUCTURE
# =============================================================================

cat("=== THREE-FACTOR MODEL SPECIFICATION ===\n")

# Factor assignments (same as bifactor but without general factor)
executive_items <- c("task_completion", "distracted", "fidgeting", "think_act", "restless")
selfcent_items <- c("considerate", "sharing", "helpful", "volunteer_help")
temper_items <- c("temper", "obedient", "lying")

cat("Factor structure:\n")
cat("Executive factor:", paste(executive_items, collapse = ", "), "\n")
cat("Self-Centered factor:", paste(selfcent_items, collapse = ", "), "\n")
cat("Temper factor:", paste(temper_items, collapse = ", "), "\n\n")

# Three-factor model syntax function
create_3factor_syntax <- function(age_suffix, label_suffix = "") {
  
  # Specific factor items for this wave
  exec_items_age <- paste0("sc", age_suffix, "_", executive_items)
  selfcent_items_age <- paste0("sc", age_suffix, "_", selfcent_items)
  temper_items_age <- paste0("sc", age_suffix, "_", temper_items)
  
  syntax <- paste0(
    "# Three correlated factors\n",
    "Executive", label_suffix, " =~ ", paste(exec_items_age, collapse = " + "), "\n",
    "SelfCent", label_suffix, " =~ ", paste(selfcent_items_age, collapse = " + "), "\n",
    "Temper", label_suffix, " =~ ", paste(temper_items_age, collapse = " + "), "\n"
    # Note: Factors are allowed to correlate freely (no orthogonality constraints)
  )
  
  return(syntax)
}

# =============================================================================
# 2. PER-WAVE MODEL TESTING
# =============================================================================

cat("=== PER-WAVE MODEL TESTING ===\n")

# Function to test individual wave models with 3-factor structure
test_3factor_wave <- function(age, age_vars, data = wide_data) {
  
  cat("\n--- TESTING AGE", age, "THREE-FACTOR MODEL ---\n")
  
  # Extract wave-specific data
  wave_data <- data[, age_vars, drop = FALSE]
  complete_data <- na.omit(wave_data)
  
  cat("Sample size:", nrow(complete_data), "\n")
  
  # Check minimum sample size
  if(nrow(complete_data) < 100) {
    cat("❌ INSUFFICIENT SAMPLE SIZE (< 100)\n")
    return(list(fit = NULL, converged = FALSE, reason = "insufficient_sample"))
  }
  
  # Create single-wave three-factor model syntax
  items_clean <- gsub(paste0("sc", gsub("Age", "", age), "_"), "", age_vars)
  
  threefactor_syntax <- paste0(
    "# Three correlated factors\n",
    "Executive =~ ", paste(age_vars[items_clean %in% executive_items], collapse = " + "), "\n",
    "SelfCent =~ ", paste(age_vars[items_clean %in% selfcent_items], collapse = " + "), "\n",
    "Temper =~ ", paste(age_vars[items_clean %in% temper_items], collapse = " + "), "\n"
  )
  
  cat("Model syntax:\n", threefactor_syntax, "\n")
  
  # Fit the model with timeout protection
  cat("Fitting three-factor model...\n")
  fit <- tryCatch({
    cfa(
      model = threefactor_syntax,
      data = complete_data,
      estimator = "WLSMV",
      ordered = age_vars,
      std.lv = TRUE,
      control = list(iter.max = 1000, eval.max = 2000)
    )
  }, error = function(e) {
    cat("❌ ERROR fitting model:", e$message, "\n")
    return(NULL)
  })
  
  # Check convergence
  if(is.null(fit)) {
    cat("❌ Model fitting failed completely\n")
    return(list(fit = NULL, converged = FALSE, reason = "fitting_failed"))
  }
  
  converged <- tryCatch({
    lavInspect(fit, "converged")
  }, error = function(e) {
    cat("❌ Error checking convergence:", e$message, "\n")
    return(FALSE)
  })
  
  cat("Converged:", converged, "\n")
  
  if(converged) {
    # Extract fit measures
    fit_measures <- fitMeasures(fit, c(
      "chisq.scaled", "df", "pvalue.scaled",
      "cfi.scaled", "tli.scaled", "rmsea.scaled", 
      "rmsea.ci.lower.scaled", "rmsea.ci.upper.scaled", "srmr"
    ))
    
    cat("Three-Factor Model Fit:\n")
    cat("  χ² (scaled) =", round(fit_measures["chisq.scaled"], 3), 
        ", df =", fit_measures["df"], 
        ", p =", round(fit_measures["pvalue.scaled"], 3), "\n")
    cat("  CFI (scaled) =", round(fit_measures["cfi.scaled"], 3), "\n")
    cat("  TLI (scaled) =", round(fit_measures["tli.scaled"], 3), "\n")
    cat("  RMSEA (scaled) =", round(fit_measures["rmsea.scaled"], 3),
        " [", round(fit_measures["rmsea.ci.lower.scaled"], 3), ", ",
        round(fit_measures["rmsea.ci.upper.scaled"], 3), "]\n")
    cat("  SRMR =", round(fit_measures["srmr"], 3), "\n")
    
    # Quick factor correlations
    tryCatch({
      factor_cors <- lavInspect(fit, "cor.lv")
      if(is.matrix(factor_cors)) {
        cat("\nFactor correlations:\n")
        print(round(factor_cors, 3))
      }
    }, error = function(e) {
      cat("Could not extract factor correlations\n")
    })
    
  } else {
    cat("❌ Model did not converge\n")
  }
  
  return(list(fit = fit, converged = converged))
}

# Test each wave individually
cat("Testing individual wave models...\n")

age11_result <- test_3factor_wave("11", sc11_vars)
age14_result <- test_3factor_wave("14", sc14_vars)
age17_result <- test_3factor_wave("17", sc17_vars)

# =============================================================================
# 3. MULTI-GROUP MEASUREMENT INVARIANCE TESTING
# =============================================================================

cat("\n=== MULTI-GROUP MEASUREMENT INVARIANCE TESTING ===\n")
cat("Testing sequence for ordinal data:\n")
cat("1. Configural invariance (same factor pattern)\n")
cat("2. Threshold invariance (equal thresholds)\n") 
cat("3. Strong invariance (+ equal loadings)\n")
cat("4. Strict invariance (+ equal residual variances)\n\n")

# Create long format dataset for multi-group analysis
cat("Creating long format dataset...\n")

# Create participant ID if not exists
if(!"id" %in% names(merged_data)) {
  wide_data$id <- 1:nrow(wide_data)
} else {
  wide_data$id <- merged_data$id[1:nrow(wide_data)]
}

# Reshape to long format
long_data_list <- list()

for(i in 1:nrow(wide_data)) {
  for(age in c(11, 14, 17)) {
    age_row <- data.frame(
      id = wide_data$id[i],
      age = age,
      stringsAsFactors = FALSE
    )
    
    # Add variables for this age
    age_vars <- paste0("sc", age, "_", core_items)
    for(j in 1:length(core_items)) {
      var_name <- core_items[j]
      age_var_name <- age_vars[j]
      if(age_var_name %in% names(wide_data)) {
        age_row[[var_name]] <- wide_data[[age_var_name]][i]
      } else {
        age_row[[var_name]] <- NA
      }
    }
    
    long_data_list[[length(long_data_list) + 1]] <- age_row
  }
}

# Combine into long dataset
long_data <- do.call(rbind, long_data_list)
long_data$age <- factor(long_data$age, levels = c(11, 14, 17), 
                       labels = c("Age11", "Age14", "Age17"))

# Remove completely missing observations
complete_rows <- rowSums(!is.na(long_data[, core_items])) > 0
long_data <- long_data[complete_rows, ]

cat("Long format dataset created:\n")
cat("  Total observations:", nrow(long_data), "\n")
cat("  Age groups:", table(long_data$age), "\n")

# Define single-group three-factor model for multi-group analysis
single_group_syntax <- paste0(
  "# Three correlated factors\n",
  "Executive =~ ", paste(executive_items, collapse = " + "), "\n",
  "SelfCent =~ ", paste(selfcent_items, collapse = " + "), "\n",
  "Temper =~ ", paste(temper_items, collapse = " + "), "\n"
)

cat("\nThree-factor model for invariance testing:\n")
cat(single_group_syntax, "\n")

# =============================================================================
# MODEL 1: CONFIGURAL INVARIANCE
# =============================================================================

cat("\n--- MODEL 1: CONFIGURAL INVARIANCE ---\n")
cat("Same factor pattern, all parameters free across waves\n")

model_configural <- cfa(
  model = single_group_syntax,
  data = long_data,
  group = "age",
  estimator = "WLSMV",
  ordered = core_items,
  std.lv = TRUE
)

if(lavInspect(model_configural, "converged")) {
  cat("✓ Configural model converged successfully\n")
  
  fit_configural <- fitMeasures(model_configural, c(
    "chisq.scaled", "df", "pvalue.scaled",
    "cfi.scaled", "tli.scaled", "rmsea.scaled",
    "rmsea.ci.lower.scaled", "rmsea.ci.upper.scaled", "srmr"
  ))
  
  cat("Configural Model Fit:\n")
  cat("  χ² (scaled) =", round(fit_configural["chisq.scaled"], 3),
      ", df =", fit_configural["df"],
      ", p =", round(fit_configural["pvalue.scaled"], 3), "\n")
  cat("  CFI (scaled) =", round(fit_configural["cfi.scaled"], 3), "\n")
  cat("  TLI (scaled) =", round(fit_configural["tli.scaled"], 3), "\n")
  cat("  RMSEA (scaled) =", round(fit_configural["rmsea.scaled"], 3),
      " [", round(fit_configural["rmsea.ci.lower.scaled"], 3), ", ",
      round(fit_configural["rmsea.ci.upper.scaled"], 3), "]\n")
  cat("  SRMR =", round(fit_configural["srmr"], 3), "\n")
  
} else {
  cat("✗ Configural model did not converge\n")
  stop("Cannot proceed without configural model")
}

# =============================================================================
# MODEL 2: THRESHOLD INVARIANCE  
# =============================================================================

cat("\n--- MODEL 2: THRESHOLD INVARIANCE ---\n")
cat("Equal thresholds across waves, loadings still free\n")

model_threshold <- cfa(
  model = single_group_syntax,
  data = long_data,
  group = "age",
  estimator = "WLSMV",
  ordered = core_items,
  group.equal = "thresholds",
  std.lv = TRUE
)

if(lavInspect(model_threshold, "converged")) {
  cat("✓ Threshold invariance model converged successfully\n")
  
  fit_threshold <- fitMeasures(model_threshold, c(
    "chisq.scaled", "df", "pvalue.scaled",
    "cfi.scaled", "tli.scaled", "rmsea.scaled",
    "rmsea.ci.lower.scaled", "rmsea.ci.upper.scaled", "srmr"
  ))
  
  cat("Threshold Invariance Model Fit:\n")
  cat("  χ² (scaled) =", round(fit_threshold["chisq.scaled"], 3),
      ", df =", fit_threshold["df"],
      ", p =", round(fit_threshold["pvalue.scaled"], 3), "\n")
  cat("  CFI (scaled) =", round(fit_threshold["cfi.scaled"], 3), "\n")
  cat("  TLI (scaled) =", round(fit_threshold["tli.scaled"], 3), "\n")
  cat("  RMSEA (scaled) =", round(fit_threshold["rmsea.scaled"], 3),
      " [", round(fit_threshold["rmsea.ci.lower.scaled"], 3), ", ",
      round(fit_threshold["rmsea.ci.upper.scaled"], 3), "]\n")
  cat("  SRMR =", round(fit_threshold["srmr"], 3), "\n")
  
  # Compare to configural
  delta_cfi <- fit_threshold["cfi.scaled"] - fit_configural["cfi.scaled"]
  delta_rmsea <- fit_threshold["rmsea.scaled"] - fit_configural["rmsea.scaled"]
  
  cat("Model comparison (Threshold vs Configural):\n")
  cat("  ΔCFI =", round(delta_cfi, 4), "\n")
  cat("  ΔRMSEA =", round(delta_rmsea, 4), "\n")
  
  if(delta_cfi <= -0.01 || delta_rmsea >= 0.015) {
    cat("  → Threshold invariance NOT supported\n")
  } else {
    cat("  → Threshold invariance SUPPORTED\n")
  }
  
} else {
  cat("✗ Threshold invariance model did not converge\n")
}

# =============================================================================
# MODEL 3: STRONG INVARIANCE (THRESHOLD + LOADING)
# =============================================================================

cat("\n--- MODEL 3: STRONG INVARIANCE ---\n") 
cat("Equal thresholds AND loadings across waves\n")

model_strong <- cfa(
  model = single_group_syntax,
  data = long_data,
  group = "age", 
  estimator = "WLSMV",
  ordered = core_items,
  group.equal = c("thresholds", "loadings"),
  std.lv = TRUE
)

if(lavInspect(model_strong, "converged")) {
  cat("✓ Strong invariance model converged successfully\n")
  
  fit_strong <- fitMeasures(model_strong, c(
    "chisq.scaled", "df", "pvalue.scaled",
    "cfi.scaled", "tli.scaled", "rmsea.scaled",
    "rmsea.ci.lower.scaled", "rmsea.ci.upper.scaled", "srmr"
  ))
  
  cat("Strong Invariance Model Fit:\n")
  cat("  χ² (scaled) =", round(fit_strong["chisq.scaled"], 3),
      ", df =", fit_strong["df"],
      ", p =", round(fit_strong["pvalue.scaled"], 3), "\n")
  cat("  CFI (scaled) =", round(fit_strong["cfi.scaled"], 3), "\n") 
  cat("  TLI (scaled) =", round(fit_strong["tli.scaled"], 3), "\n")
  cat("  RMSEA (scaled) =", round(fit_strong["rmsea.scaled"], 3),
      " [", round(fit_strong["rmsea.ci.lower.scaled"], 3), ", ",
      round(fit_strong["rmsea.ci.upper.scaled"], 3), "]\n")
  cat("  SRMR =", round(fit_strong["srmr"], 3), "\n")
  
  # Compare to threshold model
  if(exists("fit_threshold")) {
    delta_cfi <- fit_strong["cfi.scaled"] - fit_threshold["cfi.scaled"]
    delta_rmsea <- fit_strong["rmsea.scaled"] - fit_threshold["rmsea.scaled"]
    
    cat("Model comparison (Strong vs Threshold):\n")
    cat("  ΔCFI =", round(delta_cfi, 4), "\n")
    cat("  ΔRMSEA =", round(delta_rmsea, 4), "\n")
    
    if(delta_cfi <= -0.01 || delta_rmsea >= 0.015) {
      cat("  → Strong invariance NOT supported\n")
    } else {
      cat("  → Strong invariance SUPPORTED\n")
    }
  }
  
} else {
  cat("✗ Strong invariance model did not converge\n")
}

# =============================================================================
# MODEL 4: STRICT INVARIANCE
# =============================================================================

cat("\n--- MODEL 4: STRICT INVARIANCE ---\n")
cat("Equal thresholds, loadings, AND residual variances\n")

model_strict <- cfa(
  model = single_group_syntax,
  data = long_data,
  group = "age",
  estimator = "WLSMV", 
  ordered = core_items,
  group.equal = c("thresholds", "loadings", "residuals"),
  std.lv = TRUE
)

if(lavInspect(model_strict, "converged")) {
  cat("✓ Strict invariance model converged successfully\n")
  
  fit_strict <- fitMeasures(model_strict, c(
    "chisq.scaled", "df", "pvalue.scaled",
    "cfi.scaled", "tli.scaled", "rmsea.scaled",
    "rmsea.ci.lower.scaled", "rmsea.ci.upper.scaled", "srmr"
  ))
  
  cat("Strict Invariance Model Fit:\n")
  cat("  χ² (scaled) =", round(fit_strict["chisq.scaled"], 3),
      ", df =", fit_strict["df"],
      ", p =", round(fit_strict["pvalue.scaled"], 3), "\n")
  cat("  CFI (scaled) =", round(fit_strict["cfi.scaled"], 3), "\n")
  cat("  TLI (scaled) =", round(fit_strict["tli.scaled"], 3), "\n")
  cat("  RMSEA (scaled) =", round(fit_strict["rmsea.scaled"], 3),
      " [", round(fit_strict["rmsea.ci.lower.scaled"], 3), ", ",
      round(fit_strict["rmsea.ci.upper.scaled"], 3), "]\n")
  cat("  SRMR =", round(fit_strict["srmr"], 3), "\n")
  
  # Compare to strong model
  if(exists("fit_strong")) {
    delta_cfi <- fit_strict["cfi.scaled"] - fit_strong["cfi.scaled"]
    delta_rmsea <- fit_strict["rmsea.scaled"] - fit_strong["rmsea.scaled"]
    
    cat("Model comparison (Strict vs Strong):\n")
    cat("  ΔCFI =", round(delta_cfi, 4), "\n")
    cat("  ΔRMSEA =", round(delta_rmsea, 4), "\n")
    
    if(delta_cfi <= -0.01 || delta_rmsea >= 0.015) {
      cat("  → Strict invariance NOT supported\n")
    } else {
      cat("  → Strict invariance SUPPORTED\n")
    }
  }
  
} else {
  cat("✗ Strict invariance model did not converge\n")
}

# =============================================================================
# 5. MODEL COMPARISON SUMMARY
# =============================================================================

cat("\n=== MODEL COMPARISON SUMMARY ===\n")

# Create comparison table
models_list <- list()
fits_list <- list()

if(lavInspect(model_configural, "converged")) {
  models_list[["Configural"]] <- model_configural
  fits_list[["Configural"]] <- fit_configural[c("chisq.scaled", "df", "cfi.scaled", "tli.scaled", "rmsea.scaled", "srmr")]
}

if(exists("model_threshold") && lavInspect(model_threshold, "converged")) {
  models_list[["Threshold"]] <- model_threshold
  fits_list[["Threshold"]] <- fit_threshold[c("chisq.scaled", "df", "cfi.scaled", "tli.scaled", "rmsea.scaled", "srmr")]
}

if(exists("model_strong") && lavInspect(model_strong, "converged")) {
  models_list[["Strong"]] <- model_strong
  fits_list[["Strong"]] <- fit_strong[c("chisq.scaled", "df", "cfi.scaled", "tli.scaled", "rmsea.scaled", "srmr")]
}

if(exists("model_strict") && lavInspect(model_strict, "converged")) {
  models_list[["Strict"]] <- model_strict
  fits_list[["Strict"]] <- fit_strict[c("chisq.scaled", "df", "cfi.scaled", "tli.scaled", "rmsea.scaled", "srmr")]
}

# Create comparison table
if(length(fits_list) > 0) {
  comparison_table <- data.frame(
    Model = names(fits_list),
    ChiSq = sapply(fits_list, function(x) round(x["chisq.scaled"], 3)),
    df = sapply(fits_list, function(x) x["df"]),
    CFI = sapply(fits_list, function(x) round(x["cfi.scaled"], 3)),
    TLI = sapply(fits_list, function(x) round(x["tli.scaled"], 3)),
    RMSEA = sapply(fits_list, function(x) round(x["rmsea.scaled"], 3)),
    SRMR = sapply(fits_list, function(x) round(x["srmr"], 3))
  )
  
  cat("\nModel Comparison Table:\n")
  print(comparison_table)
}

# Chi-square difference tests
cat("\n--- Chi-Square Difference Tests ---\n")
if(length(models_list) > 1) {
  for(i in 2:length(models_list)) {
    current_name <- names(models_list)[i]
    previous_name <- names(models_list)[i-1]
    
    cat("Testing", current_name, "vs", previous_name, ":\n")
    
    tryCatch({
      diff_test <- anova(models_list[[i-1]], models_list[[i]])
      print(diff_test)
    }, error = function(e) {
      cat("  Error in chi-square difference test:", e$message, "\n")
    })
    cat("\n")
  }
}

# =============================================================================
# 6. FINAL SUMMARY AND RECOMMENDATIONS
# =============================================================================

cat("=== FINAL SUMMARY ===\n\n")

cat("Three-Factor Measurement Invariance Testing Summary:\n")
cat("Scale: Self-Control Scale (3-factor structure)\n")
cat("Waves: Age 11, 14, 17\n")
cat("Items: 12 ordinal items across 3 factors\n")
cat("Estimator: WLSMV with theta parameterization\n\n")

cat("Factor Structure:\n")
cat("- Executive: task_completion, distracted, fidgeting, think_act, restless\n")
cat("- Self-Centered: considerate, sharing, helpful, volunteer_help\n")
cat("- Temper: temper, obedient, lying\n\n")

# Determine highest level of invariance achieved
converged_models <- names(models_list)
cat("✅ CONVERGED MODELS:", paste(converged_models, collapse = ", "), "\n")

if(length(converged_models) > 0) {
  highest_model <- converged_models[length(converged_models)]
  cat("✅ HIGHEST ACHIEVED INVARIANCE LEVEL:", highest_model, "\n\n")
  
  if(highest_model == "Strict") {
    cat("🎉 EXCELLENT: Full strict invariance achieved!\n")
    cat("Complete measurement invariance across all three ages.\n")
    cat("You can confidently compare latent means and conduct longitudinal analyses.\n")
    
  } else if(highest_model == "Strong") {
    cat("✅ VERY GOOD: Strong invariance achieved!\n")
    cat("Threshold and loading invariance established.\n")
    cat("Latent mean comparisons are valid across ages.\n")
    
  } else if(highest_model == "Threshold") {
    cat("⚠️ PARTIAL: Only threshold invariance achieved.\n")
    cat("Same response patterns but different factor loadings across ages.\n")
    cat("Interpret latent mean comparisons cautiously.\n")
    
  } else {
    cat("⚠️ MINIMAL: Only configural invariance achieved.\n")
    cat("Same factor structure but different parameters across ages.\n")
    cat("Focus on structural similarities rather than mean comparisons.\n")
  }
}

cat("\n=== THREE-FACTOR ANALYSIS COMPLETE ===\n") 
