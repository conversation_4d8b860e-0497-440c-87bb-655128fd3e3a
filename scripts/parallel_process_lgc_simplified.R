# =============================================================================
# SIMPLIFIED PARALLEL-PROCESS LATENT GROWTH CURVE MODEL
# Self-Control (Factor Scores) + Fight with Others
# Ages 11, 14, and 17
# =============================================================================

library(pacman)
p_load(lavaan, semTools, dplyr, psych, ggplot2, knitr, haven)

cat("=== SIMPLIFIED PARALLEL-PROCESS LATENT GROWTH CURVE MODEL ===\n")
cat("Using factor scores approach for better identification\n")
cat("Ages 11, 14, and 17\n\n")

# =============================================================================
# STEP 1: DATA LOADING AND PREPARATION
# =============================================================================

cat("Loading data...\n")
data_path <- "/home/<USER>/dissertation_folder/data"
merged_data <- readRDS(file.path(data_path, "merged1203.rds"))

# Check if recoded self-control variables exist
sc11_test_vars <- c("sc11_think_act", "sc11_considerate", "sc11_sharing", "sc11_helpful")
if(!all(sc11_test_vars %in% names(merged_data))) {
  cat("Running self-control recoding scripts...\n")
  source("scripts/recode_self_control.R")
  source("scripts/recode_self_control_age14.R") 
  source("scripts/recode_self_control_age17.R")
} else {
  cat("Self-control variables already available.\n")
}

# Check for fight variables and recode them
fight_vars_original <- c("epsdfb00", "fpsdfb00", "gpsdfb00")

# Recode fight variables (from user specifications)
if("epsdfb00" %in% names(merged_data)) {
  merged_data$agr5_fig <- ifelse(merged_data$epsdfb00 %in% c(-1, 4), NA,
                                 ifelse(merged_data$epsdfb00 == 1, 0,
                                        ifelse(merged_data$epsdfb00 == 2, 1,
                                               ifelse(merged_data$epsdfb00 == 3, 2, NA))))
}

if("fpsdfb00" %in% names(merged_data)) {
  merged_data$agr6_fig <- ifelse(merged_data$fpsdfb00 %in% c(-1, -9), NA,
                                 ifelse(merged_data$fpsdfb00 == 1, 0,
                                        ifelse(merged_data$fpsdfb00 == 2, 1,
                                               ifelse(merged_data$fpsdfb00 == 3, 2, NA))))
}

if("gpsdfb00" %in% names(merged_data)) {
  merged_data$agr7_fig <- ifelse(merged_data$gpsdfb00 %in% c(-1, 4), NA,
                                 ifelse(merged_data$gpsdfb00 == 1, 0,
                                        ifelse(merged_data$gpsdfb00 == 2, 1,
                                               ifelse(merged_data$gpsdfb00 == 3, 2, NA))))
}

cat("Fight variables recoded successfully.\n")

# =============================================================================
# STEP 2: CREATE SELF-CONTROL FACTOR SCORES
# =============================================================================

cat("\n=== CREATING SELF-CONTROL FACTOR SCORES ===\n")

# Define the core 12 items used in previous analyses
core_sc_items <- c("task_completion", "distracted", "fidgeting", "think_act", "restless",
                   "considerate", "sharing", "helpful", "volunteer_help",
                   "temper", "obedient", "lying")

# Function to create factor scores for each wave
create_factor_scores <- function(age, items) {
  cat("Creating factor scores for age", age, "...\n")
  
  # Get variable names for this age
  age_vars <- paste0("sc", age, "_", items)
  available_vars <- age_vars[age_vars %in% names(merged_data)]
  
  if(length(available_vars) < 6) {
    cat("Warning: Only", length(available_vars), "variables available for age", age, "\n")
    return(rep(NA, nrow(merged_data)))
  }
  
  # Extract data for this wave
  wave_data <- merged_data[, available_vars, drop = FALSE]
  
  # Calculate factor scores using principal components
  # Remove rows with all missing values
  complete_cases <- complete.cases(wave_data)
  
  if(sum(complete_cases) < 100) {
    cat("Warning: Insufficient complete cases for age", age, "\n")
    return(rep(NA, nrow(merged_data)))
  }
  
  # PCA on complete cases
  pca_result <- tryCatch({
    prcomp(wave_data[complete_cases, ], center = TRUE, scale. = TRUE, na.action = na.omit)
  }, error = function(e) {
    cat("PCA failed for age", age, ":", e$message, "\n")
    return(NULL)
  })
  
  if(is.null(pca_result)) {
    return(rep(NA, nrow(merged_data)))
  }
  
  # Use first principal component as general self-control factor
  factor_scores <- rep(NA, nrow(merged_data))
  
  # Predict scores for all cases (including those with some missing data)
  col_means <- colMeans(wave_data, na.rm = TRUE)
  for(i in 1:nrow(merged_data)) {
    row_data <- wave_data[i, ]
    if(sum(!is.na(row_data)) >= 3) {  # Need at least 3 non-missing items
      # Impute missing values with mean for prediction
      missing_cols <- is.na(row_data)
      if(any(missing_cols)) {
        row_data[missing_cols] <- col_means[missing_cols]
      }
      # Scale using PCA centers and scales
      row_scaled <- scale(row_data, center = pca_result$center, scale = pca_result$scale)
      # Predict PC1 score
      factor_scores[i] <- as.numeric(row_scaled %*% pca_result$rotation[, 1])
    }
  }
  
  cat("Factor scores created for age", age, ": mean =", round(mean(factor_scores, na.rm = TRUE), 3),
      ", sd =", round(sd(factor_scores, na.rm = TRUE), 3), "\n")
  cat("Valid scores:", sum(!is.na(factor_scores)), "/", length(factor_scores), "\n")
  
  return(factor_scores)
}

# Create factor scores for each wave
merged_data$sc_factor_11 <- create_factor_scores(11, core_sc_items)
merged_data$sc_factor_14 <- create_factor_scores(14, core_sc_items)
merged_data$sc_factor_17 <- create_factor_scores(17, core_sc_items)

# =============================================================================
# STEP 3: CREATE ANALYSIS DATASET
# =============================================================================

cat("\n=== CREATING ANALYSIS DATASET ===\n")

# Analysis variables
analysis_vars <- c("sc_factor_11", "sc_factor_14", "sc_factor_17",
                   "agr5_fig", "agr6_fig", "agr7_fig")

# Check availability
available_analysis_vars <- analysis_vars[analysis_vars %in% names(merged_data)]
cat("Available analysis variables:", paste(available_analysis_vars, collapse = ", "), "\n")

# Create analysis dataset
analysis_data <- merged_data[, available_analysis_vars, drop = FALSE]
analysis_data$id <- 1:nrow(analysis_data)

# Data completeness check
cat("\nData completeness:\n")
for(var in available_analysis_vars) {
  complete_n <- sum(!is.na(analysis_data[[var]]))
  cat(var, ":", complete_n, "/", nrow(analysis_data), 
      "(", round(100 * complete_n / nrow(analysis_data), 1), "%)\n")
}

# Complete cases for each domain
sc_complete <- rowSums(!is.na(analysis_data[, c("sc_factor_11", "sc_factor_14", "sc_factor_17")])) >= 2
fight_complete <- rowSums(!is.na(analysis_data[, c("agr5_fig", "agr6_fig", "agr7_fig")])) >= 2
both_complete <- sc_complete & fight_complete

cat("\nAnalysis readiness:\n")
cat("At least 2 self-control waves:", sum(sc_complete), "\n")
cat("At least 2 fight waves:", sum(fight_complete), "\n")
cat("Both domains (2+ waves each):", sum(both_complete), "\n")

# =============================================================================
# STEP 4: UNIVARIATE GROWTH MODELS
# =============================================================================

cat("\n=== UNIVARIATE GROWTH MODELS ===\n")

# Model 1: Self-Control LGC
cat("1. Self-Control Latent Growth Curve Model\n")
sc_lgc_syntax <- '
# Growth factors
i_sc =~ 1*sc_factor_11 + 1*sc_factor_14 + 1*sc_factor_17
s_sc =~ 0*sc_factor_11 + 1*sc_factor_14 + 2*sc_factor_17

# Growth means
i_sc ~ 1
s_sc ~ 1

# Residual variances
sc_factor_11 ~~ sc_factor_11
sc_factor_14 ~~ sc_factor_14
sc_factor_17 ~~ sc_factor_17
'

fit_sc_lgc <- tryCatch({
  growth(sc_lgc_syntax, data = analysis_data, missing = "ML", estimator = "MLR")
}, error = function(e) {
  cat("Error fitting self-control LGC:", e$message, "\n")
  return(NULL)
})

if(!is.null(fit_sc_lgc) && lavInspect(fit_sc_lgc, "converged")) {
  cat("✓ Self-control LGC converged\n")
  
  # Growth parameters
  sc_params <- parameterEstimates(fit_sc_lgc)
  sc_intercept <- sc_params[sc_params$lhs == "i_sc" & sc_params$op == "~1", ]
  sc_slope <- sc_params[sc_params$lhs == "s_sc" & sc_params$op == "~1", ]
  
  cat("Self-control development:\n")
  if(nrow(sc_intercept) > 0) {
    cat("  Intercept =", round(sc_intercept$est, 3), "(p =", round(sc_intercept$pvalue, 3), ")\n")
  }
  if(nrow(sc_slope) > 0) {
    cat("  Slope =", round(sc_slope$est, 3), "(p =", round(sc_slope$pvalue, 3), ")\n")
    if(!is.na(sc_slope$pvalue) && sc_slope$pvalue < 0.05) {
      if(sc_slope$est > 0) {
        cat("  → Self-control problems INCREASE over time\n")
      } else {
        cat("  → Self-control IMPROVES over time\n")
      }
    } else {
      cat("  → No significant change in self-control\n")
    }
  }
  
  # Fit measures
  fit_sc <- fitMeasures(fit_sc_lgc, c("chisq", "df", "pvalue", "cfi", "tli", "rmsea", "srmr"))
  cat("Model fit: χ² =", round(fit_sc["chisq"], 2), ", df =", fit_sc["df"], 
      ", p =", round(fit_sc["pvalue"], 3), "\n")
  cat("           CFI =", round(fit_sc["cfi"], 3), ", TLI =", round(fit_sc["tli"], 3),
      ", RMSEA =", round(fit_sc["rmsea"], 3), ", SRMR =", round(fit_sc["srmr"], 3), "\n")
  
} else {
  cat("❌ Self-control LGC failed to converge\n")
}

# Model 2: Fight LGC
cat("\n2. Fight Behavior Latent Growth Curve Model\n")
fight_lgc_syntax <- '
# Growth factors  
i_fight =~ 1*agr5_fig + 1*agr6_fig + 1*agr7_fig
s_fight =~ 0*agr5_fig + 1*agr6_fig + 2*agr7_fig

# Growth means
i_fight ~ 1
s_fight ~ 1

# Residual variances
agr5_fig ~~ agr5_fig
agr6_fig ~~ agr6_fig  
agr7_fig ~~ agr7_fig
'

fit_fight_lgc <- tryCatch({
  growth(fight_lgc_syntax, data = analysis_data, missing = "ML", estimator = "MLR")
}, error = function(e) {
  cat("Error fitting fight LGC:", e$message, "\n")
  return(NULL)
})

if(!is.null(fit_fight_lgc) && lavInspect(fit_fight_lgc, "converged")) {
  cat("✓ Fight LGC converged\n")
  
  # Growth parameters
  fight_params <- parameterEstimates(fit_fight_lgc)
  fight_intercept <- fight_params[fight_params$lhs == "i_fight" & fight_params$op == "~1", ]
  fight_slope <- fight_params[fight_params$lhs == "s_fight" & fight_params$op == "~1", ]
  
  cat("Fight behavior development:\n")
  if(nrow(fight_intercept) > 0) {
    cat("  Intercept =", round(fight_intercept$est, 3), "(p =", round(fight_intercept$pvalue, 3), ")\n")
  }
  if(nrow(fight_slope) > 0) {
    cat("  Slope =", round(fight_slope$est, 3), "(p =", round(fight_slope$pvalue, 3), ")\n")
    if(!is.na(fight_slope$pvalue) && fight_slope$pvalue < 0.05) {
      if(fight_slope$est > 0) {
        cat("  → Fighting behavior INCREASES over time\n")
      } else {
        cat("  → Fighting behavior DECREASES over time\n")
      }
    } else {
      cat("  → No significant change in fighting behavior\n")
    }
  }
  
  # Fit measures
  fit_fight <- fitMeasures(fit_fight_lgc, c("chisq", "df", "pvalue", "cfi", "tli", "rmsea", "srmr"))
  cat("Model fit: χ² =", round(fit_fight["chisq"], 2), ", df =", fit_fight["df"], 
      ", p =", round(fit_fight["pvalue"], 3), "\n")
  cat("           CFI =", round(fit_fight["cfi"], 3), ", TLI =", round(fit_fight["tli"], 3),
      ", RMSEA =", round(fit_fight["rmsea"], 3), ", SRMR =", round(fit_fight["srmr"], 3), "\n")
      
} else {
  cat("❌ Fight LGC failed to converge\n")
}

# =============================================================================
# STEP 5: PARALLEL-PROCESS MODEL
# =============================================================================

cat("\n=== PARALLEL-PROCESS MODEL ===\n")

parallel_syntax <- '
# Self-control growth factors
i_sc =~ 1*sc_factor_11 + 1*sc_factor_14 + 1*sc_factor_17
s_sc =~ 0*sc_factor_11 + 1*sc_factor_14 + 2*sc_factor_17

# Fight behavior growth factors
i_fight =~ 1*agr5_fig + 1*agr6_fig + 1*agr7_fig
s_fight =~ 0*agr5_fig + 1*agr6_fig + 2*agr7_fig

# Growth means
i_sc ~ 1
s_sc ~ 1
i_fight ~ 1
s_fight ~ 1

# Cross-domain correlations (the key parallel-process parameters)
i_sc ~~ i_fight       # Intercept correlation
s_sc ~~ s_fight       # Slope correlation
i_sc ~~ s_fight       # Cross-domain: initial SC with fight change
s_sc ~~ i_fight       # Cross-domain: SC change with initial fight

# Residual variances
sc_factor_11 ~~ sc_factor_11
sc_factor_14 ~~ sc_factor_14
sc_factor_17 ~~ sc_factor_17
agr5_fig ~~ agr5_fig
agr6_fig ~~ agr6_fig
agr7_fig ~~ agr7_fig
'

fit_parallel <- tryCatch({
  growth(parallel_syntax, data = analysis_data, missing = "ML", estimator = "MLR")
}, error = function(e) {
  cat("Error fitting parallel-process model:", e$message, "\n")
  return(NULL)
})

if(!is.null(fit_parallel) && lavInspect(fit_parallel, "converged")) {
  cat("✓ Parallel-process model converged\n")
  
  # Extract correlations
  par_params <- parameterEstimates(fit_parallel)
  
  # Find correlation parameters
  intercept_cor <- par_params[par_params$lhs == "i_sc" & par_params$op == "~~" & par_params$rhs == "i_fight", ]
  slope_cor <- par_params[par_params$lhs == "s_sc" & par_params$op == "~~" & par_params$rhs == "s_fight", ]
  cross_cor1 <- par_params[par_params$lhs == "i_sc" & par_params$op == "~~" & par_params$rhs == "s_fight", ]
  cross_cor2 <- par_params[par_params$lhs == "s_sc" & par_params$op == "~~" & par_params$rhs == "i_fight", ]
  
  cat("\nParallel-process correlations:\n")
  if(nrow(intercept_cor) > 0) {
    cat("  Initial levels (i_sc ~~ i_fight) =", round(intercept_cor$est, 3), 
        "(p =", round(intercept_cor$pvalue, 3), ")\n")
    if(!is.na(intercept_cor$pvalue) && intercept_cor$pvalue < 0.05) {
      if(intercept_cor$est > 0) {
        cat("    → Higher initial self-control problems linked to more fighting\n")
      } else {
        cat("    → Higher initial self-control problems linked to less fighting\n")
      }
    }
  }
  
  if(nrow(slope_cor) > 0) {
    cat("  Change coupling (s_sc ~~ s_fight) =", round(slope_cor$est, 3), 
        "(p =", round(slope_cor$pvalue, 3), ")\n")
    if(!is.na(slope_cor$pvalue) && slope_cor$pvalue < 0.05) {
      if(slope_cor$est > 0) {
        cat("    → Changes in self-control and fighting are positively coupled\n")
      } else {
        cat("    → Changes in self-control and fighting are negatively coupled\n")
      }
    }
  }
  
  if(nrow(cross_cor1) > 0) {
    cat("  Cross-domain 1 (i_sc ~~ s_fight) =", round(cross_cor1$est, 3), 
        "(p =", round(cross_cor1$pvalue, 3), ")\n")
    if(!is.na(cross_cor1$pvalue) && cross_cor1$pvalue < 0.05) {
      cat("    → Initial self-control predicts fighting behavior change\n")
    }
  }
  
  if(nrow(cross_cor2) > 0) {
    cat("  Cross-domain 2 (s_sc ~~ i_fight) =", round(cross_cor2$est, 3), 
        "(p =", round(cross_cor2$pvalue, 3), ")\n")
    if(!is.na(cross_cor2$pvalue) && cross_cor2$pvalue < 0.05) {
      cat("    → Initial fighting predicts self-control change\n")
    }
  }
  
  # Overall model fit
  fit_par <- fitMeasures(fit_parallel, c("chisq", "df", "pvalue", "cfi", "tli", "rmsea", "srmr", "aic", "bic"))
  cat("\nOverall model fit:\n")
  cat("  χ² =", round(fit_par["chisq"], 2), ", df =", fit_par["df"], 
      ", p =", round(fit_par["pvalue"], 3), "\n")
  cat("  CFI =", round(fit_par["cfi"], 3), ", TLI =", round(fit_par["tli"], 3), "\n")
  cat("  RMSEA =", round(fit_par["rmsea"], 3), ", SRMR =", round(fit_par["srmr"], 3), "\n")
  cat("  AIC =", round(fit_par["aic"], 0), ", BIC =", round(fit_par["bic"], 0), "\n")
  
} else {
  cat("❌ Parallel-process model failed to converge\n")
}

# =============================================================================
# STEP 6: MODEL COMPARISON AND INTERPRETATION
# =============================================================================

cat("\n=== MODEL COMPARISON ===\n")

# Compare models if all converged
converged_models <- c()
if(!is.null(fit_sc_lgc) && lavInspect(fit_sc_lgc, "converged")) {
  converged_models <- c(converged_models, "Self-Control LGC")
}
if(!is.null(fit_fight_lgc) && lavInspect(fit_fight_lgc, "converged")) {
  converged_models <- c(converged_models, "Fight LGC")
}
if(!is.null(fit_parallel) && lavInspect(fit_parallel, "converged")) {
  converged_models <- c(converged_models, "Parallel-Process")
}

cat("Converged models:", paste(converged_models, collapse = ", "), "\n")

# Create comparison table if all models converged
if(length(converged_models) == 3) {
  comparison_table <- data.frame(
    Model = c("Self-Control LGC", "Fight LGC", "Parallel-Process"),
    ChiSq = c(
      round(fitMeasures(fit_sc_lgc, "chisq"), 2),
      round(fitMeasures(fit_fight_lgc, "chisq"), 2),
      round(fitMeasures(fit_parallel, "chisq"), 2)
    ),
    df = c(
      fitMeasures(fit_sc_lgc, "df"),
      fitMeasures(fit_fight_lgc, "df"),
      fitMeasures(fit_parallel, "df")
    ),
    CFI = c(
      round(fitMeasures(fit_sc_lgc, "cfi"), 3),
      round(fitMeasures(fit_fight_lgc, "cfi"), 3),
      round(fitMeasures(fit_parallel, "cfi"), 3)
    ),
    RMSEA = c(
      round(fitMeasures(fit_sc_lgc, "rmsea"), 3),
      round(fitMeasures(fit_fight_lgc, "rmsea"), 3),
      round(fitMeasures(fit_parallel, "rmsea"), 3)
    ),
    AIC = c(
      round(fitMeasures(fit_sc_lgc, "aic"), 0),
      round(fitMeasures(fit_fight_lgc, "aic"), 0),
      round(fitMeasures(fit_parallel, "aic"), 0)
    )
  )
  
  cat("\nModel comparison table:\n")
  print(comparison_table)
}

# =============================================================================
# STEP 7: THEORETICAL INTERPRETATION
# =============================================================================

cat("\n=== THEORETICAL INTERPRETATION ===\n")

cat("This parallel-process LGC model tests several key hypotheses:\n\n")

cat("1. DEVELOPMENTAL COUPLING:\n")
cat("   - Do self-control and fighting behavior change together over time?\n")
cat("   - Positive slope correlation suggests co-development\n")
cat("   - Negative slope correlation suggests compensatory development\n\n")

cat("2. INITIAL ASSOCIATION:\n")
cat("   - Are early self-control problems and fighting behavior related?\n")
cat("   - Tests foundational link between these constructs at age 11\n\n")

cat("3. CROSS-DOMAIN PREDICTION:\n")
cat("   - Does initial self-control predict changes in fighting?\n")
cat("   - Does initial fighting predict changes in self-control?\n")
cat("   - Tests developmental cascades between domains\n\n")

cat("4. PRACTICAL IMPLICATIONS:\n")
cat("   - Significant correlations suggest shared developmental processes\n")
cat("   - Cross-domain effects inform intervention timing\n")
cat("   - Slope correlations indicate need for multi-domain approaches\n\n")

cat("=== NEXT STEPS ===\n")
cat("1. Test alternative growth functions (quadratic, piecewise)\n")
cat("2. Add time-varying or time-invariant predictors\n")
cat("3. Examine residual correlations for time-specific effects\n")
cat("4. Test invariance across subgroups (gender, SES)\n")
cat("5. Validate with factor scores vs. latent variable approaches\n\n")

cat("=== SIMPLIFIED PARALLEL-PROCESS LGC ANALYSIS COMPLETE ===\n") 