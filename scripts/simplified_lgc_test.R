# =============================================================================
# SIMPLIFIED SECOND-ORDER LATENT GROWTH CURVE TEST
# Testing the concept with a subset of items
# =============================================================================

library(pacman)
p_load(lavaan, dplyr)

# Load and prepare data
cat("=== SIMPLIFIED SECOND-ORDER LGC TEST ===\n")
data_path <- "/home/<USER>/dissertation_folder/data"
merged_data <- readRDS(file.path(data_path, "merged1203.rds"))

# Source recoding scripts if needed
sc11_test_vars <- c("sc11_think_act", "sc11_considerate", "sc11_sharing", "sc11_helpful")
if(!all(sc11_test_vars %in% names(merged_data))) {
  source("scripts/recode_self_control.R")
  source("scripts/recode_self_control_age14.R") 
  source("scripts/recode_self_control_age17.R")
}

# Use only Executive Control items for testing
executive_items <- c("task_completion", "distracted", "think_act")
cat("Testing with Executive Control items:", paste(executive_items, collapse = ", "), "\n")

# Create variable lists
sc11_vars <- paste0("sc11_", executive_items)
sc14_vars <- paste0("sc14_", executive_items)
sc17_vars <- paste0("sc17_", executive_items)

# Create analysis dataset
all_vars <- c(sc11_vars, sc14_vars, sc17_vars)
available_vars <- all_vars[all_vars %in% names(merged_data)]

wide_data <- merged_data[, available_vars, drop = FALSE]
complete_rows <- rowSums(!is.na(wide_data)) > 0
wide_data <- wide_data[complete_rows, ]

cat("Sample size:", nrow(wide_data), "\n")
cat("Available variables:", length(available_vars), "/", length(all_vars), "\n")

# Simple second-order LGC model
simple_lgc_syntax <- '
# First level: Factor structure at each wave
Executive11 =~ sc11_task_completion + sc11_distracted + sc11_think_act
Executive14 =~ sc14_task_completion + sc14_distracted + sc14_think_act  
Executive17 =~ sc17_task_completion + sc17_distracted + sc17_think_act

# Second level: Growth factors
iExecutive =~ 1*Executive11 + 1*Executive14 + 1*Executive17
sExecutive =~ 0*Executive11 + 1*Executive14 + 2*Executive17

# Fix factor means for identification
Executive11 ~ 0*1
Executive14 ~ 0*1
Executive17 ~ 0*1

# Constrain disturbances to zero (perfect second-order factor model)
Executive11 ~~ 0*Executive11
Executive14 ~~ 0*Executive14
Executive17 ~~ 0*Executive17
'

cat("\nModel syntax:\n", simple_lgc_syntax, "\n")

# Fit the model
cat("Fitting simplified model...\n")
fit_simple <- tryCatch({
  sem(
    model = simple_lgc_syntax,
    data = wide_data,
    estimator = "WLSMV",
    ordered = available_vars,
    missing = "pairwise"
  )
}, error = function(e) {
  cat("Error:", e$message, "\n")
  return(NULL)
})

# Check results
if(!is.null(fit_simple)) {
  converged <- lavInspect(fit_simple, "converged")
  cat("Converged:", converged, "\n")
  
  if(converged) {
    # Fit measures
    fit_measures <- fitMeasures(fit_simple, c("cfi", "tli", "rmsea", "srmr"))
    cat("Fit measures:\n")
    print(round(fit_measures, 3))
    
    # Parameter estimates
    cat("\nGrowth parameters:\n")
    params <- parameterEstimates(fit_simple, standardized = FALSE)
    
    # Intercept and slope estimates
    intercept_est <- params[params$lhs == "iExecutive" & params$op == "~1", ]
    slope_est <- params[params$lhs == "sExecutive" & params$op == "~1", ]
    
    if(nrow(intercept_est) > 0) {
      cat("Intercept mean:", round(intercept_est$est, 3), "(p =", round(intercept_est$pvalue, 3), ")\n")
    }
    
    if(nrow(slope_est) > 0) {
      cat("Slope mean:", round(slope_est$est, 3), "(p =", round(slope_est$pvalue, 3), ")\n")
      if(!is.na(slope_est$pvalue) && slope_est$pvalue < 0.05) {
        if(slope_est$est > 0) {
          cat("→ Significant INCREASE over time\n")
        } else {
          cat("→ Significant DECREASE over time\n")  
        }
      } else {
        cat("→ No significant change\n")
      }
    }
    
    cat("\n✓ Simplified model working!\n")
    
  } else {
    cat("❌ Model did not converge\n")
  }
} else {
  cat("❌ Model fitting failed\n")
}

cat("\n=== TEST COMPLETE ===\n") 