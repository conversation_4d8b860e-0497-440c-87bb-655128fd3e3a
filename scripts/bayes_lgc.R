# ------------------ 0.  Install + load ------------------
library(blavaan)                     # loads lavaan & blavaan helpers
library(bayesplot)                   # for trace/ppc plots

# ------------------ 1.  Model string -------------------
exec_bayes <- '
  # measurement (marker metric)
  Executive11 =~ 1*sc11_task_completion + sc11_distracted + sc11_fidgeting +
                 sc11_think_act + sc11_restless
  Executive14 =~ 1*sc14_task_completion + sc14_distracted + sc14_fidgeting +
                 sc14_think_act + sc14_restless
  Executive17 =~ 1*sc17_task_completion + sc17_distracted + sc17_fidgeting +
                 sc17_think_act + sc17_restless

  # serial residual correlations (full lattice)
  sc11_task_completion ~~ sc14_task_completion + sc17_task_completion
  sc14_task_completion ~~ sc17_task_completion
  sc11_distracted      ~~ sc14_distracted + sc17_distracted
  sc14_distracted      ~~ sc17_distracted
  sc11_fidgeting       ~~ sc14_fidgeting  + sc17_fidgeting
  sc14_fidgeting       ~~ sc17_fidgeting
  sc11_think_act       ~~ sc14_think_act  + sc17_think_act
  sc14_think_act       ~~ sc17_think_act
  sc11_restless        ~~ sc14_restless   + sc17_restless
  sc14_restless        ~~ sc17_restless

  # second-order growth (latent-basis)
  iExec =~ 1*Executive11 + 1*Executive14 + 1*Executive17
  sExec =~ 0*Executive11 + 1*Executive14 + lambda3*Executive17

  # growth means
  iExec ~ 1
  sExec ~ 1
'


# ------------------ 3.  Run Stan-based MCMC ------------
fit_bayes <- bsem(
  model   = exec_bayes,          # <- the model string with "lambda3" label
  data    = wide_data,
  ordered = c("sc11_task_completion", "sc11_distracted", "sc11_fidgeting", "sc11_think_act", "sc11_restless",
              "sc14_task_completion", "sc14_distracted", "sc14_fidgeting", "sc14_think_act", "sc14_restless",
              "sc17_task_completion", "sc17_distracted", "sc17_fidgeting", "sc17_think_act", "sc17_restless"
            ),
  target  = "stan",
  bcontrol = list(cores = 11),
  n.chains = 3, burnin = 20, sample = 40, seed = 2025  # increased iterations for better convergence
)
 
# Summarize the results
summary(fit_bayes)  
