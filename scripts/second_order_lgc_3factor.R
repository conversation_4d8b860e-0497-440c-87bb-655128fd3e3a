# =============================================================================
# SECOND-ORDER LATENT GROWTH CURVE MODELING  
# Self-Control Scale: Three-Factor Structure with Growth Modeling
# Ages 11, 14, and 17
# =============================================================================

library(pacman)
p_load(lavaan, semTools, dplyr, psych, ggplot2, knitr, haven)

# =============================================================================
# 0. DATA LOADING AND PREPARATION
# =============================================================================

cat("=== SECOND-ORDER LATENT GROWTH CURVE MODELING ===\n")
cat("Three-Factor Self-Control Scale: Ages 11, 14, 17\n")
cat("Testing developmental trajectories across adolescence\n\n")

# Load data from previous analysis
cat("Loading data...\n")
data_path <- "/home/<USER>/dissertation_folder/data"
merged_data <- readRDS(file.path(data_path, "merged1203.rds"))

# Check if recoded variables exist
sc11_test_vars <- c("sc11_think_act", "sc11_considerate", "sc11_sharing", "sc11_helpful")
if(!all(sc11_test_vars %in% names(merged_data))) {
  cat("Running recoding scripts...\n")
  source("scripts/recode_self_control.R")
  source("scripts/recode_self_control_age14.R") 
  source("scripts/recode_self_control_age17.R")
  cat("Recoding completed.\n")
} else {
  cat("Recoded variables already available.\n")
}

# Define the same factor structure as before
executive_items <- c("task_completion", "distracted", "fidgeting", "think_act", "restless")
selfcent_items <- c("considerate", "sharing", "helpful", "volunteer_help")
temper_items <- c("temper", "obedient", "lying")

core_items <- c(executive_items, selfcent_items, temper_items)

# Create variable names for each wave
sc11_vars <- paste0("sc11_", core_items)
sc14_vars <- paste0("sc14_", core_items)
sc17_vars <- paste0("sc17_", core_items)

# Create analysis dataset in wide format (needed for growth modeling)
all_vars <- c(sc11_vars, sc14_vars, sc17_vars)
available_vars <- all_vars[all_vars %in% names(merged_data)]

wide_data <- merged_data[, available_vars, drop = FALSE]

# Add participant ID if needed
if(!"id" %in% names(merged_data)) {
  wide_data$id <- 1:nrow(wide_data)
} else {
  wide_data$id <- merged_data$id[1:nrow(wide_data)]
}

# Remove cases with no data across all waves
complete_check <- rowSums(!is.na(wide_data[, available_vars])) > 0
wide_data <- wide_data[complete_check, ]

cat("Analysis dataset: N =", nrow(wide_data), "participants\n")
cat("Variables available:", length(available_vars), "/", length(all_vars), "\n\n")

# =============================================================================
# 1. SECOND-ORDER LATENT GROWTH CURVE MODEL SPECIFICATION
# =============================================================================

cat("=== SECOND-ORDER LGC MODEL SPECIFICATION ===\n")

# Create second-order LGC model syntax
create_secondorder_lgc_syntax <- function() {
  
  # First level: Factor structure at each wave
  first_level <- paste0(
    "# === FIRST LEVEL: FACTOR STRUCTURE AT EACH WAVE ===\n",
    "# Age 11 factors\n",
    "Executive11 =~ ", paste(paste0("sc11_", executive_items), collapse = " + "), "\n",
    "SelfCent11 =~ ", paste(paste0("sc11_", selfcent_items), collapse = " + "), "\n", 
    "Temper11 =~ ", paste(paste0("sc11_", temper_items), collapse = " + "), "\n",
    "\n",
    "# Age 14 factors\n",
    "Executive14 =~ ", paste(paste0("sc14_", executive_items), collapse = " + "), "\n",
    "SelfCent14 =~ ", paste(paste0("sc14_", selfcent_items), collapse = " + "), "\n",
    "Temper14 =~ ", paste(paste0("sc14_", temper_items), collapse = " + "), "\n",
    "\n",
    "# Age 17 factors\n",
    "Executive17 =~ ", paste(paste0("sc17_", executive_items), collapse = " + "), "\n",
    "SelfCent17 =~ ", paste(paste0("sc17_", selfcent_items), collapse = " + "), "\n",
    "Temper17 =~ ", paste(paste0("sc17_", temper_items), collapse = " + "), "\n",
    "\n"
  )
  
  # Second level: Growth factors
  second_level <- paste0(
    "# === SECOND LEVEL: LATENT GROWTH CURVES ===\n",
    "# Executive Control Growth (Hypothesis: Linear increase)\n",
    "iExecutive =~ 1*Executive11 + 1*Executive14 + 1*Executive17\n",
    "sExecutive =~ 0*Executive11 + 3*Executive14 + 6*Executive17\n",
    "\n",
    "# Temper Control Growth (Hypothesis: Linear decrease)\n", 
    "iTemper =~ 1*Temper11 + 1*Temper14 + 1*Temper17\n",
    "sTemper =~ 0*Temper11 + 3*Temper14 + 6*Temper17\n",
    "\n",
    "# Self-Centered Prosociality Growth (Hypothesis: Rise then plateau)\n",
    "# Using latent basis model with free loading at age 17\n",
    "iSelfCent =~ 1*SelfCent11 + 1*SelfCent14 + 1*SelfCent17\n",
    "sSelfCent =~ 0*SelfCent11 + 1*SelfCent14 + lambda*SelfCent17\n",
    "\n"
  )
  
  # Constraints and additional specifications  
  constraints <- paste0(
    "# === CONSTRAINTS FOR IDENTIFICATION ===\n",
    "# Fix first indicator intercepts per wave (not factor means!)\n",
    "sc11_task_completion ~ c1*1\n", "sc14_task_completion ~ c1*1\n", "sc17_task_completion ~ c1*1\n",
    "sc11_considerate ~ c2*1\n", "sc14_considerate ~ c2*1\n", "sc17_considerate ~ c2*1\n",
    "sc11_temper ~ c3*1\n", "sc14_temper ~ c3*1\n", "sc17_temper ~ c3*1\n",
    "\n"
  )
  
  # Combine all parts
  full_syntax <- paste0(first_level, second_level, constraints)
  
  return(full_syntax)
}

# Generate the model syntax
lgc_syntax <- create_secondorder_lgc_syntax()

cat("Second-Order LGC Model Structure:\n")
cat("- First Level: Three factors (Executive, SelfCent, Temper) at each wave\n")
cat("- Second Level: Intercept and slope factors for each domain\n")
cat("- Growth Hypotheses:\n")
cat("  * Executive: Linear increase (0, 3, 6 loadings)\n")
cat("  * Temper: Linear decrease (0, 3, 6 loadings)\n") 
cat("  * SelfCent: Rise then plateau (0, 1, free loadings)\n\n")

# =============================================================================
# 2. MODEL FITTING
# =============================================================================

cat("=== MODEL FITTING ===\n")

# Fit the second-order LGC model
cat("Fitting second-order latent growth curve model...\n")

fit_lgc <- tryCatch({
  sem(
    model = lgc_syntax,
    data = wide_data,
    estimator = "WLSMV",
    ordered = available_vars,
    missing = "pairwise",
    std.lv = FALSE,  # Don't standardize latent variables for growth models
    control = list(iter.max = 2000, eval.max = 4000)
  )
}, error = function(e) {
  cat("❌ Error fitting LGC model:", e$message, "\n")
  return(NULL)
})

# Check convergence
if(!is.null(fit_lgc)) {
  converged <- lavInspect(fit_lgc, "converged")
  cat("Model converged:", converged, "\n")
  
  if(converged) {
    cat("✓ Second-order LGC model fitted successfully!\n\n")
    
    # =============================================================================
    # 3. MODEL FIT EVALUATION
    # =============================================================================
    
    cat("=== MODEL FIT EVALUATION ===\n")
    
    fit_measures <- fitMeasures(fit_lgc, c(
      "chisq.scaled", "df", "pvalue.scaled",
      "cfi.scaled", "tli.scaled", "rmsea.scaled",
      "rmsea.ci.lower.scaled", "rmsea.ci.upper.scaled", "srmr"
    ))
    
    cat("Second-Order LGC Model Fit:\n")
    cat("  χ² (scaled) =", round(fit_measures["chisq.scaled"], 3),
        ", df =", fit_measures["df"],
        ", p =", round(fit_measures["pvalue.scaled"], 3), "\n")
    cat("  CFI (scaled) =", round(fit_measures["cfi.scaled"], 3), "\n")
    cat("  TLI (scaled) =", round(fit_measures["tli.scaled"], 3), "\n")
    cat("  RMSEA (scaled) =", round(fit_measures["rmsea.scaled"], 3),
        " [", round(fit_measures["rmsea.ci.lower.scaled"], 3), ", ",
        round(fit_measures["rmsea.ci.upper.scaled"], 3), "]\n")
    cat("  SRMR =", round(fit_measures["srmr"], 3), "\n\n")
    
    # Fit interpretation
    good_fit <- (fit_measures["cfi.scaled"] >= 0.95 & 
                 fit_measures["tli.scaled"] >= 0.95 & 
                 fit_measures["rmsea.scaled"] <= 0.06 &
                 fit_measures["srmr"] <= 0.08)
    
    acceptable_fit <- (fit_measures["cfi.scaled"] >= 0.90 & 
                       fit_measures["tli.scaled"] >= 0.90 & 
                       fit_measures["rmsea.scaled"] <= 0.08 &
                       fit_measures["srmr"] <= 0.10)
    
    if(good_fit) {
      cat("✅ Model shows GOOD fit to the data\n")
    } else if(acceptable_fit) {
      cat("⚠️ Model shows ACCEPTABLE fit to the data\n")
    } else {
      cat("❌ Model shows POOR fit to the data\n")
    }
    
    # =============================================================================
    # 4. GROWTH PARAMETER ESTIMATES
    # =============================================================================
    
    cat("\n=== GROWTH PARAMETER ESTIMATES ===\n")
    
    # Extract parameter estimates
    param_estimates <- parameterEstimates(fit_lgc, standardized = FALSE, ci = TRUE)
    
    # Function to extract and display growth parameters
    extract_growth_params <- function(param_est, factor_name) {
      cat("\n--- ", toupper(factor_name), " CONTROL DEVELOPMENT ---\n")
      
      # Intercept parameters
      intercept_mean <- param_est[param_est$lhs == paste0("i", factor_name) & param_est$op == "~1", ]
      intercept_var <- param_est[param_est$lhs == paste0("i", factor_name) & param_est$rhs == paste0("i", factor_name) & param_est$op == "~~", ]
      
      # Slope parameters  
      slope_mean <- param_est[param_est$lhs == paste0("s", factor_name) & param_est$op == "~1", ]
      slope_var <- param_est[param_est$lhs == paste0("s", factor_name) & param_est$rhs == paste0("s", factor_name) & param_est$op == "~~", ]
      
      # Intercept-slope covariance
      cov_is <- param_est[(param_est$lhs == paste0("i", factor_name) & param_est$rhs == paste0("s", factor_name)) |
                          (param_est$lhs == paste0("s", factor_name) & param_est$rhs == paste0("i", factor_name)), ]
      
      # Display results
      if(nrow(intercept_mean) > 0) {
        cat("Intercept (Age 11 level):\n")
        cat("  Mean = ", round(intercept_mean$est, 3), 
            " [", round(intercept_mean$ci.lower, 3), ", ", round(intercept_mean$ci.upper, 3), "]",
            ", p = ", round(intercept_mean$pvalue, 3), "\n")
      }
      
      if(nrow(intercept_var) > 0) {
        cat("  Variance = ", round(intercept_var$est, 3), 
            " [", round(intercept_var$ci.lower, 3), ", ", round(intercept_var$ci.upper, 3), "]",
            ", p = ", round(intercept_var$pvalue, 3), "\n")
      }
      
      if(nrow(slope_mean) > 0) {
        cat("Slope (Rate of change):\n")
        cat("  Mean = ", round(slope_mean$est, 3), 
            " [", round(slope_mean$ci.lower, 3), ", ", round(slope_mean$ci.upper, 3), "]",
            ", p = ", round(slope_mean$pvalue, 3), "\n")
        
        # Interpret direction (check for NA values)
        if(!is.na(slope_mean$pvalue) && slope_mean$pvalue < 0.05) {
          if(slope_mean$est > 0) {
            cat("  → Significant INCREASE over time\n")
          } else {
            cat("  → Significant DECREASE over time\n")
          }
        } else {
          cat("  → No significant change over time\n")
        }
      }
      
      if(nrow(slope_var) > 0) {
        cat("  Variance = ", round(slope_var$est, 3), 
            " [", round(slope_var$ci.lower, 3), ", ", round(slope_var$ci.upper, 3), "]",
            ", p = ", round(slope_var$pvalue, 3), "\n")
        
        if(!is.na(slope_var$pvalue) && slope_var$pvalue < 0.05) {
          cat("  → Significant individual differences in change\n")
        } else {
          cat("  → Similar change patterns across individuals\n")
        }
      }
      
      if(nrow(cov_is) > 0) {
        cat("Intercept-Slope Covariance:\n")
        cat("  Covariance = ", round(cov_is$est, 3), 
            " [", round(cov_is$ci.lower, 3), ", ", round(cov_is$ci.upper, 3), "]",
            ", p = ", round(cov_is$pvalue, 3), "\n")
        
        if(!is.na(cov_is$pvalue) && cov_is$pvalue < 0.05) {
          if(cov_is$est < 0) {
            cat("  → Lower initial levels predict FASTER improvement\n")
          } else {
            cat("  → Higher initial levels predict FASTER improvement\n")
          }
        } else {
          cat("  → Initial level unrelated to rate of change\n")
        }
      }
    }
    
    # Extract growth parameters for each domain
    extract_growth_params(param_estimates, "Executive")
    extract_growth_params(param_estimates, "Temper") 
    extract_growth_params(param_estimates, "SelfCent")
    
    # =============================================================================
    # 5. LATENT BASIS MODEL RESULTS (SELFCENT)
    # =============================================================================
    
    cat("\n=== LATENT BASIS MODEL RESULTS ===\n")
    cat("Self-Centered Prosociality: Testing rise-then-plateau hypothesis\n")
    
    # Extract the free lambda parameter for SelfCent at age 17
    lambda_param <- param_estimates[param_estimates$label == "lambda", ]
    
    if(nrow(lambda_param) > 0) {
      cat("Free loading at Age 17 (lambda):\n")
      cat("  Estimate = ", round(lambda_param$est, 3), 
          " [", round(lambda_param$ci.lower, 3), ", ", round(lambda_param$ci.upper, 3), "]",
          ", p = ", round(lambda_param$pvalue, 3), "\n")
      
      # Interpret the pattern
      if(lambda_param$est < 1) {
        cat("  → Pattern: Rise then PLATEAU (λ < 1)\n")
        cat("  → Change slows down by age 17\n")
      } else if(lambda_param$est > 1) {
        cat("  → Pattern: Accelerating INCREASE (λ > 1)\n") 
        cat("  → Change speeds up by age 17\n")
      } else {
        cat("  → Pattern: Linear INCREASE (λ ≈ 1)\n")
        cat("  → Constant rate of change\n")
      }
    }
    
    # =============================================================================
    # 6. FACTOR CORRELATIONS AT EACH WAVE
    # =============================================================================
    
    cat("\n=== FACTOR CORRELATIONS BY WAVE ===\n")
    
    # Extract factor correlations at each wave
    factor_cors <- param_estimates[param_estimates$op == "~~" & 
                                   param_estimates$lhs != param_estimates$rhs &
                                   (grepl("11|14|17", param_estimates$lhs) & grepl("11|14|17", param_estimates$rhs)), ]
    
    if(nrow(factor_cors) > 0) {
      for(age in c("11", "14", "17")) {
        cat("\nAge", age, "Factor Correlations:\n")
        age_cors <- factor_cors[grepl(age, factor_cors$lhs) & grepl(age, factor_cors$rhs), ]
        
        if(nrow(age_cors) > 0) {
          for(i in 1:nrow(age_cors)) {
            cat("  ", age_cors$lhs[i], " ~~ ", age_cors$rhs[i], ": r = ", 
                round(age_cors$est[i], 3), " (p = ", round(age_cors$pvalue[i], 3), ")\n")
          }
        }
      }
    }
    
    # =============================================================================
    # 7. SUMMARY AND INTERPRETATION
    # =============================================================================
    
    cat("\n=== DEVELOPMENTAL SUMMARY ===\n")
    
    cat("Second-Order Latent Growth Curve Analysis Results:\n\n")
    
    cat("HYPOTHESIS TESTING:\n")
    
    # Executive hypothesis
    exec_slope <- param_estimates[param_estimates$lhs == "sExecutive" & param_estimates$op == "~1", ]
    if(nrow(exec_slope) > 0) {
      if(!is.na(exec_slope$pvalue) && exec_slope$pvalue < 0.05 && exec_slope$est > 0) {
        cat("✅ Executive Control: SUPPORTED - Significant linear increase\n")
      } else if(!is.na(exec_slope$pvalue) && exec_slope$pvalue < 0.05 && exec_slope$est < 0) {
        cat("❌ Executive Control: NOT SUPPORTED - Significant decrease (opposite)\n")
      } else {
        cat("⚠️ Executive Control: PARTIAL - No significant change detected\n")
      }
    }
    
    # Temper hypothesis
    temp_slope <- param_estimates[param_estimates$lhs == "sTemper" & param_estimates$op == "~1", ]
    if(nrow(temp_slope) > 0) {
      if(!is.na(temp_slope$pvalue) && temp_slope$pvalue < 0.05 && temp_slope$est < 0) {
        cat("✅ Temper Control: SUPPORTED - Significant linear decrease\n")
      } else if(!is.na(temp_slope$pvalue) && temp_slope$pvalue < 0.05 && temp_slope$est > 0) {
        cat("❌ Temper Control: NOT SUPPORTED - Significant increase (opposite)\n")
      } else {
        cat("⚠️ Temper Control: PARTIAL - No significant change detected\n")
      }
    }
    
    # SelfCent hypothesis
    if(nrow(lambda_param) > 0) {
      if(lambda_param$est < 1 && lambda_param$ci.upper < 1) {
        cat("✅ Self-Centered Prosociality: SUPPORTED - Rise then plateau pattern\n")
      } else if(lambda_param$est > 1 && lambda_param$ci.lower > 1) {
        cat("❌ Self-Centered Prosociality: NOT SUPPORTED - Accelerating increase\n")
      } else {
        cat("⚠️ Self-Centered Prosociality: PARTIAL - Pattern unclear\n")
      }
    }
    
    cat("\nINDIVIDUAL DIFFERENCES:\n")
    
    # Check for significant variance in slopes
    exec_slope_var <- param_estimates[param_estimates$lhs == "sExecutive" & param_estimates$rhs == "sExecutive" & param_estimates$op == "~~", ]
    temp_slope_var <- param_estimates[param_estimates$lhs == "sTemper" & param_estimates$rhs == "sTemper" & param_estimates$op == "~~", ]
    self_slope_var <- param_estimates[param_estimates$lhs == "sSelfCent" & param_estimates$rhs == "sSelfCent" & param_estimates$op == "~~", ]
    
    if(nrow(exec_slope_var) > 0 && !is.na(exec_slope_var$pvalue) && exec_slope_var$pvalue < 0.05) {
      cat("• Executive Control: Significant individual differences in development\n")
    }
    if(nrow(temp_slope_var) > 0 && !is.na(temp_slope_var$pvalue) && temp_slope_var$pvalue < 0.05) {
      cat("• Temper Control: Significant individual differences in development\n") 
    }
    if(nrow(self_slope_var) > 0 && !is.na(self_slope_var$pvalue) && self_slope_var$pvalue < 0.05) {
      cat("• Self-Centered Prosociality: Significant individual differences in development\n")
    }
    
  } else {
    cat("❌ Model did not converge - check model specification\n")
  }
  
} else {
  cat("❌ Model fitting failed completely\n")
}

cat("\n=== SECOND-ORDER LGC ANALYSIS COMPLETE ===\n") 