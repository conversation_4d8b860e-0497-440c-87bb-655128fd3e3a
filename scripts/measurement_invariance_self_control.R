# =============================================================================
# LONGITUDINAL MEASUREMENT INVARIANCE TESTING
# Bifactor Self-Control Scale Across Ages 11, 14, and 17
# =============================================================================

library(pacman)
p_load(lavaan, semTools, dplyr, psych, ggplot2, knitr, haven)

# =============================================================================
# 0. DATA LOADING
# =============================================================================

cat("=== LONGITUDINAL MEASUREMENT INVARIANCE TESTING ===\n")
cat("Bifactor Self-Control Scale: Ages 11, 14, 17\n\n")

# Load the dataset
cat("Loading data...\n")
data_path <- "/home/<USER>/dissertation_folder/data"
merged_data <- readRDS(file.path(data_path, "merged1203.rds"))
cat("Data loaded successfully. Dimensions:", dim(merged_data), "\n")

# Check if recoded variables exist, if not, create them
sc11_test_vars <- c("sc11_think_act", "sc11_considerate", "sc11_sharing", "sc11_helpful")
if(!all(sc11_test_vars %in% names(merged_data))) {
  cat("Recoded variables not found. Running recoding scripts...\n")
  source("scripts/recode_self_control.R")
  source("scripts/recode_self_control_age14.R") 
  source("scripts/recode_self_control_age17.R")
  cat("Recoding completed.\n")
} else {
  cat("Recoded variables already available.\n")
}
cat("\n")

# =============================================================================
# 1. DATA PREPARATION
# =============================================================================

# Define the 12 consistent self-control variables across all waves
sc_vars_base <- c(
  "think_act",      # Think things out before acting
  "considerate",    # Being considerate of other people's feelings  
  "sharing",        # Sharing readily with other children
  "helpful",        # Being helpful if someone is hurt
  "volunteer_help", # Often volunteering to help others
  "task_completion",# Sees tasks through to the end, good attention span
  "obedient",       # Generally obedient
  "distracted",     # Is easily distracted, concentration wanders
  "temper",         # Often has temper tantrums or hot tempers
  "restless",       # Child is restless, overactive, cannot stay still for long
  "fidgeting",      # Child is constantly fidgeting or squirming
  "lying"           # Lying or cheating
)

# Create variable names for each wave
sc11_vars <- paste0("sc11_", sc_vars_base)
sc14_vars <- paste0("sc14_", sc_vars_base)
sc17_vars <- paste0("sc17_", sc_vars_base)

# Check variable availability
cat("Checking variable availability across waves...\n")
all_vars <- c(sc11_vars, sc14_vars, sc17_vars)
available_vars <- all_vars[all_vars %in% names(merged_data)]
missing_vars <- all_vars[!all_vars %in% names(merged_data)]

cat("Available variables:", length(available_vars), "/", length(all_vars), "\n")
if(length(missing_vars) > 0) {
  cat("Missing variables:\n")
  for(var in missing_vars) cat("  -", var, "\n")
  cat("\nEnsure all recoding scripts have been run first.\n")
}

# Check completeness by wave
cat("\nVariable completeness by wave:\n")
sc11_available <- sc11_vars[sc11_vars %in% names(merged_data)]
sc14_available <- sc14_vars[sc14_vars %in% names(merged_data)]
sc17_available <- sc17_vars[sc17_vars %in% names(merged_data)]

cat("Age 11:", length(sc11_available), "/", length(sc11_vars), "variables\n")
cat("Age 14:", length(sc14_available), "/", length(sc14_vars), "variables\n")
cat("Age 17:", length(sc17_available), "/", length(sc17_vars), "variables\n")

# Create analysis dataset with available variables only
analysis_vars <- intersect(intersect(sc11_available, sc14_available), sc17_available)
if(length(analysis_vars) == 0) {
  # If no complete overlap, find common base names
  sc11_base <- gsub("sc11_", "", sc11_available)
  sc14_base <- gsub("sc14_", "", sc14_available)
  sc17_base <- gsub("sc17_", "", sc17_available)
  
  common_base <- intersect(intersect(sc11_base, sc14_base), sc17_base)
  
  if(length(common_base) > 0) {
    analysis_vars_11 <- paste0("sc11_", common_base)
    analysis_vars_14 <- paste0("sc14_", common_base)
    analysis_vars_17 <- paste0("sc17_", common_base)
    
    cat("\nUsing", length(common_base), "common variables across all waves:\n")
    for(var in common_base) cat("  -", var, "\n")
  } else {
    stop("No common variables found across all three waves")
  }
} else {
  # Extract common base names from analysis_vars
  common_base <- unique(gsub("sc(11|14|17)_", "", analysis_vars))
  analysis_vars_11 <- paste0("sc11_", common_base)
  analysis_vars_14 <- paste0("sc14_", common_base)
  analysis_vars_17 <- paste0("sc17_", common_base)
}

# =============================================================================
# 2. CREATE WIDE FORMAT DATASET
# =============================================================================

cat("\n=== CREATING ANALYSIS DATASET ===\n")

# Extract data for analysis
wide_data <- merged_data[, c(analysis_vars_11, analysis_vars_14, analysis_vars_17), drop = FALSE]

# Check sample sizes
cat("Sample sizes with available data:\n")
cat("Age 11 complete cases:", sum(complete.cases(merged_data[, analysis_vars_11])), "\n")
cat("Age 14 complete cases:", sum(complete.cases(merged_data[, analysis_vars_14])), "\n")
cat("Age 17 complete cases:", sum(complete.cases(merged_data[, analysis_vars_17])), "\n")

# Check for participants with data at all three waves
complete_longitudinal <- complete.cases(wide_data)
cat("Complete longitudinal cases (all 3 waves):", sum(complete_longitudinal), "\n")

# For invariance testing, we'll use all available data (not just complete cases)
# lavaan handles missing data with FIML when using ML, or pairwise with WLSMV
cat("Using all available data for invariance testing\n")

# =============================================================================
# 3. DEFINE BIFACTOR MODEL
# =============================================================================

cat("\n=== DEFINING BIFACTOR MODEL ===\n")

# Based on the factor structure from individual wave analyses
# Executive: task_completion, distracted, fidgeting, think_act, restless
# SelfCent: considerate, sharing, helpful, volunteer_help  
# Temper: temper, obedient, lying

# Define factor assignments
executive_items <- c("task_completion", "distracted", "fidgeting", "think_act", "restless")
selfcent_items <- c("considerate", "sharing", "helpful", "volunteer_help")
temper_items <- c("temper", "obedient", "lying")

# Check if all items are in common_base
executive_available <- executive_items[executive_items %in% common_base]
selfcent_available <- selfcent_items[selfcent_items %in% common_base]
temper_available <- temper_items[temper_items %in% common_base]

cat("Factor structure with available items:\n")
cat("Executive factor:", length(executive_available), "items -", paste(executive_available, collapse = ", "), "\n")
cat("Self-Centered factor:", length(selfcent_available), "items -", paste(selfcent_available, collapse = ", "), "\n")
cat("Temper factor:", length(temper_available), "items -", paste(temper_available, collapse = ", "), "\n")

# Create model syntax function
create_bifactor_syntax <- function(age_suffix, label_suffix = "") {
  
  # All available items for this wave
  all_items_age <- paste0("sc", age_suffix, "_", common_base)
  general_items <- paste(all_items_age, collapse = " + ")
  
  # Specific factor items
  exec_items_age <- paste0("sc", age_suffix, "_", executive_available)
  selfcent_items_age <- paste0("sc", age_suffix, "_", selfcent_available)
  temper_items_age <- paste0("sc", age_suffix, "_", temper_available)
  
  syntax <- paste0(
    "# General factor\n",
    "General", label_suffix, " =~ ", general_items, "\n\n",
    
    "# Specific factors\n",
    "Executive", label_suffix, " =~ ", paste(exec_items_age, collapse = " + "), "\n",
    "SelfCent", label_suffix, " =~ ", paste(selfcent_items_age, collapse = " + "), "\n",
    "Temper", label_suffix, " =~ ", paste(temper_items_age, collapse = " + "), "\n\n",
    
    "# Orthogonal constraints\n",
    "General", label_suffix, " ~~ 0*Executive", label_suffix, "\n",
    "General", label_suffix, " ~~ 0*SelfCent", label_suffix, "\n", 
    "General", label_suffix, " ~~ 0*Temper", label_suffix, "\n",
    "Executive", label_suffix, " ~~ 0*SelfCent", label_suffix, "\n",
    "Executive", label_suffix, " ~~ 0*Temper", label_suffix, "\n",
    "SelfCent", label_suffix, " ~~ 0*Temper", label_suffix, "\n"
  )
  
  return(syntax)
}

# =============================================================================
# 4. INVARIANCE TESTING SEQUENCE
# =============================================================================

cat("\n=== MEASUREMENT INVARIANCE TESTING ===\n")
cat("Testing sequence for ordinal data:\n")
cat("1. Configural invariance (same factor pattern)\n")
cat("2. Threshold invariance (equal thresholds)\n") 
cat("3. Threshold + Loading invariance (strong invariance)\n")
cat("4. Strict invariance (+ equal residual variances)\n\n")

# =============================================================================
# MODEL 1: CONFIGURAL INVARIANCE
# =============================================================================

cat("--- MODEL 1: CONFIGURAL INVARIANCE ---\n")
cat("Same factor pattern, all parameters free across waves\n")

# Create configural model syntax
configural_syntax <- paste(
  create_bifactor_syntax("11", "_11"),
  create_bifactor_syntax("14", "_14"), 
  create_bifactor_syntax("17", "_17"),
  sep = "\n"
)

cat("Fitting configural model...\n")

# Fit configural model
model_configural <- cfa(
  model = configural_syntax,
  data = wide_data,
  estimator = "WLSMV",
  ordered = c(analysis_vars_11, analysis_vars_14, analysis_vars_17),
  std.lv = TRUE,
  missing = "pairwise"
)

# Check convergence
if(lavInspect(model_configural, "converged")) {
  cat("✓ Configural model converged successfully\n")
  
  # Extract fit indices
  fit_configural <- fitMeasures(model_configural, c(
    "chisq.scaled", "df", "pvalue.scaled",
    "cfi.scaled", "tli.scaled", "rmsea.scaled", 
    "rmsea.ci.lower.scaled", "rmsea.ci.upper.scaled", "srmr"
  ))
  
  cat("Configural Model Fit:\n")
  cat("  χ² (scaled) =", round(fit_configural["chisq.scaled"], 3), 
      ", df =", fit_configural["df"], 
      ", p =", round(fit_configural["pvalue.scaled"], 3), "\n")
  cat("  CFI (scaled) =", round(fit_configural["cfi.scaled"], 3), "\n")
  cat("  TLI (scaled) =", round(fit_configural["tli.scaled"], 3), "\n")
  cat("  RMSEA (scaled) =", round(fit_configural["rmsea.scaled"], 3),
      " [", round(fit_configural["rmsea.ci.lower.scaled"], 3), ", ",
      round(fit_configural["rmsea.ci.upper.scaled"], 3), "]\n")
  cat("  SRMR =", round(fit_configural["srmr"], 3), "\n")
  
} else {
  cat("✗ Configural model did not converge\n")
  cat("Check model specification and data quality\n")
  stop("Cannot proceed without configural model")
}

# =============================================================================
# MODEL 2: THRESHOLD INVARIANCE  
# =============================================================================

cat("\n--- MODEL 2: THRESHOLD INVARIANCE ---\n")
cat("Equal thresholds across waves, loadings still free\n")

# For threshold invariance with lavaan, we use the group.equal = "thresholds" argument
# But since we have a wide format, we need to create a multi-group syntax

# Alternative approach: Use semTools measurementInvariance function
cat("Using semTools for threshold invariance testing...\n")

# First, let's create a long format dataset for multi-group analysis
# Create participant ID if not exists
if(!"id" %in% names(merged_data)) {
  wide_data$id <- 1:nrow(wide_data)
} else {
  wide_data$id <- merged_data$id
}

# Reshape to long format
long_data_list <- list()

for(i in 1:nrow(wide_data)) {
  # Age 11 data
  age11_row <- data.frame(
    id = wide_data$id[i],
    age = 11,
    stringsAsFactors = FALSE
  )
  for(var in common_base) {
    age11_row[[var]] <- wide_data[[paste0("sc11_", var)]][i]
  }
  
  # Age 14 data  
  age14_row <- data.frame(
    id = wide_data$id[i],
    age = 14,
    stringsAsFactors = FALSE
  )
  for(var in common_base) {
    age14_row[[var]] <- wide_data[[paste0("sc14_", var)]][i]
  }
  
  # Age 17 data
  age17_row <- data.frame(
    id = wide_data$id[i], 
    age = 17,
    stringsAsFactors = FALSE
  )
  for(var in common_base) {
    age17_row[[var]] <- wide_data[[paste0("sc17_", var)]][i]
  }
  
  long_data_list[[length(long_data_list) + 1]] <- age11_row
  long_data_list[[length(long_data_list) + 1]] <- age14_row
  long_data_list[[length(long_data_list) + 1]] <- age17_row
}

# Combine into long dataset
long_data <- do.call(rbind, long_data_list)
long_data$age <- factor(long_data$age, levels = c(11, 14, 17), labels = c("Age11", "Age14", "Age17"))

# Remove rows with all missing data
complete_rows <- rowSums(!is.na(long_data[, common_base])) > 0
long_data <- long_data[complete_rows, ]

cat("Long format dataset created:\n")
cat("  Total observations:", nrow(long_data), "\n")
cat("  Age groups:", table(long_data$age), "\n")

# Define single-group bifactor model for multi-group analysis
single_group_syntax <- paste0(
  "# General factor\n",
  "General =~ ", paste(common_base, collapse = " + "), "\n\n",
  
  "# Specific factors\n", 
  "Executive =~ ", paste(executive_available, collapse = " + "), "\n",
  "SelfCent =~ ", paste(selfcent_available, collapse = " + "), "\n",
  "Temper =~ ", paste(temper_available, collapse = " + "), "\n\n",
  
  "# Orthogonal constraints\n",
  "General ~~ 0*Executive\n",
  "General ~~ 0*SelfCent\n",
  "General ~~ 0*Temper\n", 
  "Executive ~~ 0*SelfCent\n",
  "Executive ~~ 0*Temper\n",
  "SelfCent ~~ 0*Temper\n"
)

cat("Fitting threshold invariance model...\n")

# Fit threshold invariance model
model_threshold <- cfa(
  model = single_group_syntax,
  data = long_data,
  group = "age",
  estimator = "WLSMV",
  ordered = common_base,
  group.equal = "thresholds",
  std.lv = TRUE
)

if(lavInspect(model_threshold, "converged")) {
  cat("✓ Threshold invariance model converged successfully\n")
  
  fit_threshold <- fitMeasures(model_threshold, c(
    "chisq.scaled", "df", "pvalue.scaled",
    "cfi.scaled", "tli.scaled", "rmsea.scaled",
    "rmsea.ci.lower.scaled", "rmsea.ci.upper.scaled", "srmr"
  ))
  
  cat("Threshold Invariance Model Fit:\n")
  cat("  χ² (scaled) =", round(fit_threshold["chisq.scaled"], 3),
      ", df =", fit_threshold["df"],
      ", p =", round(fit_threshold["pvalue.scaled"], 3), "\n")
  cat("  CFI (scaled) =", round(fit_threshold["cfi.scaled"], 3), "\n")
  cat("  TLI (scaled) =", round(fit_threshold["tli.scaled"], 3), "\n")
  cat("  RMSEA (scaled) =", round(fit_threshold["rmsea.scaled"], 3),
      " [", round(fit_threshold["rmsea.ci.lower.scaled"], 3), ", ",
      round(fit_threshold["rmsea.ci.upper.scaled"], 3), "]\n")
  cat("  SRMR =", round(fit_threshold["srmr"], 3), "\n")
  
} else {
  cat("✗ Threshold invariance model did not converge\n")
}

# =============================================================================
# MODEL 3: THRESHOLD + LOADING INVARIANCE (STRONG INVARIANCE)
# =============================================================================

cat("\n--- MODEL 3: THRESHOLD + LOADING INVARIANCE (STRONG) ---\n") 
cat("Equal thresholds AND loadings across waves\n")

cat("Fitting strong invariance model...\n")

model_strong <- cfa(
  model = single_group_syntax,
  data = long_data,
  group = "age", 
  estimator = "WLSMV",
  ordered = common_base,
  group.equal = c("thresholds", "loadings"),
  std.lv = TRUE
)

if(lavInspect(model_strong, "converged")) {
  cat("✓ Strong invariance model converged successfully\n")
  
  fit_strong <- fitMeasures(model_strong, c(
    "chisq.scaled", "df", "pvalue.scaled",
    "cfi.scaled", "tli.scaled", "rmsea.scaled",
    "rmsea.ci.lower.scaled", "rmsea.ci.upper.scaled", "srmr"
  ))
  
  cat("Strong Invariance Model Fit:\n")
  cat("  χ² (scaled) =", round(fit_strong["chisq.scaled"], 3),
      ", df =", fit_strong["df"],
      ", p =", round(fit_strong["pvalue.scaled"], 3), "\n")
  cat("  CFI (scaled) =", round(fit_strong["cfi.scaled"], 3), "\n") 
  cat("  TLI (scaled) =", round(fit_strong["tli.scaled"], 3), "\n")
  cat("  RMSEA (scaled) =", round(fit_strong["rmsea.scaled"], 3),
      " [", round(fit_strong["rmsea.ci.lower.scaled"], 3), ", ",
      round(fit_strong["rmsea.ci.upper.scaled"], 3), "]\n")
  cat("  SRMR =", round(fit_strong["srmr"], 3), "\n")
  
} else {
  cat("✗ Strong invariance model did not converge\n")
}

# =============================================================================
# MODEL 4: STRICT INVARIANCE
# =============================================================================

cat("\n--- MODEL 4: STRICT INVARIANCE ---\n")
cat("Equal thresholds, loadings, AND residual variances\n")

cat("Fitting strict invariance model...\n")

model_strict <- cfa(
  model = single_group_syntax,
  data = long_data,
  group = "age",
  estimator = "WLSMV", 
  ordered = common_base,
  group.equal = c("thresholds", "loadings", "residuals"),
  std.lv = TRUE
)

if(lavInspect(model_strict, "converged")) {
  cat("✓ Strict invariance model converged successfully\n")
  
  fit_strict <- fitMeasures(model_strict, c(
    "chisq.scaled", "df", "pvalue.scaled",
    "cfi.scaled", "tli.scaled", "rmsea.scaled",
    "rmsea.ci.lower.scaled", "rmsea.ci.upper.scaled", "srmr"
  ))
  
  cat("Strict Invariance Model Fit:\n")
  cat("  χ² (scaled) =", round(fit_strict["chisq.scaled"], 3),
      ", df =", fit_strict["df"],
      ", p =", round(fit_strict["pvalue.scaled"], 3), "\n")
  cat("  CFI (scaled) =", round(fit_strict["cfi.scaled"], 3), "\n")
  cat("  TLI (scaled) =", round(fit_strict["tli.scaled"], 3), "\n")
  cat("  RMSEA (scaled) =", round(fit_strict["rmsea.scaled"], 3),
      " [", round(fit_strict["rmsea.ci.lower.scaled"], 3), ", ",
      round(fit_strict["rmsea.ci.upper.scaled"], 3), "]\n")
  cat("  SRMR =", round(fit_strict["srmr"], 3), "\n")
  
} else {
  cat("✗ Strict invariance model did not converge\n")
}

# =============================================================================
# 5. MODEL COMPARISON AND INVARIANCE TESTING
# =============================================================================

cat("\n=== MODEL COMPARISON AND INVARIANCE TESTS ===\n")

# Create comparison table
models_list <- list()
fits_list <- list()
model_names <- c()

# Add configural as baseline (convert to multi-group format)
cat("Fitting baseline configural model for comparison...\n")
model_configural_mg <- cfa(
  model = single_group_syntax,
  data = long_data,
  group = "age",
  estimator = "WLSMV",
  ordered = common_base,
  std.lv = TRUE
)

if(lavInspect(model_configural_mg, "converged")) {
  models_list[["Configural"]] <- model_configural_mg
  fits_list[["Configural"]] <- fitMeasures(model_configural_mg, c(
    "chisq.scaled", "df", "cfi.scaled", "tli.scaled", "rmsea.scaled", "srmr"
  ))
  model_names <- c(model_names, "Configural")
}

if(exists("model_threshold") && lavInspect(model_threshold, "converged")) {
  models_list[["Threshold"]] <- model_threshold
  fits_list[["Threshold"]] <- fit_threshold[c("chisq.scaled", "df", "cfi.scaled", "tli.scaled", "rmsea.scaled", "srmr")]
  model_names <- c(model_names, "Threshold")
}

if(exists("model_strong") && lavInspect(model_strong, "converged")) {
  models_list[["Strong"]] <- model_strong
  fits_list[["Strong"]] <- fit_strong[c("chisq.scaled", "df", "cfi.scaled", "tli.scaled", "rmsea.scaled", "srmr")]
  model_names <- c(model_names, "Strong")
}

if(exists("model_strict") && lavInspect(model_strict, "converged")) {
  models_list[["Strict"]] <- model_strict
  fits_list[["Strict"]] <- fit_strict[c("chisq.scaled", "df", "cfi.scaled", "tli.scaled", "rmsea.scaled", "srmr")]
  model_names <- c(model_names, "Strict")
}

# Create comparison table
if(length(fits_list) > 0) {
  comparison_table <- data.frame(
    Model = names(fits_list),
    ChiSq = sapply(fits_list, function(x) round(x["chisq.scaled"], 3)),
    df = sapply(fits_list, function(x) x["df"]),
    CFI = sapply(fits_list, function(x) round(x["cfi.scaled"], 3)),
    TLI = sapply(fits_list, function(x) round(x["tli.scaled"], 3)),
    RMSEA = sapply(fits_list, function(x) round(x["rmsea.scaled"], 3)),
    SRMR = sapply(fits_list, function(x) round(x["srmr"], 3))
  )
  
  cat("\nModel Comparison Table:\n")
  print(comparison_table)
  
  # Calculate fit differences
  if(length(fits_list) > 1) {
    cat("\n--- Fit Index Changes ---\n")
    for(i in 2:length(fits_list)) {
      current_model <- names(fits_list)[i]
      previous_model <- names(fits_list)[i-1]
      
      delta_cfi <- fits_list[[i]]["cfi.scaled"] - fits_list[[i-1]]["cfi.scaled"]
      delta_rmsea <- fits_list[[i]]["rmsea.scaled"] - fits_list[[i-1]]["rmsea.scaled"]
      
      cat(current_model, "vs", previous_model, ":\n")
      cat("  ΔCFI =", round(delta_cfi, 4), "\n")
      cat("  ΔRMSEA =", round(delta_rmsea, 4), "\n")
      
      # Interpret changes (Chen, 2007; Cheung & Rensvold, 2002)
      if(delta_cfi <= -0.01 || delta_rmsea >= 0.015) {
        cat("  → Invariance NOT supported (substantial deterioration)\n")
      } else if(delta_cfi <= -0.005 || delta_rmsea >= 0.010) {
        cat("  → Invariance QUESTIONABLE (moderate deterioration)\n")
      } else {
        cat("  → Invariance SUPPORTED (acceptable fit change)\n")
      }
      cat("\n")
    }
  }
}

# Chi-square difference tests
cat("--- Chi-Square Difference Tests ---\n")
if(length(models_list) > 1) {
  for(i in 2:length(models_list)) {
    current_name <- names(models_list)[i]
    previous_name <- names(models_list)[i-1]
    
    cat("Testing", current_name, "vs", previous_name, ":\n")
    
    tryCatch({
      diff_test <- anova(models_list[[i-1]], models_list[[i]])
      print(diff_test)
      
      if(nrow(diff_test) >= 2) {
        p_value <- diff_test[2, "Pr(>Chisq)"]
        if(!is.na(p_value)) {
          if(p_value < 0.05) {
            cat("  → Significant deterioration (p < 0.05) - invariance NOT supported\n")
          } else {
            cat("  → Non-significant change (p ≥ 0.05) - invariance supported\n")
          }
        }
      }
    }, error = function(e) {
      cat("  Error in chi-square difference test:", e$message, "\n")
    })
    cat("\n")
  }
} else {
  cat("Need at least 2 models for comparison\n")
}

# =============================================================================
# 6. SUMMARY AND RECOMMENDATIONS
# =============================================================================

cat("=== SUMMARY AND RECOMMENDATIONS ===\n\n")

cat("Measurement Invariance Testing Summary:\n")
cat("Scale: Bifactor Self-Control Scale\n")
cat("Waves: Age 11, 14, 17\n")
cat("Items:", length(common_base), "ordinal items\n")
cat("Estimator: WLSMV with theta parameterization\n\n")

# Determine highest level of invariance achieved
if(length(model_names) == 0) {
  cat("❌ NO MODELS CONVERGED\n")
  cat("Recommendations:\n")
  cat("1. Check data quality and missing data patterns\n")
  cat("2. Consider simpler models or different estimators\n")
  cat("3. Examine model identification issues\n")
  
} else {
  # Check which models have acceptable fit
  acceptable_models <- c()
  
  if("Configural" %in% names(fits_list)) {
    fit_cfg <- fits_list[["Configural"]]
    if(fit_cfg["cfi.scaled"] >= 0.95 && fit_cfg["rmsea.scaled"] <= 0.08) {
      acceptable_models <- c(acceptable_models, "Configural")
    }
  }
  
  if("Threshold" %in% names(fits_list)) {
    fit_thr <- fits_list[["Threshold"]]
    if(fit_thr["cfi.scaled"] >= 0.95 && fit_thr["rmsea.scaled"] <= 0.08) {
      acceptable_models <- c(acceptable_models, "Threshold")
    }
  }
  
  if("Strong" %in% names(fits_list)) {
    fit_str <- fits_list[["Strong"]]
    if(fit_str["cfi.scaled"] >= 0.95 && fit_str["rmsea.scaled"] <= 0.08) {
      acceptable_models <- c(acceptable_models, "Strong")
    }
  }
  
  if("Strict" %in% names(fits_list)) {
    fit_stc <- fits_list[["Strict"]]
    if(fit_stc["cfi.scaled"] >= 0.95 && fit_stc["rmsea.scaled"] <= 0.08) {
      acceptable_models <- c(acceptable_models, "Strict")
    }
  }
  
  cat("✅ CONVERGED MODELS:", paste(model_names, collapse = ", "), "\n")
  
  if(length(acceptable_models) > 0) {
    highest_invariance <- acceptable_models[length(acceptable_models)]
    cat("✅ HIGHEST SUPPORTED INVARIANCE LEVEL:", highest_invariance, "\n\n")
    
    if(highest_invariance == "Strict") {
      cat("🎉 EXCELLENT: Full strict invariance achieved!\n")
      cat("The bifactor self-control scale shows complete measurement invariance.\n")
      cat("You can confidently compare latent means and conduct longitudinal analyses.\n")
      
    } else if(highest_invariance == "Strong") {
      cat("✅ VERY GOOD: Strong invariance achieved!\n")
      cat("The scale shows threshold and loading invariance.\n")
      cat("You can compare latent means across ages with confidence.\n")
      cat("Strict invariance testing may be optional for most purposes.\n")
      
    } else if(highest_invariance == "Threshold") {
      cat("⚠️ PARTIAL: Only threshold invariance achieved.\n")
      cat("Thresholds are invariant but loadings differ across ages.\n")
      cat("Latent mean comparisons should be interpreted cautiously.\n")
      cat("Consider partial invariance testing or model modifications.\n")
      
    } else {
      cat("⚠️ MINIMAL: Only configural invariance achieved.\n")
      cat("Same factor structure but different thresholds/loadings.\n")
      cat("Avoid direct latent mean comparisons across ages.\n")
      cat("Focus on factor structure similarities.\n")
    }
    
  } else {
    cat("❌ NO ACCEPTABLE FIT: All models show poor fit\n")
    cat("Consider model modifications or alternative approaches.\n")
  }
}

cat("\n--- INTERPRETATION GUIDELINES ---\n")
cat("For ordinal data with WLSMV estimator:\n")
cat("• Configural: Same factor pattern across ages\n")
cat("• Threshold: + Equal response category thresholds\n")  
cat("• Strong: + Equal factor loadings (enables mean comparisons)\n")
cat("• Strict: + Equal residual variances (full invariance)\n\n")

cat("Acceptable Fit Criteria:\n")
cat("• CFI ≥ 0.95, TLI ≥ 0.95\n")
cat("• RMSEA ≤ 0.06 (≤ 0.08 acceptable)\n")
cat("• SRMR ≤ 0.08\n\n")

cat("Invariance Decision Rules:\n")
cat("• ΔCFI ≤ -0.010 or ΔRMSEA ≥ 0.015: Invariance NOT supported\n")
cat("• ΔCFI ≤ -0.005 or ΔRMSEA ≥ 0.010: Invariance questionable\n")
cat("• Changes smaller than above: Invariance supported\n\n")

cat("=== ANALYSIS COMPLETE ===\n") 
