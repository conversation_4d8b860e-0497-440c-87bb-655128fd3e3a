library(pacman)
p_load(stats, car, foreign, svMisc, devtools, roxygen2, lattice, psych, lavaan, semTools, psych, VIM)

# Define the subset of self-control variables to test
self_control_subset <- c(
  "sc11_task_completion",  # Sees tasks through to the end, good attention span
  "sc11_distracted",       # Is easily distracted, concentration wanders
  "sc11_fidgeting",        # Child is constantly fidgeting or squirming
  "sc11_think_act",        # Think things out before acting
  "sc11_restless",         # Child is restless, overactive, cannot stay still for long
  "sc11_temper",           # Often has temper tantrums or hot tempers
  "sc11_obedient",         # Generally obedient
  "sc11_lying"             # Lying or cheating
)
 
cat("=== TESTING SELF-CONTROL AS ONE LATENT CONCEPT ===\n")
cat("Variables being tested:", length(self_control_subset), "\n")
cat("Variables:", paste(self_control_subset, collapse = ", "), "\n\n")

# Check which variables are available in the dataset
available_vars <- self_control_subset[self_control_subset %in% names(merged_data)]
missing_vars <- self_control_subset[!self_control_subset %in% names(merged_data)]

cat("=== VARIABLE AVAILABILITY CHECK ===\n")
cat("Available variables:", length(available_vars), "/", length(self_control_subset), "\n")
cat("Available:", paste(available_vars, collapse = ", "), "\n")
if(length(missing_vars) > 0) {
  cat("Missing:", paste(missing_vars, collapse = ", "), "\n")
  cat("Note: Run recode_self_control.R first to create these variables\n")
}

# Create subset with available self-control variables and complete cases
sc_subset_data <- merged_data[, available_vars, drop = FALSE]
sc_subset_complete <- na.omit(sc_subset_data)

cat("\nSample sizes:\n")
cat("Original dataset:", nrow(merged_data), "\n")
cat("With subset self-control variables:", nrow(sc_subset_data), "\n")
cat("Complete cases:", nrow(sc_subset_complete), "\n")

# Check for sufficient sample size
if(nrow(sc_subset_complete) < 100) {
  warning("Small sample size (< 100) may affect reliability of factor analysis results")
}

####
## Check dimensionality
####

cat("\n=== DIMENSIONALITY ANALYSIS ===\n")

# Correlation matrix
cor_matrix <- cor(sc_subset_complete)
cat("Correlation matrix computed for", ncol(sc_subset_complete), "variables\n")

# Print correlation matrix
cat("\nCorrelation Matrix:\n")
print(round(cor_matrix, 3))

# Check for adequate correlations
mean_cor <- mean(cor_matrix[lower.tri(cor_matrix)])
cat("\nMean inter-item correlation:", round(mean_cor, 3), "\n")

if(mean_cor < 0.15) {
  warning("Low mean inter-item correlation (< 0.15) suggests items may not measure same construct")
} else if(mean_cor > 0.50) {
  warning("High mean inter-item correlation (> 0.50) suggests possible redundancy")
} else {
  cat("Mean inter-item correlation is in acceptable range (0.15-0.50)\n")
}

# Scree plot
check.scree <- fa(cor_matrix, fm = "pa", SMC = TRUE, rotate = "none")

# Create scree plot
scree_plot <- xyplot(check.scree$values ~ 1:ncol(cor_matrix),
                     aspect = 1,
                     type = "b",
                     col = "black",
                     xlab = "Factor",
                     ylab = "Eigenvalue",
                     main = "Scree Plot for Self-Control Subset Items",
                     pch = 16
)
print(scree_plot)

# Parallel analysis
cat("\nParallel Analysis:\n")
parallel_result <- fa.parallel(cor_matrix, n.obs = nrow(sc_subset_complete), fa = "fa")

#################
## EFA
#################

cat("\n=== EXPLORATORY FACTOR ANALYSIS ===\n")

# Initial EFA with 1 factor (our hypothesis)
cat("\nExploratory Factor Analysis (1 factor - testing unidimensionality):\n")
efa1 <- fa(cor_matrix, fm = "pa", nfactors = 1, SMC = TRUE, 
           residuals = TRUE, rotate = "none")
print(efa1)

# Factor loadings
cat("\nFactor loadings (1 factor):\n")
print(round(efa1$loadings, 3))

# Explained variance
cat("\nProportion of variance explained:", round(efa1$Vaccounted[2,1], 3), "\n")

# EFA with 2 factors to test against unidimensional hypothesis
cat("\nExploratory Factor Analysis (2 factors - testing against unidimensionality):\n")
efa2 <- fa(cor_matrix, fm = "pa", nfactors = 2, SMC = TRUE, 
           residuals = TRUE, rotate = "varimax")
print(efa2)

# Factor loadings for 2-factor solution
cat("\nFactor loadings (2 factors):\n")
print(round(efa2$loadings, 3))

# Compute polychoric correlations (appropriate for ordinal data)
cat("\nComputing polychoric correlation matrix for ordinal data...\n")
poly_matrix <- polychoric(sc_subset_complete)$rho
cat("Polychoric correlation matrix computed\n")

# EFA with 1 factor using polychoric correlations
cat("\nExploratory Factor Analysis (1 factor, polychoric correlations):\n")
efa1_poly <- fa(poly_matrix, fm = "pa", nfactors = 1, SMC = TRUE, 
                residuals = TRUE, rotate = "none")
print(efa1_poly)

#################
## CFA
#################

cat("\n=== CONFIRMATORY FACTOR ANALYSIS ===\n")

# CFA with 1 factor (testing our hypothesis that all items measure self-control)
cat("\nConfirmatory Factor Analysis (1 factor - Self-Control):\n")
cfa1_model <- "SelfControl =~ sc11_task_completion + sc11_distracted + sc11_fidgeting + sc11_think_act + sc11_restless + sc11_temper + sc11_obedient + sc11_lying"

cfa1 <- cfa(model = cfa1_model, 
            data = sc_subset_complete,
            estimator = "WLSMV",
            ordered = available_vars,
            std.lv = TRUE)

print(cfa1)
summary(cfa1, fit.measures = TRUE, standardized = TRUE, rsquare = TRUE)

# Extract key fit indices
fit_indices <- fitMeasures(cfa1, c("chisq", "df", "pvalue", "cfi", "tli", "rmsea", "rmsea.ci.lower", "rmsea.ci.upper", "srmr"))

cat("\n=== KEY FIT INDICES FOR 1-FACTOR MODEL ===\n")
cat("Chi-square:", round(fit_indices["chisq"], 3), "\n")
cat("df:", fit_indices["df"], "\n")
cat("p-value:", round(fit_indices["pvalue"], 3), "\n")
cat("CFI:", round(fit_indices["cfi"], 3), "\n")
cat("TLI:", round(fit_indices["tli"], 3), "\n")
cat("RMSEA:", round(fit_indices["rmsea"], 3), "\n")
cat("RMSEA 90% CI: [", round(fit_indices["rmsea.ci.lower"], 3), ", ", round(fit_indices["rmsea.ci.upper"], 3), "]\n")
cat("SRMR:", round(fit_indices["srmr"], 3), "\n")

# Interpretation of fit
cat("\n=== FIT INTERPRETATION ===\n")
if(fit_indices["cfi"] >= 0.95 & fit_indices["rmsea"] <= 0.06 & fit_indices["srmr"] <= 0.08) {
  cat("EXCELLENT FIT: The 1-factor model shows excellent fit to the data.\n")
} else if(fit_indices["cfi"] >= 0.90 & fit_indices["rmsea"] <= 0.08 & fit_indices["srmr"] <= 0.10) {
  cat("ACCEPTABLE FIT: The 1-factor model shows acceptable fit to the data.\n")
} else {
  cat("POOR FIT: The 1-factor model shows poor fit to the data.\n")
}

#################
## Reliability Analysis
#################

cat("\n=== RELIABILITY ANALYSIS ===\n")

# Cronbach's Alpha with error handling
alpha_result <- NULL
alpha_value <- NA

try({
  alpha_result <- alpha(sc_subset_complete)
  alpha_value <- alpha_result$total$raw_alpha
  cat("Cronbach's Alpha:", round(alpha_value, 3), "\n")
  cat("Alpha if item deleted:\n")
  print(round(alpha_result$alpha.drop, 3))
}, silent = TRUE)

if(is.null(alpha_result) || is.na(alpha_value)) {
  cat("Error calculating Cronbach's Alpha\n")
  alpha_value <- NA
}

# McDonald's Omega with error handling
cat("\nCalculating McDonald's Omega...\n")
omega_result <- NULL
omega_value <- NA

try({
  omega_result <- omega(sc_subset_complete, nfactors = 1, plot = FALSE)
  omega_value <- omega_result$omega.tot
  cat("McDonald's Omega (total):", round(omega_value, 3), "\n")
}, silent = TRUE)

if(is.null(omega_result) || is.na(omega_value)) {
  # Alternative: Use reliability from CFA
  cat("Using CFA-based reliability estimate...\n")
  standardized_loadings <- inspect(cfa1, "std")$lambda[,1]
  standardized_loadings <- standardized_loadings[standardized_loadings != 0]
  
  # Composite reliability
  sum_loadings <- sum(standardized_loadings)
  sum_loadings_sq <- sum(standardized_loadings^2)
  error_var <- length(standardized_loadings) - sum_loadings_sq
  
  composite_reliability <- (sum_loadings^2) / ((sum_loadings^2) + error_var)
  cat("Composite Reliability (from CFA):", round(composite_reliability, 3), "\n")
  omega_value <- composite_reliability
}

print(omega_result)
print(omega_value)
