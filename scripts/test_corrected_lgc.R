# =============================================================================
# TEST: CORRECTED SECOND-ORDER LGC IDENTIFICATION
# Verify that fixing indicator intercepts (not factor means) works properly
# =============================================================================

library(pacman)
p_load(lavaan, dplyr)

cat("=== TESTING CORRECTED IDENTIFICATION ===\n")

# Load data (simplified version for testing)
data_path <- "/home/<USER>/dissertation_folder/data"
if(file.exists(file.path(data_path, "merged1203.rds"))) {
  merged_data <- readRDS(file.path(data_path, "merged1203.rds"))
  cat("Data loaded successfully\n")
  
  # Check if recoded variables exist
  if(!"sc11_task_completion" %in% names(merged_data)) {
    cat("Running recoding scripts...\n")
    source("scripts/recode_self_control.R")
    source("scripts/recode_self_control_age14.R") 
    source("scripts/recode_self_control_age17.R")
  }
  
  # Test with a simple Executive Control model
  executive_items <- c("task_completion", "distracted", "think_act")
  exec_vars <- c(paste0("sc11_", executive_items),
                 paste0("sc14_", executive_items), 
                 paste0("sc17_", executive_items))
  
  test_data <- merged_data[, exec_vars, drop = FALSE]
  complete_rows <- rowSums(!is.na(test_data)) > 0
  test_data <- test_data[complete_rows, ]
  
  cat("Test sample size:", nrow(test_data), "\n")
  
  # CORRECTED Model: Fix indicator intercepts (not factor means)
  corrected_model <- '
  # First level: Executive factors
  Executive11 =~ sc11_task_completion + sc11_distracted + sc11_think_act
  Executive14 =~ sc14_task_completion + sc14_distracted + sc14_think_act
  Executive17 =~ sc17_task_completion + sc17_distracted + sc17_think_act
  
  # Second level: Growth factors  
  iExecutive =~ 1*Executive11 + 1*Executive14 + 1*Executive17
  sExecutive =~ 0*Executive11 + 1*Executive14 + 2*Executive17
  
  # CORRECT identification: Fix indicator intercepts
  sc11_task_completion ~ c1*1
  sc14_task_completion ~ c1*1
  sc17_task_completion ~ c1*1
  '
  
  cat("\nFitting corrected model...\n")
  fit_corrected <- tryCatch({
    sem(
      model = corrected_model,
      data = test_data,
      estimator = "WLSMV",
      ordered = exec_vars,
      missing = "pairwise"
    )
  }, error = function(e) {
    cat("Error:", e$message, "\n")
    return(NULL)
  })
  
  if(!is.null(fit_corrected) && lavInspect(fit_corrected, "converged")) {
    cat("✅ Corrected model converged successfully!\n")
    
    # Check growth parameter estimates
    params <- parameterEstimates(fit_corrected)
    
    # Extract intercept and slope means
    intercept_mean <- params[params$lhs == "iExecutive" & params$op == "~1", ]
    slope_mean <- params[params$lhs == "sExecutive" & params$op == "~1", ]
    
    cat("\n🎯 GROWTH PARAMETER RESULTS:\n")
    if(nrow(intercept_mean) > 0) {
      cat("Intercept mean:", round(intercept_mean$est, 4), 
          "(SE =", round(intercept_mean$se, 4), ", p =", round(intercept_mean$pvalue, 4), ")\n")
      if(intercept_mean$est != 0) {
        cat("✅ Intercept is NON-ZERO (good!)\n")
      } else {
        cat("❌ Intercept is zero (identification problem)\n")
      }
    }
    
    if(nrow(slope_mean) > 0) {
      cat("Slope mean:", round(slope_mean$est, 4), 
          "(SE =", round(slope_mean$se, 4), ", p =", round(slope_mean$pvalue, 4), ")\n")
      if(slope_mean$est != 0) {
        cat("✅ Slope is NON-ZERO (capturing development!)\n")
        if(!is.na(slope_mean$pvalue) && slope_mean$pvalue < 0.05) {
          if(slope_mean$est > 0) {
            cat("→ Significant INCREASE over time\n")
          } else {
            cat("→ Significant DECREASE over time\n")
          }
        }
      } else {
        cat("❌ Slope is zero (no development captured)\n")
      }
    }
    
    # Check factor means (should be free/non-zero)
    factor_means <- params[params$op == "~1" & grepl("Executive", params$lhs), ]
    cat("\n📊 FIRST-ORDER FACTOR MEANS:\n")
    for(i in 1:nrow(factor_means)) {
      cat(factor_means$lhs[i], "mean:", round(factor_means$est[i], 4), "\n")
    }
    
    # Model fit
    fit_measures <- fitMeasures(fit_corrected, c("cfi", "tli", "rmsea", "srmr"))
    cat("\n📈 MODEL FIT:\n")
    cat("CFI =", round(fit_measures["cfi"], 3), "\n")
    cat("TLI =", round(fit_measures["tli"], 3), "\n")
    cat("RMSEA =", round(fit_measures["rmsea"], 3), "\n")
    cat("SRMR =", round(fit_measures["srmr"], 3), "\n")
    
    cat("\n✅ CORRECTED IDENTIFICATION WORKING PROPERLY!\n")
    
  } else {
    cat("❌ Corrected model failed to converge\n")
  }
  
} else {
  cat("❌ Data file not found\n")
}

cat("\n=== TEST COMPLETE ===\n") 