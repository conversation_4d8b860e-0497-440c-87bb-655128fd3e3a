# ===================================================================
# MPLUS DATA DIAGNOSTICS SCRIPT
# Run this before exporting data to MPlus to identify issues
# ===================================================================

# Load required libraries
library(dplyr)
library(psych)

# ===================================================================
# 1. DEFINE VARIABLE MAPPINGS
# ===================================================================

# Original long variable names
original_vars <- c(
  # Age 11 self-control
  "sc11_task_completion", "sc11_distracted", "sc11_fidgeting", "sc11_think_act", "sc11_restless",
  "sc11_considerate", "sc11_sharing", "sc11_helpful", "sc11_volunteer_help",
  "sc11_temper", "sc11_obedient", "sc11_lying",
  
  # Age 14 self-control  
  "sc14_task_completion", "sc14_distracted", "sc14_fidgeting", "sc14_think_act", "sc14_restless",
  "sc14_considerate", "sc14_sharing", "sc14_helpful", "sc14_volunteer_help",
  "sc14_temper", "sc14_obedient", "sc14_lying",
  
  # Age 17 self-control
  "sc17_task_completion", "sc17_distracted", "sc17_fidgeting", "sc17_think_act", "sc17_restless",
  "sc17_considerate", "sc17_sharing", "sc17_helpful", "sc17_volunteer_help",
  "sc17_temper", "sc17_obedient", "sc17_lying",
  
  # Fighting behavior
  "agr11_fight", "agr14_fight", "agr17_fight"
)

# Shortened variable names for MPlus (8 characters max)
short_vars <- c(
  # Age 11 self-control
  "sc11_tc", "sc11_dis", "sc11_fid", "sc11_ta", "sc11_res",
  "sc11_con", "sc11_sha", "sc11_hel", "sc11_vol",
  "sc11_tem", "sc11_obe", "sc11_lyi",
  
  # Age 14 self-control
  "sc14_tc", "sc14_dis", "sc14_fid", "sc14_ta", "sc14_res",
  "sc14_con", "sc14_sha", "sc14_hel", "sc14_vol",
  "sc14_tem", "sc14_obe", "sc14_lyi",
  
  # Age 17 self-control
  "sc17_tc", "sc17_dis", "sc17_fid", "sc17_ta", "sc17_res",
  "sc17_con", "sc17_sha", "sc17_hel", "sc17_vol",
  "sc17_tem", "sc17_obe", "sc17_lyi",
  
  # Fighting behavior
  "agr11", "agr14", "agr17"
)

# Create mapping
var_mapping <- setNames(short_vars, original_vars)

cat("=== MPLUS DATA DIAGNOSTICS ===\n")
cat("Original variables:", length(original_vars), "\n")
cat("Shortened variables:", length(short_vars), "\n\n")

# ===================================================================
# 2. CHECK VARIABLE AVAILABILITY
# ===================================================================

cat("=== CHECKING VARIABLE AVAILABILITY ===\n")

# Load your dataset (adjust path as needed)
# data_path <- "/home/<USER>/dissertation_folder/data"
# merged_data <- readRDS(file.path(data_path, "merged1203.rds"))

# Check which original variables exist
available_vars <- original_vars[original_vars %in% names(merged_data)]
missing_vars <- original_vars[!original_vars %in% names(merged_data)]

cat("Variables found:", length(available_vars), "/", length(original_vars), "\n")

if(length(missing_vars) > 0) {
  cat("\n❌ MISSING VARIABLES:\n")
  for(var in missing_vars) {
    cat("  -", var, "\n")
  }
  cat("\n⚠️  You need to create these variables before proceeding!\n")
} else {
  cat("✅ All variables found in dataset\n")
}

# ===================================================================
# 3. DATA COMPLETENESS ANALYSIS
# ===================================================================

cat("\n=== DATA COMPLETENESS ANALYSIS ===\n")

if(length(available_vars) > 0) {
  # Overall completeness
  any_data <- rowSums(!is.na(merged_data[, available_vars, drop = FALSE])) > 0
  cat("Total cases in dataset:", nrow(merged_data), "\n")
  cat("Cases with ANY analysis data:", sum(any_data), "\n")
  cat("Cases with NO analysis data:", sum(!any_data), "\n")
  cat("Proportion with any data:", round(mean(any_data), 3), "\n\n")
  
  # Variable-specific completion rates
  completion_rates <- sapply(merged_data[, available_vars, drop = FALSE], function(x) {
    n_valid <- sum(!is.na(x))
    prop_valid <- mean(!is.na(x))
    c(n_valid = n_valid, prop_valid = prop_valid)
  })
  
  completion_df <- data.frame(
    Variable = available_vars,
    N_Valid = completion_rates["n_valid", ],
    Prop_Valid = round(completion_rates["prop_valid", ], 3)
  )
  completion_df <- completion_df[order(completion_df$Prop_Valid), ]
  
  cat("VARIABLE COMPLETION RATES (sorted by completeness):\n")
  print(completion_df, row.names = FALSE)
  
  # Identify problematic variables
  low_completion <- completion_df$Variable[completion_df$Prop_Valid < 0.1]
  if(length(low_completion) > 0) {
    cat("\n⚠️  Variables with <10% completion:\n")
    for(var in low_completion) {
      cat("  -", var, "(", round(completion_df$Prop_Valid[completion_df$Variable == var], 3), ")\n")
    }
  }
}

# ===================================================================
# 4. WAVE-SPECIFIC ANALYSIS
# ===================================================================

cat("\n=== WAVE-SPECIFIC COMPLETENESS ===\n")

waves <- c("11", "14", "17")
for(wave in waves) {
  wave_vars <- available_vars[grepl(paste0("sc", wave, "_|agr", wave), available_vars)]
  if(length(wave_vars) > 0) {
    any_wave_data <- rowSums(!is.na(merged_data[, wave_vars, drop = FALSE])) > 0
    cat("Age", wave, "variables (", length(wave_vars), "):", sum(any_wave_data), "cases with data\n")
  }
}

# ===================================================================
# 5. CONSTRUCT-SPECIFIC ANALYSIS  
# ===================================================================

cat("\n=== CONSTRUCT-SPECIFIC COMPLETENESS ===\n")

constructs <- list(
  "Executive Function" = grep("(tc|dis|fid|ta|res)", available_vars, value = TRUE),
  "Self-Centeredness" = grep("(con|sha|hel|vol)", available_vars, value = TRUE),
  "Temper" = grep("(tem|obe|lyi)", available_vars, value = TRUE),
  "Fighting" = grep("agr", available_vars, value = TRUE)
)

for(construct_name in names(constructs)) {
  construct_vars <- constructs[[construct_name]]
  if(length(construct_vars) > 0) {
    any_construct_data <- rowSums(!is.na(merged_data[, construct_vars, drop = FALSE])) > 0
    mean_completion <- mean(sapply(merged_data[, construct_vars, drop = FALSE], function(x) mean(!is.na(x))))
    cat(construct_name, ":", sum(any_construct_data), "cases,", 
        round(mean_completion, 3), "average completion\n")
  }
}

# ===================================================================
# 6. DESCRIPTIVE STATISTICS
# ===================================================================

cat("\n=== DESCRIPTIVE STATISTICS ===\n")

if(length(available_vars) > 0) {
  # Calculate descriptives for available variables
  desc_stats <- describe(merged_data[, available_vars, drop = FALSE])
  
  # Focus on key statistics
  desc_summary <- data.frame(
    Variable = rownames(desc_stats),
    N = desc_stats$n,
    Mean = round(desc_stats$mean, 2),
    SD = round(desc_stats$sd, 2),
    Min = desc_stats$min,
    Max = desc_stats$max,
    Skew = round(desc_stats$skew, 2)
  )
  
  cat("First 10 variables:\n")
  print(head(desc_summary, 10), row.names = FALSE)
  
  # Check for extreme values
  extreme_skew <- desc_summary$Variable[abs(desc_summary$Skew) > 2 & !is.na(desc_summary$Skew)]
  if(length(extreme_skew) > 0) {
    cat("\n⚠️  Variables with extreme skewness (|skew| > 2):\n")
    for(var in extreme_skew) {
      skew_val <- desc_summary$Skew[desc_summary$Variable == var]
      cat("  -", var, "(skew =", skew_val, ")\n")
    }
  }
}

# ===================================================================
# 7. CORRELATION ANALYSIS
# ===================================================================

cat("\n=== CORRELATION STRUCTURE CHECK ===\n")

if(length(available_vars) >= 2) {
  # Calculate correlations (pairwise complete)
  cor_matrix <- cor(merged_data[, available_vars, drop = FALSE], use = "pairwise.complete.obs")
  
  # Check for extremely high correlations (potential multicollinearity)
  high_cors <- which(abs(cor_matrix) > 0.9 & cor_matrix < 1, arr.ind = TRUE)
  
  if(nrow(high_cors) > 0) {
    cat("⚠️  Very high correlations (|r| > 0.9):\n")
    for(i in 1:nrow(high_cors)) {
      var1 <- rownames(cor_matrix)[high_cors[i, 1]]
      var2 <- colnames(cor_matrix)[high_cors[i, 2]]
      cor_val <- cor_matrix[high_cors[i, 1], high_cors[i, 2]]
      cat("  ", var1, "<->", var2, ":", round(cor_val, 3), "\n")
    }
  } else {
    cat("✅ No extremely high correlations found\n")
  }
  
  # Check for variables with no variance
  zero_var <- apply(merged_data[, available_vars, drop = FALSE], 2, function(x) var(x, na.rm = TRUE) == 0)
  if(any(zero_var, na.rm = TRUE)) {
    cat("\n❌ Variables with zero variance:\n")
    cat("  ", paste(names(zero_var)[zero_var], collapse = ", "), "\n")
  }
}

# ===================================================================
# 8. RECOMMENDATIONS
# ===================================================================

cat("\n=== RECOMMENDATIONS ===\n")

if(length(missing_vars) > 0) {
  cat("1. ❌ CREATE MISSING VARIABLES before proceeding\n")
}

if(sum(!any_data) > nrow(merged_data) * 0.5) {
  cat("2. ⚠️  More than 50% of cases have no analysis data - check data sources\n")
} else if(sum(!any_data) > 0) {
  cat("2. ✅ Remove", sum(!any_data), "cases with no analysis data\n")
}

if(length(available_vars) < length(original_vars)) {
  cat("3. ⚠️  Missing", length(original_vars) - length(available_vars), "variables - model may need modification\n")
} else {
  cat("3. ✅ All variables available for analysis\n")
}

min_completion <- min(completion_df$Prop_Valid, na.rm = TRUE)
if(min_completion < 0.3) {
  cat("4. ⚠️  Some variables have very low completion (<30%) - consider excluding\n")
} else {
  cat("4. ✅ Reasonable completion rates across variables\n")
}

final_n <- sum(any_data)
if(final_n < 200) {
  cat("5. ❌ Sample size too small for complex SEM (N =", final_n, ") - need N > 200\n")
} else if(final_n < 500) {
  cat("5. ⚠️  Modest sample size (N =", final_n, ") - monitor convergence closely\n") 
} else {
  cat("5. ✅ Adequate sample size (N =", final_n, ") for SEM analysis\n")
}

cat("\n=== NEXT STEPS ===\n")
cat("1. Address any ❌ issues above\n")
cat("2. Run variable recoding scripts if needed\n") 
cat("3. Export clean dataset with shortened variable names\n")
cat("4. Run MPlus models sequentially (configural → metric → scalar → growth)\n")

# ===================================================================
# 9. EXPORT CLEAN DATASET (IF READY)
# ===================================================================

if(length(missing_vars) == 0 && sum(any_data) >= 200) {
  cat("\n=== EXPORTING CLEAN DATASET ===\n")
  
  # Create clean dataset with shortened names
  clean_data <- merged_data[any_data, available_vars, drop = FALSE]
  names(clean_data) <- var_mapping[available_vars]
  
  # Replace missing with MPlus code
  clean_data[is.na(clean_data)] <- -999
  
  # Export
  write.table(clean_data, "merged1203_clean.dat", 
             row.names = FALSE, col.names = FALSE, 
             sep = "\t", na = "-999")
  
  cat("✅ Clean dataset exported: merged1203_clean.dat\n")
  cat("   Final N =", nrow(clean_data), "\n")
  cat("   Variables =", ncol(clean_data), "\n")
  
  # Create variable list for MPlus syntax
  cat("\n=== MPLUS VARIABLE LIST ===\n")
  cat("NAMES = \n")
  var_list <- paste(names(clean_data), collapse = " ")
  cat(var_list, ";\n")
  
} else {
  cat("\n⚠️  Dataset not ready for export - address issues above first\n")
}

cat("\n=== DIAGNOSTICS COMPLETE ===\n")