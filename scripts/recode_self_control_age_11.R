# =============================================================================
 # Recode Self-Control age 11 variables
# =============================================================================

# Load packages
library(pacman)
p_load(psych, corrplot, lavaan, semTools, VIM)

# =============================================================================
# 1. SELF-CONTROL VARIABLES DEFINITION
# =============================================================================

# Variables that NEED reverse coding
self_control_reverse <- c(
  "epsdst00",  # SDST: Think things out before acting
  "epsdgf00",  # Having at least one good friend
  "epsdlc00",   # Generally liked by other children
  "epsdpf00",  # Being considerate of other people's feelings
  "epsdsr00",  # Sharing readily with other children
  "epsdhu00",  # Being helpful if someone is hurt
  "epsdky00",  # Being kind to younger children
  "epsdvh00",  # Often volunteering to help others
  "epsdte00",  # Sees tasks through to the end, good attention span
  "epsdor00"   # Generally obedient
)

# Variables that do NOT need reverse coding
self_control_normal <- c(
  "epsdsp00",  # Being rather solitary and tending to play alone
  "epsddc00",  # Is easily distracted, concentration wanders
  "epsdgb00",  # Getting on better with adults than other children
  "epsdtt00",  # Often has temper tantrums or hot tempers
  "epsdmw00",  # Having many worries
  "epsdud00",  # Being often unhappy, down-hearted, or tearful
  "epsdnc00",  # Being nervous or clingy in new situations
  "epsdfe00",  # Having many fears, being easily scared
  "epsdpb00",  # Child is restless, overactive, cannot stay still for long
  "epsdfs00",  # Child is constantly fidgeting or squirming
  "epsdoa00"   # Lying or cheating
)

# Combined list of all self-control variables
all_self_control_vars <- c(self_control_reverse, self_control_normal)

# Note: epsdoa00 (lying/cheating) and epsdor00 (generally obedient) are handled in measurement_risk_taking.R

cat("=== SELF-CONTROL VARIABLES PROCESSING (AGE 11) ===\n")
cat("Variables requiring reverse coding:", length(self_control_reverse), "\n")
cat("Variables with normal coding:", length(self_control_normal), "\n")
cat("Total variables:", length(all_self_control_vars), "\n\n")

# =============================================================================
# 2. CHECK EXISTING VARIABLES
# =============================================================================

# Check which variables exist in the data
existing_vars <- all_self_control_vars[all_self_control_vars %in% names(merged_data)]
missing_vars <- all_self_control_vars[!all_self_control_vars %in% names(merged_data)]

cat("=== VARIABLE AVAILABILITY ===\n")
cat("Found variables (", length(existing_vars), "):", paste(existing_vars, collapse = ", "), "\n")
if(length(missing_vars) > 0) {
  cat("Missing variables (", length(missing_vars), "):", paste(missing_vars, collapse = ", "), "\n")
}
cat("\n")

# =============================================================================
# 3. DISPLAY FREQUENCY TABLES FOR EXISTING VARIABLES
# =============================================================================

cat("=== ORIGINAL VARIABLE DISTRIBUTIONS ===\n")
for(var in existing_vars) {
  cat("\n--- ", var, " ---\n")
  var_numeric <- as.vector(merged_data[[var]])
  print(table(var_numeric, useNA = "ifany"))
}

# =============================================================================
# 4. RECODE VARIABLES THAT NEED REVERSE CODING
# =============================================================================

cat("\n=== REVERSE CODING VARIABLES ===\n")

# SDST: Think things out before acting (reverse code)
if("epsdst00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$epsdst00)
  merged_data$sc11_think_act <- ifelse(var_numeric %in% c(-1, 4), NA,
                                       ifelse(var_numeric == 1, 2,
                                              ifelse(var_numeric == 2, 1,
                                                     ifelse(var_numeric == 3, 0, var_numeric))))
  cat("✓ Reverse coded: epsdst00 -> sc11_think_act\n")
}

# Having at least one good friend (reverse code)
if("epsdgf00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$epsdgf00)
  merged_data$sc11_good_friend <- ifelse(var_numeric %in% c(-1, 4), NA,
                                         ifelse(var_numeric == 1, 2,
                                                ifelse(var_numeric == 2, 1,
                                                       ifelse(var_numeric == 3, 0, var_numeric))))
  cat("✓ Reverse coded: epsdgf00 -> sc11_good_friend\n")
}

# Generally liked by other children (reverse code)
if("epsdlc00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$epsdlc00)
  merged_data$sc11_liked_children <- ifelse(var_numeric %in% c(-1, 4), NA,
                                            ifelse(var_numeric == 1, 2,
                                                   ifelse(var_numeric == 2, 1,
                                                          ifelse(var_numeric == 3, 0, var_numeric))))
  cat("✓ Reverse coded: epsdlc00 -> sc11_liked_children\n")
}



# Being considerate of other people's feelings (reverse code)
if("epsdpf00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$epsdpf00)
  merged_data$sc11_considerate <- ifelse(var_numeric %in% c(-1, 4), NA,
                                         ifelse(var_numeric == 1, 2,
                                                ifelse(var_numeric == 2, 1,
                                                       ifelse(var_numeric == 3, 0, var_numeric))))
  cat("✓ Reverse coded: epsdpf00 -> sc11_considerate\n")
}

# Sharing readily with other children (reverse code)
if("epsdsr00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$epsdsr00)
  merged_data$sc11_sharing <- ifelse(var_numeric %in% c(-1, 4), NA,
                                     ifelse(var_numeric == 1, 2,
                                            ifelse(var_numeric == 2, 1,
                                                   ifelse(var_numeric == 3, 0, var_numeric))))
  cat("✓ Reverse coded: epsdsr00 -> sc11_sharing\n")
}

# Being helpful if someone is hurt (reverse code)
if("epsdhu00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$epsdhu00)
  merged_data$sc11_helpful <- ifelse(var_numeric %in% c(-1, 4), NA,
                                     ifelse(var_numeric == 1, 2,
                                            ifelse(var_numeric == 2, 1,
                                                   ifelse(var_numeric == 3, 0, var_numeric))))
  cat("✓ Reverse coded: epsdhu00 -> sc11_helpful\n")
}

# Being kind to younger children (reverse code)
if("epsdky00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$epsdky00)
  merged_data$sc11_kind_younger <- ifelse(var_numeric %in% c(-1, 4), NA,
                                          ifelse(var_numeric == 1, 2,
                                                 ifelse(var_numeric == 2, 1,
                                                        ifelse(var_numeric == 3, 0, var_numeric))))
  cat("✓ Reverse coded: epsdky00 -> sc11_kind_younger\n")
}

# Often volunteering to help others (reverse code)
if("epsdvh00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$epsdvh00)
  merged_data$sc11_volunteer_help <- ifelse(var_numeric %in% c(-1, 4), NA,
                                            ifelse(var_numeric == 1, 2,
                                                   ifelse(var_numeric == 2, 1,
                                                          ifelse(var_numeric == 3, 0, var_numeric))))
  cat("✓ Reverse coded: epsdvh00 -> sc11_volunteer_help\n")
}

# Sees tasks through to the end, good attention span (reverse code)
if("epsdte00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$epsdte00)
  merged_data$sc11_task_completion <- ifelse(var_numeric %in% c(-1, 4), NA,
                                             ifelse(var_numeric == 1, 2,
                                                    ifelse(var_numeric == 2, 1,
                                                           ifelse(var_numeric == 3, 0, var_numeric))))
  cat("✓ Reverse coded: epsdte00 -> sc11_task_completion\n")
}

# Generally obedient (reverse code)
if("epsdor00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$epsdor00)
  merged_data$sc11_obedient <- ifelse(var_numeric %in% c(-1, 4), NA,
                                      ifelse(var_numeric == 1, 2,
                                             ifelse(var_numeric == 2, 1,
                                                    ifelse(var_numeric == 3, 0, var_numeric))))
  cat("✓ Reverse coded: epsdor00 -> sc11_obedient\n")
}

# =============================================================================
# 5. RECODE VARIABLES THAT DO NOT NEED REVERSE CODING
# =============================================================================

cat("\n=== NORMAL CODING VARIABLES ===\n")

# Being rather solitary and tending to play alone (normal coding)
if("epsdsp00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$epsdsp00)
  merged_data$sc11_solitary <- ifelse(var_numeric %in% c(-1, 4), NA,
                                      ifelse(var_numeric == 1, 0,
                                             ifelse(var_numeric == 2, 1,
                                                    ifelse(var_numeric == 3, 2, var_numeric))))
  cat("✓ Normal coded: epsdsp00 -> sc11_solitary\n")
}

# Is easily distracted, concentration wanders (normal coding)
if("epsddc00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$epsddc00)
  merged_data$sc11_distracted <- ifelse(var_numeric %in% c(-1, 4), NA,
                                        ifelse(var_numeric == 1, 0,
                                               ifelse(var_numeric == 2, 1,
                                                      ifelse(var_numeric == 3, 2, var_numeric))))
  cat("✓ Normal coded: epsddc00 -> sc11_distracted\n")
}

# Getting on better with adults than other children (normal coding)
if("epsdgb00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$epsdgb00)
  merged_data$sc11_better_adults <- ifelse(var_numeric %in% c(-1, 4), NA,
                                           ifelse(var_numeric == 1, 0,
                                                  ifelse(var_numeric == 2, 1,
                                                         ifelse(var_numeric == 3, 2, var_numeric))))
  cat("✓ Normal coded: epsdgb00 -> sc11_better_adults\n")
}

# Often has temper tantrums or hot tempers (normal coding)
if("epsdtt00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$epsdtt00)
  merged_data$sc11_temper <- ifelse(var_numeric %in% c(-1, 4), NA,
                                    ifelse(var_numeric == 1, 0,
                                           ifelse(var_numeric == 2, 1,
                                                  ifelse(var_numeric == 3, 2, var_numeric))))
  cat("✓ Normal coded: epsdtt00 -> sc11_temper\n")
}

# Having many worries (normal coding)
if("epsdmw00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$epsdmw00)
  merged_data$sc11_worries <- ifelse(var_numeric %in% c(-1, 4), NA,
                                     ifelse(var_numeric == 1, 0,
                                            ifelse(var_numeric == 2, 1,
                                                   ifelse(var_numeric == 3, 2, var_numeric))))
  cat("✓ Normal coded: epsdmw00 -> sc11_worries\n")
}

# Being often unhappy, down-hearted, or tearful (normal coding)
if("epsdud00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$epsdud00)
  merged_data$sc11_unhappy <- ifelse(var_numeric %in% c(-1, 4), NA,
                                     ifelse(var_numeric == 1, 0,
                                            ifelse(var_numeric == 2, 1,
                                                   ifelse(var_numeric == 3, 2, var_numeric))))
  cat("✓ Normal coded: epsdud00 -> sc11_unhappy\n")
}

# Being nervous or clingy in new situations (normal coding)
if("epsdnc00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$epsdnc00)
  merged_data$sc11_nervous <- ifelse(var_numeric %in% c(-1, 4), NA,
                                     ifelse(var_numeric == 1, 0,
                                            ifelse(var_numeric == 2, 1,
                                                   ifelse(var_numeric == 3, 2, var_numeric))))
  cat("✓ Normal coded: epsdnc00 -> sc11_nervous\n")
}

# Having many fears, being easily scared (normal coding)
if("epsdfe00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$epsdfe00)
  merged_data$sc11_fears <- ifelse(var_numeric %in% c(-1, 4), NA,
                                   ifelse(var_numeric == 1, 0,
                                          ifelse(var_numeric == 2, 1,
                                                 ifelse(var_numeric == 3, 2, var_numeric))))
  cat("✓ Normal coded: epsdfe00 -> sc11_fears\n")
}

# Child is restless, overactive, cannot stay still for long (normal coding)
if("epsdpb00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$epsdpb00)
  merged_data$sc11_restless <- ifelse(var_numeric %in% c(-1, 4), NA,
                                      ifelse(var_numeric == 1, 0,
                                             ifelse(var_numeric == 2, 1,
                                                    ifelse(var_numeric == 3, 2, var_numeric))))
  cat("✓ Normal coded: epsdpb00 -> sc11_restless\n")
}

# Child is constantly fidgeting or squirming (normal coding)
if("epsdfs00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$epsdfs00)
  merged_data$sc11_fidgeting <- ifelse(var_numeric %in% c(-1, 4), NA,
                                       ifelse(var_numeric == 1, 0,
                                              ifelse(var_numeric == 2, 1,
                                                     ifelse(var_numeric == 3, 2, var_numeric))))
  cat("✓ Normal coded: epsdfs00 -> sc11_fidgeting\n")
}

# Lying or cheating (normal coding)
if("epsdoa00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$epsdoa00)
  merged_data$sc11_lying <- ifelse(var_numeric %in% c(-1, 4), NA,
                                   ifelse(var_numeric == 1, 0,
                                          ifelse(var_numeric == 2, 1,
                                                 ifelse(var_numeric == 3, 2, var_numeric))))
  cat("✓ Normal coded: epsdoa00 -> sc11_lying\n")
}

# =============================================================================
# 6. CHECK RECODED VARIABLES
# =============================================================================

# List of all new self-control variables
sc11_vars_reverse <- c("sc11_think_act", "sc11_good_friend", "sc11_liked_children",
                       "sc11_considerate", "sc11_sharing", "sc11_helpful", "sc11_kind_younger", "sc11_volunteer_help",
                       "sc11_task_completion", "sc11_obedient")

sc11_vars_normal <- c("sc11_solitary", "sc11_distracted", "sc11_better_adults", "sc11_temper",
                      "sc11_worries", "sc11_unhappy", "sc11_nervous", "sc11_fears", "sc11_restless", "sc11_fidgeting", "sc11_lying")

all_sc11_vars <- c(sc11_vars_reverse, sc11_vars_normal)

# Check which recoded variables exist
existing_recoded <- all_sc11_vars[all_sc11_vars %in% names(merged_data)]

cat("\n=== RECODED VARIABLE DISTRIBUTIONS ===\n")
cat("Successfully recoded variables (", length(existing_recoded), "):\n")

for(var in existing_recoded) {
  cat("\n--- ", var, " ---\n")
  print(table(merged_data[[var]], useNA = "ifany"))
}

# =============================================================================
# 7. SUMMARY
# =============================================================================

cat("\n=== PROCESSING SUMMARY ===\n")
cat("Original variables found:", length(existing_vars), "/", length(all_self_control_vars), "\n")
cat("Variables requiring reverse coding:", length(sc11_vars_reverse), "(10 total)\n")
cat("Variables with normal coding:", length(sc11_vars_normal), "(11 total)\n")
cat("Successfully processed variables:", length(existing_recoded), "\n")

if(length(missing_vars) > 0) {
  cat("\nMissing variables that need to be checked:\n")
  for(var in missing_vars) {
    cat("- ", var, "\n")
  }
}

cat("\n=== VARIABLE MAPPING ===\n")
cat("Variables that need reverse coding (higher original score = lower self-control):\n")
reverse_mapping <- data.frame(
  Original = c("epsdst00", "epsdgf00", "epsdlc00", "epsdpf00", 
               "epsdsr00", "epsdhu00", "epsdky00", "epsdvh00", "epsdte00", "epsdor00"),
  New = c("sc11_think_act", "sc11_good_friend", "sc11_liked_children",
          "sc11_considerate", "sc11_sharing", "sc11_helpful", "sc11_kind_younger", "sc11_volunteer_help", 
          "sc11_task_completion", "sc11_obedient"),
  Description = c("Think things out before acting", "Having at least one good friend",
                  "Generally liked by other children",
                  "Being considerate of feelings", "Sharing readily with children",
                  "Being helpful if someone hurt", "Being kind to younger children",
                  "Often volunteering to help others", "Sees tasks through to end", "Generally obedient")
)
print(reverse_mapping)

cat("\nVariables with normal coding (higher score = lower self-control):\n")
normal_mapping <- data.frame(
  Original = c("epsdsp00", "epsddc00", "epsdgb00", "epsdtt00", "epsdmw00",
               "epsdud00", "epsdnc00", "epsdfe00", "epsdpb00", "epsdfs00", "epsdoa00"),
  New = c("sc11_solitary", "sc11_distracted", "sc11_better_adults", "sc11_temper", "sc11_worries",
          "sc11_unhappy", "sc11_nervous", "sc11_fears", "sc11_restless", "sc11_fidgeting", "sc11_lying"),
  Description = c("Being rather solitary", "Easily distracted", "Getting on better with adults",
                  "Often has temper tantrums", "Having many worries", "Often unhappy/tearful",
                  "Nervous in new situations", "Having many fears", "Restless/overactive",
                  "Constantly fidgeting", "Lying or cheating")
)
print(normal_mapping)

cat("\nNote: All age 11 self-control variables are now processed in this script.\n")
cat("All recoded variables have prefix 'sc11_' to distinguish from age 14 variables.\n")



