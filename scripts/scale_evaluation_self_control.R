####
## Scale Construction and Evaluation:
## Self-Control Scale (16 recoded items)
####

library(pacman)
p_load(stats, car, foreign, svMisc, devtools, roxygen2, lattice, psych)

####
## Load dataset with recoded self-control items
## Assumes merged_data is available with recoded variables from recode_self_control.R
####

# If merged_data is not in environment, load it
# merged_data <- readRDS("path/to/your/merged_data.rds")  # Uncomment and modify path as needed

# All 16 recoded self-control variables (all coded in same direction: higher = worse self-control)
# Note: Excluded social relationship variables: sc_solitary, sc_good_friend, sc_liked_children, 
#       sc_better_adults, sc_kind_younger (focus on core self-control behaviors)
self_control_vars <- c(
  # Variables that were reverse coded (positive behaviors → higher score = more problems)
  "sc_think_act",        # Think things out before acting
  "sc_considerate",      # Being considerate of other people's feelings
  "sc_sharing",          # Sharing readily with other children
  "sc_helpful",          # Being helpful if someone is hurt
  "sc_volunteer_help",   # Often volunteering to help others
  "sc_task_completion",  # Sees tasks through to the end, good attention span
  "sc_obedient",         # Generally obedient
  
  # Variables that were normal coded (negative behaviors → higher score = more problems)
  "sc_distracted",       # Is easily distracted, concentration wanders
  "sc_temper",           # Often has temper tantrums or hot tempers
  "sc_worries",          # Having many worries
  "sc_unhappy",          # Being often unhappy, down-hearted, or tearful
  "sc_nervous",          # Being nervous or clingy in new situations
  "sc_fears",            # Having many fears, being easily scared
  "sc_restless",         # Child is restless, overactive, cannot stay still for long
  "sc_fidgeting",        # Child is constantly fidgeting or squirming
  "sc_lying"             # Lying or cheating
)

# Check which recoded variables are available in the dataset
available_vars <- self_control_vars[self_control_vars %in% names(merged_data)]
missing_vars <- self_control_vars[!self_control_vars %in% names(merged_data)]

cat("=== RECODED VARIABLE AVAILABILITY CHECK ===\n")
cat("Available recoded variables:", length(available_vars), "/", length(self_control_vars), "\n")
cat("Available:", paste(available_vars, collapse = ", "), "\n")
if(length(missing_vars) > 0) {
  cat("Missing:", paste(missing_vars, collapse = ", "), "\n")
  cat("Note: Run recode_self_control.R first to create these variables\n")
}

# Create subset with available self-control variables and complete cases
sc_data <- merged_data[, available_vars, drop = FALSE]
sc_data_complete <- na.omit(sc_data)

cat("\nSample sizes:\n")
cat("Original dataset:", nrow(merged_data), "\n")
cat("With recoded self-control variables:", nrow(sc_data), "\n")
cat("Complete cases:", nrow(sc_data_complete), "\n")

# Check for sufficient sample size
if(nrow(sc_data_complete) < 100) {
  warning("Small sample size (< 100) may affect reliability of factor analysis results")
}

####
## Check dimensionality
####

cat("\n=== DIMENSIONALITY ANALYSIS ===\n")

# Correlation matrix
cor_matrix <- cor(sc_data_complete)
cat("Correlation matrix computed for", ncol(sc_data_complete), "recoded variables\n")

# Check for adequate correlations
mean_cor <- mean(cor_matrix[lower.tri(cor_matrix)])
cat("Mean inter-item correlation:", round(mean_cor, 3), "\n")

if(mean_cor < 0.15) {
  warning("Low mean inter-item correlation (< 0.15) suggests items may not measure same construct")
} else if(mean_cor > 0.50) {
  warning("High mean inter-item correlation (> 0.50) suggests possible redundancy")
}

# Scree plot
check.scree <- fa(cor_matrix, fm = "pa", SMC = TRUE, rotate = "none")

# Create scree plot
scree_plot <- xyplot(check.scree$values ~ 1:ncol(cor_matrix),
                     aspect = 1,
                     type = "b",
                     col = "black",
                     xlab = "Factor",
                     ylab = "Eigenvalue",
                     main = "Scree Plot for Recoded Self-Control Items",
                     pch = 16
)
print(scree_plot)

# Parallel analysis
cat("\nParallel Analysis:\n")
parallel_result <- fa.parallel(cor_matrix, n.obs = nrow(sc_data_complete), fa = "fa")

# Initial EFA with 1 factor
cat("\nExploratory Factor Analysis (1 factor):\n")
efa1 <- fa(cor_matrix, fm = "pa", nfactors = 1, SMC = TRUE, 
           residuals = TRUE, rotate = "none")
print(efa1)

# EFA with 2 factors to check for potential subscales
cat("\nExploratory Factor Analysis (2 factors):\n")
efa2 <- fa(cor_matrix, fm = "pa", nfactors = 2, SMC = TRUE, 
           residuals = TRUE, rotate = "varimax")
print(efa2)

# EFA with 3 factors to check for potential subscales
cat("\nExploratory Factor Analysis (3 factors):\n")
efa3 <- fa(cor_matrix, fm = "pa", nfactors = 3, SMC = TRUE, 
           residuals = TRUE, rotate = "varimax")
print(efa3)

# EFA with 4 factors to check for potential subscales
cat("\nExploratory Factor Analysis (4 factors):\n")
efa4 <- fa(cor_matrix, fm = "pa", nfactors = 4, SMC = TRUE, 
           residuals = TRUE, rotate = "varimax")
print(efa4)

# EFA with 3 factors using oblique rotation (allowing factors to correlate)
cat("\nExploratory Factor Analysis (3 factors, oblique rotation):\n")
efa3_oblique <- fa(cor_matrix, fm = "pa", nfactors = 3, SMC = TRUE, 
                   residuals = TRUE, rotate = "oblimin")
print(efa3_oblique)

# EFA with 4 factors using oblique rotation (allowing factors to correlate)
cat("\nExploratory Factor Analysis (4 factors, oblique rotation):\n")
efa4_oblique <- fa(cor_matrix, fm = "pa", nfactors = 4, SMC = TRUE, 
                   residuals = TRUE, rotate = "oblimin")
print(efa4_oblique)

# Compute polychoric correlations (appropriate for ordinal data)
cat("\nComputing polychoric correlation matrix for ordinal data...\n")
poly_matrix <- polychoric(sc_data_complete)$rho
cat("Polychoric correlation matrix computed\n")

# EFA with 3 factors using oblique rotation and polychoric correlations
cat("\nExploratory Factor Analysis (3 factors, oblique rotation, polychoric correlations):\n")
efa3_poly_oblique <- fa(poly_matrix, fm = "pa", nfactors = 3, SMC = TRUE, 
                        residuals = TRUE, rotate = "oblimin")
print(efa3_poly_oblique)

# EFA with 4 factors using oblique rotation and polychoric correlations
cat("\nExploratory Factor Analysis (4 factors, oblique rotation, polychoric correlations):\n")
efa4_poly_oblique <- fa(poly_matrix, fm = "pa", nfactors = 4, SMC = TRUE, 
                        residuals = TRUE, rotate = "oblimin")
print(efa4_poly_oblique)

####
## Identify factor structure and item assignments based on 4-factor solution
####

cat("\n=== 4-FACTOR SOLUTION ANALYSIS ===\n")
cat("Using efa4_poly_oblique as the best solution (4 factors, oblique, polychoric)\n")

# Extract factor loadings from 4-factor solution
loadings_4f <- efa4_poly_oblique$loadings
colnames(loadings_4f) <- paste0("Factor", 1:4)

cat("\nFactor loadings (4-factor solution):\n")
loading_df_4f <- data.frame(
  Variable = rownames(loadings_4f),
  Factor1 = round(loadings_4f[,1], 3),
  Factor2 = round(loadings_4f[,2], 3),
  Factor3 = round(loadings_4f[,3], 3),
  Factor4 = round(loadings_4f[,4], 3),
  Communality = round(efa4_poly_oblique$communality, 3),
  Uniqueness = round(efa4_poly_oblique$uniquenesses, 3)
)
print(loading_df_4f)

# Assign items to factors based on highest loading (>= 0.3)
factor_assignments <- list()
weak_items <- c()

for(i in 1:nrow(loadings_4f)) {
  item_name <- rownames(loadings_4f)[i]
  item_loadings <- abs(loadings_4f[i,])
  max_loading <- max(item_loadings)
  
  if(max_loading >= 0.3) {
    assigned_factor <- which.max(item_loadings)
    factor_assignments[[paste0("Factor", assigned_factor)]] <- c(factor_assignments[[paste0("Factor", assigned_factor)]], item_name)
  } else {
    weak_items <- c(weak_items, item_name)
  }
}

cat("\n=== FACTOR ASSIGNMENTS ===\n")
for(factor_name in names(factor_assignments)) {
  if(length(factor_assignments[[factor_name]]) > 0) {
    cat(paste0("\n", factor_name, " (", length(factor_assignments[[factor_name]]), " items):\n"))
    cat(paste(factor_assignments[[factor_name]], collapse = ", "), "\n")
  }
}

if(length(weak_items) > 0) {
  cat("\nWeak items (max |loading| < 0.3):\n")
  cat(paste(weak_items, collapse = ", "), "\n")
  cat("Consider removing these items\n")
} else {
  cat("\nNo weak items identified (all max |loadings| >= 0.3)\n")
}

# Display factor correlations
cat("\n=== FACTOR CORRELATIONS ===\n")
factor_cors <- efa4_poly_oblique$Phi
colnames(factor_cors) <- rownames(factor_cors) <- paste0("Factor", 1:4)
print(round(factor_cors, 3))

####
## Create subscales based on 4-factor structure
####

if(length(weak_items) > 0) {
  refined_vars <- available_vars[!available_vars %in% weak_items]
  cat("\nUsing refined variable set (", length(refined_vars), " items) after removing weak items\n")
  cat("Removed items:", paste(weak_items, collapse = ", "), "\n")
  
  # Update factor assignments to remove weak items
  for(factor_name in names(factor_assignments)) {
    factor_assignments[[factor_name]] <- factor_assignments[[factor_name]][!factor_assignments[[factor_name]] %in% weak_items]
  }
} else {
  refined_vars <- available_vars
  cat("\nUsing all available variables (", length(refined_vars), " items) in 4-factor structure\n")
}

refined_data <- sc_data_complete[, refined_vars, drop = FALSE]

# Reverse all variables for consistent scale direction (higher = better self-control)
cat("\nReversing all variables for scale construction (higher = better self-control):\n")
max_score <- 2  # Maximum possible score for recoded items (0, 1, 2)
refined_data_reversed <- max_score - refined_data
cat("All", ncol(refined_data_reversed), "variables reversed\n")

# Create subscale datasets based on factor assignments
subscales_data <- list()
for(factor_name in names(factor_assignments)) {
  if(length(factor_assignments[[factor_name]]) > 0) {
    # Keep only items that are in the refined dataset
    factor_items <- factor_assignments[[factor_name]][factor_assignments[[factor_name]] %in% refined_vars]
    if(length(factor_items) > 0) {
      subscales_data[[factor_name]] <- refined_data_reversed[, factor_items, drop = FALSE]
      cat(paste0("\n", factor_name, " subscale: ", length(factor_items), " items\n"))
    }
  }
}

####
## Create subscales and overall scale with reliability analysis
####

cat("\n=== SCALE RELIABILITY ANALYSIS ===\n")

# Subscale reliability and score creation
subscale_alphas <- list()
complete_cases <- complete.cases(merged_data[, refined_vars])

for(factor_name in names(subscales_data)) {
  if(ncol(subscales_data[[factor_name]]) >= 2) {  # Need at least 2 items for alpha
    cat(paste0("\n--- ", factor_name, " Subscale ---\n"))
    alpha_sub <- psych::alpha(subscales_data[[factor_name]])
    subscale_alphas[[factor_name]] <- alpha_sub
    print(alpha_sub)
    
    # Create subscale scores
    subscale_scores <- rowMeans(subscales_data[[factor_name]])
    scale_var_name <- paste0("self_control_", tolower(gsub("Factor", "factor", factor_name)))
    merged_data[[scale_var_name]] <- NA
    merged_data[[scale_var_name]][complete_cases] <- subscale_scores
    
    cat(paste0(factor_name, " subscale created: ", scale_var_name, "\n"))
    cat("Reliability (α):", round(alpha_sub$total$std.alpha, 3), "\n")
    cat("Items:", ncol(subscales_data[[factor_name]]), "\n")
  } else if(ncol(subscales_data[[factor_name]]) == 1) {
    cat(paste0("\n", factor_name, ": Single item - no reliability estimate\n"))
    # Still create the scale for single items
    scale_var_name <- paste0("self_control_", tolower(gsub("Factor", "factor", factor_name)))
    merged_data[[scale_var_name]] <- NA
    merged_data[[scale_var_name]][complete_cases] <- subscales_data[[factor_name]][,1]
  }
}

# Overall scale reliability and score creation  
cat("\n--- Overall Scale ---\n")
alpha_result <- psych::alpha(refined_data_reversed)
print(alpha_result)

# Create overall scale score
scale_scores <- rowMeans(refined_data_reversed)
merged_data$self_control_scale <- NA
merged_data$self_control_scale[complete_cases] <- scale_scores

cat("\nOverall scale created with", length(refined_vars), "items\n")
cat("Overall scale reliability (Cronbach's alpha):", round(alpha_result$total$std.alpha, 3), "\n")
cat("Mean inter-item correlation:", round(alpha_result$total$average_r, 3), "\n")

# Distribution of scale scores
scale_hist <- histogram(~scale_scores, 
                        aspect = 1,
                        main = "Distribution of Self-Control Scale Scores",
                        xlab = "Self-Control Scale Score (Higher = Better Self-Control)",
                        col = "lightblue"
)
print(scale_hist)

# Basic descriptive statistics
cat("\nScale score descriptives:\n")
cat("Mean:", round(mean(scale_scores), 3), "\n")
cat("SD:", round(sd(scale_scores), 3), "\n")
cat("Range:", round(min(scale_scores), 3), "-", round(max(scale_scores), 3), "\n")
cat("Skewness:", round(psych::skew(scale_scores), 3), "\n")
cat("Kurtosis:", round(psych::kurtosi(scale_scores), 3), "\n")



####
## Scale optimization function (adapted from original)
####

scale4me <- function (data, 
                      scale.vars,
                      length = 4,
                      reliab = c("alpha", "lambda6", "lambda4",
                                 "beta", "glb", "omega"), 
                      cutoff = 0.70,
                      crit.vars = NULL,
                      optimize = c("reliab", "eigen")) {
  
  var.names <- colnames(data[,scale.vars])  
  
  if (optimize == "reliab") {
    
    allComb <- t(combn(x = var.names, m = length))
    results <- as.data.frame(allComb)
    results$reliab <- rep(NA, times = nrow(allComb))
    
    comb <- nrow(allComb)
    print(paste0("# of combinations: ", comb), quote = FALSE)
    
    if (reliab == "alpha") {
      for (i in 1:nrow(allComb)) {
        vars <- allComb[i,]
        results[i,]$reliab <- psych::alpha(data[,vars])$total$std.alpha
      }
    }
    
    if (reliab == "lambda6") {
      for (i in 1:nrow(allComb)) {
        vars <- allComb[i,]
        results[i,]$reliab <- psych::alpha(data[,vars])$total$G6
      }
    }
    
    if (reliab == "lambda4") {
      for (i in 1:nrow(allComb)) {
        vars <- allComb[i,]
        results[i,]$reliab <- psych::splitHalf(data[,vars])$maxrb
      }
    }
    
    if (reliab == "beta") {
      for (i in 1:nrow(allComb)) {
        vars <- allComb[i,]
        results[i,]$reliab <- psych::splitHalf(data[,vars])$minrb
      }
    }
    
    if (reliab == "glb") {
      for (i in 1:nrow(allComb)) {
        vars <- allComb[i,]
        results[i,]$reliab <- psych::glb(data[,vars])$glb.Fa
      }
    }
    
    if (reliab == "omega" & length >= 9) {
      for (i in 1:nrow(allComb)) {
        vars <- allComb[i,]
        results[i,]$reliab <- psych::omega(data[,vars], plot=FALSE)$omega.tot
      }
    }
    
    if (reliab == "omega" & length < 9) {
      stop ("Omega function requires 3 factors for identification.
          Can only be performed on scale of length >= 9" )
    }
    
    results.all <- results
    results.cut <- results[which(results$reliab>=cutoff),]
    
    if (!is.null(crit.vars) && nrow(results.cut) > 0) {
      results.cut$corr <- rep(NA, times = nrow(results.cut))
      
      scale <- matrix(data = NA, nrow = nrow(data), ncol = nrow(results.cut))
      
      for (i in 1:nrow(results.cut)) {
        vars.r <- as.matrix(results.cut[i,1:length])
        scale[,i] <- apply(data[,vars.r], MARGIN = 1, FUN = mean)
        results.cut[i,]$corr <- cor(scale[,i], crit.vars, use="pairwise.complete.obs")
      }
      
      results.cor <- results.cut
      crit.max <- results.cor[which.max(abs(results.cor$corr)),]
    } else {
      results.cor <- results.cut
      crit.max <- if(nrow(results.cut) > 0) results.cut[which.max(results.cut$reliab),] else NULL
    }
    
    mean_reliab <- mean(results.all$reliab, na.rm = TRUE)
    sd_reliab <- sd(results.all$reliab, na.rm = TRUE)
    
  }
  
  if (optimize == "eigen") {
    
    allComb <- t(combn(x = var.names, m = length))
    results <- as.data.frame(allComb)
    results$eigen <- rep(NA, times = nrow(allComb))
    
    for (i in 1:nrow(allComb)) {
      vars <- allComb[i,]
      results[i,]$eigen <- fa(cor(data[,vars]))$values[1]
    }
    
    results.all <- results
    results.cut <- results[which(results$eigen >= quantile(results$eigen,prob=1-25/100)),]
    
    if (!is.null(crit.vars) && nrow(results.cut) > 0) {
      results.cut$corr <- rep(NA, times = nrow(results.cut))
      
      scale <- matrix(data = NA, nrow = nrow(data), ncol = nrow(results.cut))
      
      for (i in 1:nrow(results.cut)) {
        vars.r <- as.matrix(results.cut[i,1:length])
        scale[,i] <- apply(data[,vars.r], MARGIN = 1, FUN = mean)
        results.cut[i,]$corr <- cor(scale[,i], crit.vars, use="pairwise.complete.obs")
      }
      
      results.cor <- results.cut
      crit.max <- results.cor[which.max(abs(results.cor$corr)),]
    } else {
      results.cor <- results.cut
      crit.max <- if(nrow(results.cut) > 0) results.cut[which.max(results.cut$eigen),] else NULL
    }
    
    mean_reliab <- mean(results.all$eigen, na.rm = TRUE)
    sd_reliab <- sd(results.all$eigen, na.rm = TRUE)
    
    comb <- nrow(allComb)
    print(paste0("# of combinations: ", comb), quote = FALSE)
  }
  
  scale4meout <- list(results.all, results.cor, mean_reliab, sd_reliab, crit.max)
  names(scale4meout) <- c("results.all", "results.cor", "mean", "sd", "crit.max")
  
  scale4meout
}

####
## Scale Optimization with scale4me function
####

cat("\n=== SCALE OPTIMIZATION: 13-ITEM SOLUTION ===\n")

# Use scale4me to find optimal 13-item combination
cat("Testing all possible 13-item combinations from", ncol(refined_data_reversed), "variables...\n")

optimal_13item <- scale4me(
  data = refined_data_reversed,
  scale.vars = colnames(refined_data_reversed),
  length = 13,
  reliab = "alpha",
  cutoff = 0.70,
  optimize = "reliab"
)

# Display results
cat("\nOptimization completed!\n")
cat("Total combinations tested:", nrow(optimal_13item$results.all), "\n")
cat("Combinations above cutoff (α ≥ 0.70):", nrow(optimal_13item$results.cor), "\n")
cat("Mean reliability across all combinations:", round(optimal_13item$mean, 3), "\n")
cat("SD of reliability:", round(optimal_13item$sd, 3), "\n")

# Show best combination
if(!is.null(optimal_13item$crit.max)) {
  cat("\n=== OPTIMAL 13-ITEM COMBINATION ===\n")
  best_items <- as.character(optimal_13item$crit.max[1:13])
  best_reliability <- optimal_13item$crit.max$reliab
  
  cat("Best 13-item combination:\n")
  for(i in 1:13) {
    cat(paste0(i, ". ", best_items[i], "\n"))
  }
  cat("Cronbach's alpha:", round(best_reliability, 3), "\n")
  
  # Create optimal scale dataset
  optimal_scale_data <- refined_data_reversed[, best_items]
  
  # Validate the optimal scale
  cat("\n=== VALIDATION OF OPTIMAL SCALE ===\n")
  alpha_validation <- psych::alpha(optimal_scale_data)
  print(alpha_validation)
  
  # Create scale scores
  optimal_scale_scores <- rowMeans(optimal_scale_data)
  
  # Add to main dataset
  complete_cases <- complete.cases(merged_data[, refined_vars])
  merged_data$self_control_optimal_13item <- NA
  merged_data$self_control_optimal_13item[complete_cases] <- optimal_scale_scores
  
  cat("\n=== OPTIMAL SCALE DESCRIPTIVES ===\n")
  cat("Mean:", round(mean(optimal_scale_scores), 3), "\n")
  cat("SD:", round(sd(optimal_scale_scores), 3), "\n")
  cat("Range:", round(min(optimal_scale_scores), 3), "-", round(max(optimal_scale_scores), 3), "\n")
  cat("Skewness:", round(psych::skew(optimal_scale_scores), 3), "\n")
  cat("Kurtosis:", round(psych::kurtosi(optimal_scale_scores), 3), "\n")
  
     # Distribution plot
   optimal_hist <- histogram(~optimal_scale_scores, 
                            aspect = 1,
                            main = "Distribution of Optimal 13-Item Self-Control Scale",
                            xlab = "Optimal Scale Score (Higher = Better Self-Control)",
                            col = "lightgreen"
   )
   print(optimal_hist)
   
   # Show top 10 combinations for comparison
   cat("\n=== TOP 10 COMBINATIONS ===\n")
   top_10 <- optimal_13item$results.cor[order(optimal_13item$results.cor$reliab, decreasing = TRUE)[1:min(10, nrow(optimal_13item$results.cor))], ]
   rownames(top_10) <- 1:nrow(top_10)
   print(top_10)
   
   # Compare with full 16-item scale
   cat("\n=== COMPARISON: 13-ITEM vs 16-ITEM SCALE ===\n")
   
   # Correlation between optimal 13-item and full 16-item scale
   scale_correlation <- cor(optimal_scale_scores, scale_scores, use = "complete.obs")
   cat("Correlation between optimal 13-item and full 16-item scale:", round(scale_correlation, 3), "\n")
   
   # Reliability comparison
   cat("Reliability comparison:\n")
   cat("- Full 16-item scale:", round(alpha_result$total$std.alpha, 3), "\n")
   cat("- Optimal 13-item scale:", round(best_reliability, 3), "\n")
   cat("- Reliability difference:", round(best_reliability - alpha_result$total$std.alpha, 3), "\n")
   
   # Efficiency ratio (reliability per item)
   efficiency_16 <- alpha_result$total$std.alpha / length(refined_vars)
   efficiency_13 <- best_reliability / 13
   cat("Efficiency (reliability per item):\n")
   cat("- Full 16-item scale:", round(efficiency_16, 3), "\n")
   cat("- Optimal 13-item scale:", round(efficiency_13, 3), "\n")
   cat("- Efficiency gain:", round(efficiency_13 / efficiency_16, 2), "x\n")
  
 } else {
   cat("\nNo 13-item combinations met the reliability cutoff of 0.70\n")
   cat("Consider lowering the cutoff or examining the data quality\n")
   
   # Show best combinations even if below cutoff
   if(nrow(optimal_13item$results.all) > 0) {
     cat("\n=== BEST COMBINATIONS (Below Cutoff) ===\n")
     best_below_cutoff <- optimal_13item$results.all[order(optimal_13item$results.all$reliab, decreasing = TRUE)[1:5], ]
     print(best_below_cutoff)
   }
 }
 
 # Additional reliability measures for comparison
 cat("\n=== ALTERNATIVE RELIABILITY MEASURES ===\n")
 
 if(!is.null(optimal_13item$crit.max)) {
   cat("Testing alternative reliability measures for optimal 13-item scale:\n")
   
   # Lambda6 (Guttman's lambda 6)
   lambda6_13item <- scale4me(
     data = refined_data_reversed,
     scale.vars = colnames(refined_data_reversed),
     length = 13,
     reliab = "lambda6",
     cutoff = 0.70,
     optimize = "reliab"
   )
   
   if(!is.null(lambda6_13item$crit.max)) {
     cat("Best 13-item combination using Lambda6:\n")
     cat("Items:", paste(as.character(lambda6_13item$crit.max[1:13]), collapse = ", "), "\n")
     cat("Lambda6:", round(lambda6_13item$crit.max$reliab, 3), "\n")
   }
   
   # GLB (Greatest Lower Bound)
   glb_13item <- scale4me(
     data = refined_data_reversed,
     scale.vars = colnames(refined_data_reversed),
     length = 13,
     reliab = "glb",
     cutoff = 0.70,
     optimize = "reliab"
   )
   
   if(!is.null(glb_13item$crit.max)) {
     cat("Best 13-item combination using GLB:\n")
     cat("Items:", paste(as.character(glb_13item$crit.max[1:13]), collapse = ", "), "\n")
     cat("GLB:", round(glb_13item$crit.max$reliab, 3), "\n")
   }
 }
 
 cat("\n=== SCALE OPTIMIZATION COMPLETE ===\n")
 cat("Optimal 13-item scale saved as: merged_data$self_control_optimal_13item\n")



####
## Confirmatory Factor Analysis (CFA)
####

cat("\n=== CONFIRMATORY FACTOR ANALYSIS ===\n")

# Load lavaan for CFA
if (!require(lavaan, quietly = TRUE)) {
  install.packages("lavaan")
  library(lavaan)
}

# Map variables to factors based on EFA recommendations
# Note: Using original variable names as they appear in the dataset
emotional_items <- c("sc_worries", "sc_unhappy", "sc_nervous", "sc_fears")
attention_items <- c("sc_distracted", "sc_task_completion", "sc_think_act", "sc_fidgeting", "sc_restless")
prosociality_items <- c("sc_helpful", "sc_considerate", "sc_sharing", "sc_volunteer_help")
conduct_items <- c("sc_temper", "sc_lying", "sc_obedient")

# Create dataset for CFA (using original coding before reversal for scale construction)
cfa_data <- sc_data_complete[, refined_vars, drop = FALSE]

cat("CFA dataset prepared with", nrow(cfa_data), "complete cases and", ncol(cfa_data), "variables\n")

####
## 4-Factor CFA Model
####

cat("\n--- 4-Factor CFA Model ---\n")

# Define 4-factor CFA model
fourfactor_model <- '
  # Factor loadings (all variables coded as higher = worse self-control)
  Emotional =~ sc_worries + sc_unhappy + sc_nervous + sc_fears 
  Attention =~ sc_distracted + sc_task_completion + sc_think_act + sc_fidgeting + sc_restless
  Prosociality =~ sc_helpful + sc_considerate + sc_sharing + sc_volunteer_help
  Conduct =~ sc_temper + sc_lying + sc_obedient
  
  # Allow factors to correlate (oblique model)
  Emotional ~~ Attention
  Emotional ~~ Prosociality
  Emotional ~~ Conduct
  Attention ~~ Prosociality
  Attention ~~ Conduct
  Prosociality ~~ Conduct
'

# Fit 4-factor CFA with robust MLR estimator
cat("Fitting 4-factor CFA model with robust MLR estimator...\n")
cfa_4factor <- cfa(fourfactor_model, 
                   data = cfa_data, 
                   estimator = "MLR",
                   std.lv = TRUE,
                   missing = "fiml")

# Model summary
cat("\n4-Factor CFA Results:\n")
summary(cfa_4factor, fit.measures = TRUE, standardized = TRUE, rsquare = TRUE)

# Extract key fit indices
fit_4factor <- fitMeasures(cfa_4factor, c("chisq.scaled", "df", "pvalue.scaled", 
                                          "cfi.scaled", "tli.scaled", "rmsea.scaled", 
                                          "rmsea.ci.lower.scaled", "rmsea.ci.upper.scaled",
                                          "srmr"))

cat("\n--- 4-Factor CFA Fit Summary ---\n")
cat("χ² (scaled):", round(fit_4factor["chisq.scaled"], 3), "\n")
cat("df:", fit_4factor["df"], "\n")
cat("p-value:", round(fit_4factor["pvalue.scaled"], 3), "\n")
cat("CFI (scaled):", round(fit_4factor["cfi.scaled"], 3), "\n")
cat("TLI (scaled):", round(fit_4factor["tli.scaled"], 3), "\n")
cat("RMSEA (scaled):", round(fit_4factor["rmsea.scaled"], 3), 
    " [", round(fit_4factor["rmsea.ci.lower.scaled"], 3), ", ", 
    round(fit_4factor["rmsea.ci.upper.scaled"], 3), "]\n")
cat("SRMR:", round(fit_4factor["srmr"], 3), "\n")

# Factor correlations
cat("\n--- Factor Correlations (4-Factor CFA) ---\n")
cfa_factor_cors <- lavInspect(cfa_4factor, "cor.lv")
print(round(cfa_factor_cors, 3))

####
## Bifactor CFA Model  
####

cat("\n--- Bifactor CFA Model ---\n")

# Define bifactor model (general factor + 4 specific factors)
bifactor_model <- '
  # General factor (all items load on this)
  General =~ sc_worries + sc_unhappy + sc_nervous + sc_fears + sc_restless +
             sc_distracted + sc_task_completion + sc_think_act + sc_fidgeting +
             sc_helpful + sc_considerate + sc_sharing + sc_volunteer_help +
             sc_temper + sc_lying + sc_obedient
  
  # Specific factors (orthogonal to general and each other)
  S_Emotional =~ sc_worries + sc_unhappy + sc_nervous + sc_fears 
  S_Attention =~ sc_distracted + sc_task_completion + sc_think_act + sc_fidgeting + sc_restless
  S_Prosociality =~ sc_helpful + sc_considerate + sc_sharing + sc_volunteer_help
  S_Conduct =~ sc_temper + sc_lying + sc_obedient
  
  # Orthogonal constraints (specific factors uncorrelated with general and each other)
  General ~~ 0*S_Emotional + 0*S_Attention + 0*S_Prosociality + 0*S_Conduct
  S_Emotional ~~ 0*S_Attention + 0*S_Prosociality + 0*S_Conduct
  S_Attention ~~ 0*S_Prosociality + 0*S_Conduct
  S_Prosociality ~~ 0*S_Conduct
'

# Fit bifactor CFA with robust MLR estimator
cat("Fitting bifactor CFA model with robust MLR estimator...\n")
cfa_bifactor <- cfa(bifactor_model, 
                    data = cfa_data, 
                    estimator = "MLR",
                    std.lv = TRUE,
                    missing = "fiml")

# Model summary
cat("\nBifactor CFA Results:\n")
summary(cfa_bifactor, fit.measures = TRUE, standardized = TRUE, rsquare = TRUE)

# Extract key fit indices
fit_bifactor <- fitMeasures(cfa_bifactor, c("chisq.scaled", "df", "pvalue.scaled", 
                                            "cfi.scaled", "tli.scaled", "rmsea.scaled", 
                                            "rmsea.ci.lower.scaled", "rmsea.ci.upper.scaled",
                                            "srmr"))

cat("\n--- Bifactor CFA Fit Summary ---\n")
cat("χ² (scaled):", round(fit_bifactor["chisq.scaled"], 3), "\n")
cat("df:", fit_bifactor["df"], "\n")
cat("p-value:", round(fit_bifactor["pvalue.scaled"], 3), "\n")
cat("CFI (scaled):", round(fit_bifactor["cfi.scaled"], 3), "\n")
cat("TLI (scaled):", round(fit_bifactor["tli.scaled"], 3), "\n")
cat("RMSEA (scaled):", round(fit_bifactor["rmsea.scaled"], 3), 
    " [", round(fit_bifactor["rmsea.ci.lower.scaled"], 3), ", ", 
    round(fit_bifactor["rmsea.ci.upper.scaled"], 3), "]\n")
cat("SRMR:", round(fit_bifactor["srmr"], 3), "\n")

####
## Model Comparison
####

cat("\n--- Model Comparison ---\n")

# Compare models using chi-square difference test
cat("Comparing 4-factor vs Bifactor models:\n")
model_comparison <- anova(cfa_4factor, cfa_bifactor)
print(model_comparison)

# Fit comparison table
comparison_table <- data.frame(
  Model = c("4-Factor CFA", "Bifactor CFA"),
  ChiSq = round(c(fit_4factor["chisq.scaled"], fit_bifactor["chisq.scaled"]), 3),
  df = c(fit_4factor["df"], fit_bifactor["df"]),
  CFI = round(c(fit_4factor["cfi.scaled"], fit_bifactor["cfi.scaled"]), 3),
  TLI = round(c(fit_4factor["tli.scaled"], fit_bifactor["tli.scaled"]), 3),
  RMSEA = round(c(fit_4factor["rmsea.scaled"], fit_bifactor["rmsea.scaled"]), 3),
  SRMR = round(c(fit_4factor["srmr"], fit_bifactor["srmr"]), 3)
)

cat("\nFit Index Comparison:\n")
print(comparison_table)

# Interpretation of fit indices
cat("\n--- Fit Index Interpretation ---\n")
cat("Good fit criteria:\n")
cat("- CFI ≥ 0.95, TLI ≥ 0.95\n")
cat("- RMSEA ≤ 0.06 (≤ 0.08 acceptable)\n")
cat("- SRMR ≤ 0.08\n")

# Evaluate which model fits better
if(fit_bifactor["cfi.scaled"] > fit_4factor["cfi.scaled"] & 
   fit_bifactor["rmsea.scaled"] < fit_4factor["rmsea.scaled"]) {
  cat("\nBifactor model shows better fit indices\n")
} else if(fit_4factor["cfi.scaled"] > fit_bifactor["cfi.scaled"] & 
          fit_4factor["rmsea.scaled"] < fit_bifactor["rmsea.scaled"]) {
  cat("\n4-Factor model shows better fit indices\n")
} else {
  cat("\nMixed results - examine specific indices for model selection\n")
}

# Extract standardized loadings for bifactor model
if(lavInspect(cfa_bifactor, "converged")) {
  bifactor_loadings <- standardizedSolution(cfa_bifactor)
  bifactor_loadings_general <- bifactor_loadings[bifactor_loadings$lhs == "General" & 
                                                 bifactor_loadings$op == "=~", ]
  
  cat("\n--- Bifactor Model: General Factor Loadings ---\n")
  general_loadings_df <- data.frame(
    Item = bifactor_loadings_general$rhs,
    Loading = round(bifactor_loadings_general$est.std, 3)
  )
  print(general_loadings_df)
  
  # Calculate reliability coefficients if available
  cat("\nAttempting to calculate reliability coefficients...\n")
  tryCatch({
    if(packageVersion("lavaan") >= "0.6.8") {
      omega_total <- reliability(cfa_bifactor)
      cat("Reliability coefficients:\n")
      print(omega_total)
    } else {
      cat("Reliability function requires lavaan >= 0.6.8\n")
    }
  }, error = function(e) {
    cat("Could not calculate reliability coefficients:", e$message, "\n")
  })
} else {
  cat("\nWarning: Bifactor model did not converge properly\n")
  cat("Consider model identification issues or starting values\n")
}

####
## Advanced Model Testing
####

cat("\n=== ADVANCED MODEL TESTING ===\n")

####
## Heywood Case Fix for Bifactor Model
####

cat("\n--- Bifactor Model with Heywood Case Constraints ---\n")

# Check for Heywood cases in original bifactor model
if(lavInspect(cfa_bifactor, "converged")) {
  # Check for negative variances or extreme loadings
  bifactor_params <- standardizedSolution(cfa_bifactor)
  specific_loadings <- bifactor_params[grepl("S_", bifactor_params$lhs) & bifactor_params$op == "=~", ]
  
  cat("Checking for Heywood cases in bifactor model...\n")
  extreme_loadings <- specific_loadings[abs(specific_loadings$est.std) > 0.95, ]
  if(nrow(extreme_loadings) > 0) {
    cat("Items with extreme specific factor loadings (|λ| > 0.95):\n")
    print(extreme_loadings[, c("lhs", "rhs", "est.std")])
  }
  
  # Check residual variances
  residual_vars <- lavInspect(cfa_bifactor, "theta")
  negative_vars <- which(diag(residual_vars) < 0.01)
  if(length(negative_vars) > 0) {
    cat("Items with near-zero or negative residual variances:\n")
    cat(names(negative_vars), "\n")
  }
}

# Define constrained bifactor model (fix potential Heywood case for temper)
bifactor_constrained_model <- '
  # General factor (all items load on this)
  General =~ sc_worries + sc_unhappy + sc_nervous + sc_fears + sc_restless +
             sc_distracted + sc_task_completion + sc_think_act + sc_fidgeting +
             sc_helpful + sc_considerate + sc_sharing + sc_volunteer_help +
             sc_temper + sc_lying + sc_obedient
  
  # Specific factors (orthogonal to general and each other)
  S_Emotional =~ sc_worries + sc_unhappy + sc_nervous + sc_fears 
  S_Attention =~ sc_distracted + sc_task_completion + sc_think_act + sc_fidgeting + sc_restless
  S_Prosociality =~ sc_helpful + sc_considerate + sc_sharing + sc_volunteer_help
  S_Conduct =~ a*sc_temper + sc_lying + sc_obedient
  
  # Orthogonal constraints
  General ~~ 0*S_Emotional + 0*S_Attention + 0*S_Prosociality + 0*S_Conduct
  S_Emotional ~~ 0*S_Attention + 0*S_Prosociality + 0*S_Conduct
  S_Attention ~~ 0*S_Prosociality + 0*S_Conduct
  S_Prosociality ~~ 0*S_Conduct
  
  # Heywood case constraint: specific loading for temper ≤ 0.95
  a < 0.95
  
  # Alternative: constrain residual variance ≥ 0.01
  sc_temper ~~ c*sc_temper
  c > 0.01
'

# Fit constrained bifactor model
cat("Fitting constrained bifactor CFA model...\n")
cfa_bifactor_constrained <- cfa(bifactor_constrained_model, 
                               data = cfa_data, 
                               estimator = "MLR",
                               std.lv = TRUE,
                               missing = "fiml")

# Check if constrained model converged
if(lavInspect(cfa_bifactor_constrained, "converged")) {
  cat("Constrained bifactor model converged successfully\n")
  summary(cfa_bifactor_constrained, fit.measures = TRUE, standardized = TRUE)
  
  fit_bifactor_constrained <- fitMeasures(cfa_bifactor_constrained, 
                                         c("chisq.scaled", "df", "pvalue.scaled", 
                                           "cfi.scaled", "tli.scaled", "rmsea.scaled", 
                                           "srmr"))
  
  cat("\n--- Constrained Bifactor CFA Fit Summary ---\n")
  cat("χ² (scaled):", round(fit_bifactor_constrained["chisq.scaled"], 3), "\n")
  cat("CFI (scaled):", round(fit_bifactor_constrained["cfi.scaled"], 3), "\n")
  cat("RMSEA (scaled):", round(fit_bifactor_constrained["rmsea.scaled"], 3), "\n")
} else {
  cat("Constrained bifactor model did not converge\n")
  cat("Consider alternative constraint strategies\n")
}

####
## Full ESEM-Bifactor Implementation
####
p_load(esem)
cat("\n--- Full ESEM-Bifactor Model ---\n")

# Load and install esem package if needed
tryCatch({
  if (!require(esem, quietly = TRUE)) {
    cat("Installing esem package...\n")
    install.packages("esem")
    library(esem)
  }
  
  # Use esem package to create target matrix automatically
  cat("Creating ESEM target matrix using esem package...\n")
  
  # First conduct EFA to get initial factor structure
  efa_esem <- fa(cfa_data, nfactors = 4, rotate = "oblimin", fm = "pa")
  
  # Create target matrix for 4-factor ESEM using make_target function
  # Check correct syntax for make_target function
  target_esem <- tryCatch({
    # Try different syntaxes for make_target
    make_target(efa_esem$loadings)
  }, error = function(e1) {
    tryCatch({
      # Alternative syntax
      make_target(loadings = efa_esem$loadings, nfactors = 4)
    }, error = function(e2) {
      tryCatch({
        # Manual target matrix creation if esem functions fail
        loadings_matrix <- efa_esem$loadings
        target <- matrix(NA, nrow = nrow(loadings_matrix), ncol = ncol(loadings_matrix))
        for(i in 1:nrow(loadings_matrix)) {
          primary_factor <- which.max(abs(loadings_matrix[i,]))
          target[i, primary_factor] <- 0  # Free parameter
        }
        rownames(target) <- rownames(loadings_matrix)
        colnames(target) <- colnames(loadings_matrix)
        target
      }, error = function(e3) {
        cat("Could not create target matrix:", e3$message, "\n")
        NULL
      })
    })
  })
  
  if(!is.null(target_esem)) {
    cat("Target matrix created successfully\n")
    
    # Generate ESEM syntax using esem_c function or manual approach
    esem_syntax <- tryCatch({
      esem_c(target_esem)
    }, error = function(e) {
      # Manual ESEM syntax creation as fallback
      cat("Creating manual ESEM syntax as fallback...\n")
      
      # Create basic ESEM syntax manually
      item_names <- colnames(cfa_data)
      factor_syntax <- ""
      
      for(f in 1:4) {
        factor_loadings <- ""
        for(i in 1:length(item_names)) {
          if(!is.na(target_esem[i, f]) && target_esem[i, f] == 0) {
            # Primary loading - free parameter
            factor_loadings <- paste0(factor_loadings, item_names[i], " + ")
          } else {
            # Cross-loading - constrained near zero
            factor_loadings <- paste0(factor_loadings, "0.01*", item_names[i], " + ")
          }
        }
        # Remove trailing " + "
        factor_loadings <- substr(factor_loadings, 1, nchar(factor_loadings) - 3)
        factor_syntax <- paste0(factor_syntax, "F", f, " =~ ", factor_loadings, "\n")
      }
      
      # Add factor correlations
      factor_syntax <- paste0(factor_syntax, "\n# Factor correlations\n")
      for(i in 1:3) {
        for(j in (i+1):4) {
          factor_syntax <- paste0(factor_syntax, "F", i, " ~~ F", j, "\n")
        }
      }
      
      factor_syntax
    })
  } else {
    stop("Could not create target matrix for ESEM")
  }
  
  cat("ESEM syntax generated automatically\n")
  cat("Base ESEM model created with 4 factors\n")
  
  # Modify syntax to add bifactor structure
  # Add general factor loading on all items
  item_names <- colnames(cfa_data)
  general_loadings <- paste(item_names, collapse = " + ")
  
  # Create bifactor ESEM syntax by adding general factor
  esem_bifactor_syntax <- paste0(
    esem_syntax, "\n\n",
    "# General factor (bifactor)\n",
    "g =~ ", general_loadings, "\n\n",
    "# Orthogonal constraints (general orthogonal to specific factors)\n",
    "g ~~ 0*F1 + 0*F2 + 0*F3 + 0*F4\n"
  )
  
  cat("Bifactor structure added to ESEM syntax\n")
  cat("General factor specified as orthogonal to specific factors\n")
  
  # Print the generated syntax for inspection
  cat("\n--- Generated ESEM-Bifactor Syntax ---\n")
  cat(esem_bifactor_syntax, "\n")
  
  # Fit ESEM-bifactor model
  cat("\nFitting ESEM-bifactor model with orthogonal constraints...\n")
  
  # For bifactor models with explicit orthogonal constraints, we don't need rotation parameter
  # The constraints are already specified in the syntax
  lavaan_version <- packageVersion("lavaan")
  cat("lavaan version", as.character(lavaan_version), "\n")
  
  esem_bifactor_model <- cfa(esem_bifactor_syntax,
                            data = cfa_data,
                            estimator = "MLR",
                            std.lv = TRUE,
                            missing = "fiml")
  
  # Check convergence and summarize
  if(lavInspect(esem_bifactor_model, "converged")) {
    cat("ESEM-bifactor model converged successfully\n")
    
    # Extract fit indices
    fit_esem_bifactor <- fitMeasures(esem_bifactor_model, 
                                    c("chisq.scaled", "df", "pvalue.scaled", 
                                      "cfi.scaled", "tli.scaled", "rmsea.scaled", 
                                      "rmsea.ci.lower.scaled", "rmsea.ci.upper.scaled",
                                      "srmr"))
    
    cat("\n--- ESEM-Bifactor Model Fit ---\n")
    cat("χ² (scaled):", round(fit_esem_bifactor["chisq.scaled"], 3), "\n")
    cat("df:", fit_esem_bifactor["df"], "\n")
    cat("p-value:", round(fit_esem_bifactor["pvalue.scaled"], 3), "\n")
    cat("CFI (scaled):", round(fit_esem_bifactor["cfi.scaled"], 3), "\n")
    cat("TLI (scaled):", round(fit_esem_bifactor["tli.scaled"], 3), "\n")
    cat("RMSEA (scaled):", round(fit_esem_bifactor["rmsea.scaled"], 3), 
        " [", round(fit_esem_bifactor["rmsea.ci.lower.scaled"], 3), ", ", 
        round(fit_esem_bifactor["rmsea.ci.upper.scaled"], 3), "]\n")
    cat("SRMR:", round(fit_esem_bifactor["srmr"], 3), "\n")
    
    # Extract and display loadings
    esem_loadings <- standardizedSolution(esem_bifactor_model)
    
    # General factor loadings
    general_loadings_esem <- esem_loadings[esem_loadings$lhs == "g" & 
                                          esem_loadings$op == "=~", ]
    if(nrow(general_loadings_esem) > 0) {
      cat("\n--- ESEM-Bifactor: General Factor Loadings ---\n")
      general_df_esem <- data.frame(
        Item = general_loadings_esem$rhs,
        Loading = round(general_loadings_esem$est.std, 3),
        SE = round(general_loadings_esem$se, 3),
        pvalue = round(general_loadings_esem$pvalue, 3)
      )
      print(general_df_esem)
    }
    
    # Cross-loadings summary
    specific_loadings_esem <- esem_loadings[grepl("F[1-4]", esem_loadings$lhs) & 
                                           esem_loadings$op == "=~", ]
    if(nrow(specific_loadings_esem) > 0) {
      cat("\n--- ESEM: Cross-Loadings Summary ---\n")
      # Show items with meaningful cross-loadings (|λ| > 0.20)
      meaningful_cross <- specific_loadings_esem[abs(specific_loadings_esem$est.std) > 0.20, ]
      if(nrow(meaningful_cross) > 0) {
        cross_summary <- data.frame(
          Item = meaningful_cross$rhs,
          Factor = meaningful_cross$lhs,
          Loading = round(meaningful_cross$est.std, 3),
          pvalue = round(meaningful_cross$pvalue, 3)
        )
        print(cross_summary)
        cat("Note: Cross-loadings |λ| > 0.20 shown\n")
      } else {
        cat("No substantial cross-loadings (|λ| > 0.20) detected\n")
      }
    }
    
    # Model comparison with previous models
    cat("\n--- ESEM-Bifactor vs Standard Bifactor Comparison ---\n")
    
    # Calculate fit differences (ΔCFI, ΔRMSEA)
    delta_cfi <- fit_esem_bifactor["cfi.scaled"] - fit_bifactor["cfi.scaled"]
    delta_rmsea <- fit_esem_bifactor["rmsea.scaled"] - fit_bifactor["rmsea.scaled"]
    
    cat("ΔCFI (ESEM-Bifactor vs Standard Bifactor):", round(delta_cfi, 3), "\n")
    cat("ΔRMSEA (ESEM-Bifactor vs Standard Bifactor):", round(delta_rmsea, 3), "\n")
    
    # Interpretation of differences
    if(delta_cfi >= 0.02) {
      cat("Substantial CFI improvement (ΔCFI ≥ 0.02) suggests meaningful cross-loadings\n")
    } else if(delta_cfi >= 0.01) {
      cat("Moderate CFI improvement (ΔCFI ≥ 0.01) suggests some cross-loadings\n")
    } else {
      cat("Minimal CFI improvement (ΔCFI < 0.01) - standard CFA may be adequate\n")
    }
    
    if(delta_rmsea <= -0.015) {
      cat("Substantial RMSEA improvement (ΔRMSEA ≤ -0.015) supports ESEM approach\n")
    } else if(delta_rmsea <= -0.008) {
      cat("Moderate RMSEA improvement (ΔRMSEA ≤ -0.008) supports ESEM approach\n")
    } else {
      cat("Minimal RMSEA improvement - consider simpler model\n")
    }
    
    # Satorra-Bentler chi-square difference test
    cat("\n--- Satorra-Bentler Chi-Square Difference Test ---\n")
    tryCatch({
      sb_test <- anova(cfa_bifactor, esem_bifactor_model)
      print(sb_test)
      
      if(sb_test$`Pr(>Chisq)`[2] < 0.05) {
        cat("Significant improvement with ESEM-bifactor (p < 0.05)\n")
      } else {
        cat("Non-significant improvement with ESEM-bifactor (p ≥ 0.05)\n")
      }
    }, error = function(e) {
      cat("Could not perform chi-square difference test:", e$message, "\n")
    })
    
  } else {
    cat("ESEM-bifactor model did not converge\n")
    cat("Consider model identification or starting value issues\n")
  }
  
}, error = function(e) {
  cat("Error in ESEM-bifactor analysis:", e$message, "\n")
  cat("Possible issues:\n")
  cat("1. esem package not available - install with: install.packages('esem')\n")
  cat("2. Model identification problems\n")
  cat("3. Sample size insufficient for complex model\n")
  cat("Consider using Mplus for full ESEM-bifactor capabilities\n")
})

summary(cfa_bifactor_constrained)

####
## Second-Order CFA Model
####

cat("\n--- Second-Order CFA Model ---\n")

# Define second-order (hierarchical) CFA model
secondorder_model <- '
  # First-order factors
  Emotional =~ sc_worries + sc_unhappy + sc_nervous + sc_fears 
  Attention =~ sc_distracted + sc_task_completion + sc_think_act + sc_fidgeting + sc_restless
  Prosociality =~ sc_helpful + sc_considerate + sc_sharing + sc_volunteer_help
  Conduct =~ sc_temper + sc_lying + sc_obedient
  
  # Second-order factor (Self-Control loads on all first-order factors)
  SelfControl =~ Emotional + Attention + Prosociality + Conduct
'

# Fit second-order CFA model
cat("Fitting second-order CFA model...\n")
cfa_secondorder <- cfa(secondorder_model, 
                      data = cfa_data, 
                      estimator = "MLR",
                      std.lv = TRUE,
                      missing = "fiml")

# Model summary
cat("\nSecond-Order CFA Results:\n")
summary(cfa_secondorder, fit.measures = TRUE, standardized = TRUE, rsquare = TRUE)

# Extract fit indices
fit_secondorder <- fitMeasures(cfa_secondorder, c("chisq.scaled", "df", "pvalue.scaled", 
                                                  "cfi.scaled", "tli.scaled", "rmsea.scaled", 
                                                  "rmsea.ci.lower.scaled", "rmsea.ci.upper.scaled",
                                                  "srmr"))

cat("\n--- Second-Order CFA Fit Summary ---\n")
cat("χ² (scaled):", round(fit_secondorder["chisq.scaled"], 3), "\n")
cat("df:", fit_secondorder["df"], "\n")
cat("p-value:", round(fit_secondorder["pvalue.scaled"], 3), "\n")
cat("CFI (scaled):", round(fit_secondorder["cfi.scaled"], 3), "\n")
cat("TLI (scaled):", round(fit_secondorder["tli.scaled"], 3), "\n")
cat("RMSEA (scaled):", round(fit_secondorder["rmsea.scaled"], 3), 
    " [", round(fit_secondorder["rmsea.ci.lower.scaled"], 3), ", ", 
    round(fit_secondorder["rmsea.ci.upper.scaled"], 3), "]\n")
cat("SRMR:", round(fit_secondorder["srmr"], 3), "\n")

# Extract second-order loadings
if(lavInspect(cfa_secondorder, "converged")) {
  secondorder_loadings <- standardizedSolution(cfa_secondorder)
  higher_order_loadings <- secondorder_loadings[secondorder_loadings$lhs == "SelfControl" & 
                                                secondorder_loadings$op == "=~", ]
  
  cat("\n--- Second-Order Factor Loadings ---\n")
  secondorder_df <- data.frame(
    Factor = higher_order_loadings$rhs,
    Loading = round(higher_order_loadings$est.std, 3),
    SE = round(higher_order_loadings$se, 3),
    pvalue = round(higher_order_loadings$pvalue, 3)
  )
  print(secondorder_df)
}

####
## ESEM Bifactor Model
####

cat("\n--- ESEM Bifactor Model ---\n")

# Load required packages for ESEM
tryCatch({
  # Check if we can do ESEM in lavaan (requires target rotation)
  cat("Attempting ESEM bifactor analysis...\n")
  
  # First, conduct EFA to get target matrix for ESEM
  efa_for_esem <- fa(cfa_data, nfactors = 4, rotate = "oblimin", fm = "ml")
  
  # Create target matrix (0 = free, NA = fixed to 0)
  target_matrix <- matrix(NA, nrow = ncol(cfa_data), ncol = 4)
  rownames(target_matrix) <- colnames(cfa_data)
  colnames(target_matrix) <- paste0("F", 1:4)
  
  # Set primary loadings as free (0), others as near-zero
  loadings_matrix <- efa_for_esem$loadings
  for(i in 1:nrow(loadings_matrix)) {
    primary_factor <- which.max(abs(loadings_matrix[i,]))
    target_matrix[i, primary_factor] <- 0  # Free parameter
    # Allow small cross-loadings for conduct items on attention factor
    item_name <- rownames(loadings_matrix)[i]
    if(item_name %in% c("sc_temper", "sc_lying") && primary_factor != 2) {
      target_matrix[i, 2] <- 0  # Allow cross-loading on attention factor
    }
  }
  
  cat("Target matrix for ESEM created\n")
  cat("Primary loadings set as free parameters\n")
  cat("Cross-loadings allowed for conduct items on attention factor\n")
  
  # Note: Full ESEM bifactor requires specialized software (Mplus) or advanced lavaan syntax
  cat("\nNote: Full ESEM bifactor analysis requires Mplus or advanced lavaan syntax\n")
  cat("Consider using Mplus or specialized ESEM packages for complete analysis\n")
  
  # Alternative: Relaxed CFA allowing specific cross-loadings
  cat("\n--- Relaxed CFA with Cross-Loadings ---\n")
  
  relaxed_cfa_model <- '
    # Primary factor loadings
    Emotional =~ sc_worries + sc_unhappy + sc_nervous + sc_fears 
    Attention =~ sc_distracted + sc_task_completion + sc_think_act + sc_fidgeting + sc_restless
    Prosociality =~ sc_helpful + sc_considerate + sc_sharing + sc_volunteer_help
    Conduct =~ sc_temper + sc_lying + sc_obedient
    
    # Cross-loadings (conduct items on attention factor)
    Attention =~ sc_temper + sc_lying
    
    # Factor correlations
    Emotional ~~ Attention + Prosociality + Conduct
    Attention ~~ Prosociality + Conduct
    Prosociality ~~ Conduct
  '
  
  cfa_relaxed <- cfa(relaxed_cfa_model, 
                    data = cfa_data, 
                    estimator = "MLR",
                    std.lv = TRUE,
                    missing = "fiml")
  
  if(lavInspect(cfa_relaxed, "converged")) {
    cat("Relaxed CFA with cross-loadings converged\n")
    
    fit_relaxed <- fitMeasures(cfa_relaxed, c("chisq.scaled", "df", "cfi.scaled", 
                                             "tli.scaled", "rmsea.scaled", "srmr"))
    
    cat("Relaxed CFA Fit:\n")
    cat("CFI (scaled):", round(fit_relaxed["cfi.scaled"], 3), "\n")
    cat("RMSEA (scaled):", round(fit_relaxed["rmsea.scaled"], 3), "\n")
    cat("SRMR:", round(fit_relaxed["srmr"], 3), "\n")
    
    # Extract cross-loadings
    relaxed_loadings <- standardizedSolution(cfa_relaxed)
    cross_loadings <- relaxed_loadings[relaxed_loadings$lhs == "Attention" & 
                                      relaxed_loadings$rhs %in% c("sc_temper", "sc_lying"), ]
    if(nrow(cross_loadings) > 0) {
      cat("\nCross-loadings (Conduct items on Attention):\n")
      print(cross_loadings[, c("rhs", "est.std", "pvalue")])
    }
  }
  
}, error = function(e) {
  cat("Error in ESEM analysis:", e$message, "\n")
  cat("Consider using specialized ESEM software or packages\n")
})

####
## Model Comparison Summary
####

cat("\n=== COMPREHENSIVE MODEL COMPARISON ===\n")

# Create comprehensive comparison table
model_names <- c("4-Factor CFA", "Bifactor CFA", "Second-Order CFA")
model_fits <- list(fit_4factor, fit_bifactor, fit_secondorder)

# Add constrained bifactor if it converged
if(exists("fit_bifactor_constrained") && lavInspect(cfa_bifactor_constrained, "converged")) {
  model_names <- c(model_names, "Bifactor Constrained")
  model_fits <- c(model_fits, list(fit_bifactor_constrained))
}

# Add relaxed CFA if it converged
if(exists("fit_relaxed") && lavInspect(cfa_relaxed, "converged")) {
  model_names <- c(model_names, "Relaxed CFA")
  model_fits <- c(model_fits, list(fit_relaxed))
}

# Add ESEM-bifactor if it converged
if(exists("fit_esem_bifactor") && exists("esem_bifactor_model") && lavInspect(esem_bifactor_model, "converged")) {
  model_names <- c(model_names, "ESEM-Bifactor")
  model_fits <- c(model_fits, list(fit_esem_bifactor))
}

# Create comparison table
comprehensive_comparison <- data.frame(
  Model = model_names,
  ChiSq = sapply(model_fits, function(x) round(x["chisq.scaled"], 3)),
  df = sapply(model_fits, function(x) x["df"]),
  CFI = sapply(model_fits, function(x) round(x["cfi.scaled"], 3)),
  TLI = sapply(model_fits, function(x) round(x["tli.scaled"], 3)),
  RMSEA = sapply(model_fits, function(x) round(x["rmsea.scaled"], 3)),
  SRMR = sapply(model_fits, function(x) round(x["srmr"], 3))
)

cat("\nComprehensive Model Comparison:\n")
print(comprehensive_comparison)

# Best fitting model
best_cfi <- which.max(comprehensive_comparison$CFI)
best_rmsea <- which.min(comprehensive_comparison$RMSEA)
best_srmr <- which.min(comprehensive_comparison$SRMR)

cat("\n--- Best Fitting Models ---\n")
cat("Highest CFI:", model_names[best_cfi], "(CFI =", comprehensive_comparison$CFI[best_cfi], ")\n")
cat("Lowest RMSEA:", model_names[best_rmsea], "(RMSEA =", comprehensive_comparison$RMSEA[best_rmsea], ")\n")
cat("Lowest SRMR:", model_names[best_srmr], "(SRMR =", comprehensive_comparison$SRMR[best_srmr], ")\n")

# Model selection recommendation
if(best_cfi == best_rmsea && best_cfi == best_srmr) {
  cat("\nRecommendation: Use", model_names[best_cfi], "- consistently best fit across indices\n")
} else {
  cat("\nRecommendation: Compare top 2-3 models considering theoretical interpretability\n")
}

