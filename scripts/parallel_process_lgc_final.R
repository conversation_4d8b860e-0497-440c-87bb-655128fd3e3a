# =============================================================================
# FINAL PARALLEL-PROCESS LATENT GROWTH CURVE MODEL
# Self-Control + Fight with Others (Ages 11, 14, 17)
# Optimized for Reliability and Efficiency
# =============================================================================

library(pacman)
p_load(lavaan, semTools, dplyr, psych)

cat("=== FINAL PARALLEL-PROCESS LATENT GROWTH CURVE MODEL ===\n")

# =============================================================================
# 1. DATA LOADING AND SETUP
# =============================================================================

data_path <- "/home/<USER>/dissertation_folder/data"
merged_data <- readRDS(file.path(data_path, "merged1203.rds"))

# Ensure self-control variables exist
if(!all(c("sc11_think_act", "sc11_considerate") %in% names(merged_data))) {
  source("scripts/recode_self_control.R")
  source("scripts/recode_self_control_age14.R") 
  source("scripts/recode_self_control_age17.R")
}

# Recode fight variables
merged_data$agr5_fig <- ifelse(merged_data$epsdfb00 %in% c(-1, 4), NA,
                               ifelse(merged_data$epsdfb00 == 1, 0,
                                      ifelse(merged_data$epsdfb00 == 2, 1,
                                             ifelse(merged_data$epsdfb00 == 3, 2, NA))))

merged_data$agr6_fig <- ifelse(merged_data$fpsdfb00 %in% c(-1, -9), NA,
                               ifelse(merged_data$fpsdfb00 == 1, 0,
                                      ifelse(merged_data$fpsdfb00 == 2, 1,
                                             ifelse(merged_data$fpsdfb00 == 3, 2, NA))))

merged_data$agr7_fig <- ifelse(merged_data$gpsdfb00 %in% c(-1, 4), NA,
                               ifelse(merged_data$gpsdfb00 == 1, 0,
                                      ifelse(merged_data$gpsdfb00 == 2, 1,
                                             ifelse(merged_data$gpsdfb00 == 3, 2, NA))))

# =============================================================================
# 2. CREATE SELF-CONTROL COMPOSITE SCORES (Simple Approach)
# =============================================================================

cat("Creating self-control composite scores...\n")

# Core items for three-factor structure
exec_items <- c("task_completion", "distracted", "fidgeting", "think_act", "restless")
prosocial_items <- c("considerate", "sharing", "helpful", "volunteer_help")
temper_items <- c("temper", "obedient", "lying")

# Function to create composite scores
create_composite <- function(age, items) {
  vars <- paste0("sc", age, "_", items)
  available_vars <- vars[vars %in% names(merged_data)]
  
  if(length(available_vars) >= 3) {
    # Calculate mean score (at least 3 items needed)
    scores <- rowMeans(merged_data[, available_vars, drop = FALSE], na.rm = TRUE)
    scores[rowSums(!is.na(merged_data[, available_vars, drop = FALSE])) < 3] <- NA
    return(scores)
  } else {
    return(rep(NA, nrow(merged_data)))
  }
}

# Create composite scores for each domain and age
for(age in c(11, 14, 17)) {
  merged_data[[paste0("sc_exec_", age)]] <- create_composite(age, exec_items)
  merged_data[[paste0("sc_prosocial_", age)]] <- create_composite(age, prosocial_items)
  merged_data[[paste0("sc_temper_", age)]] <- create_composite(age, temper_items)
  
  # Overall self-control composite
  exec_score <- merged_data[[paste0("sc_exec_", age)]]
  prosocial_score <- merged_data[[paste0("sc_prosocial_", age)]]
  temper_score <- merged_data[[paste0("sc_temper_", age)]]
  
  # Average across domains (need at least 2 domains)
  domain_scores <- cbind(exec_score, prosocial_score, temper_score)
  merged_data[[paste0("sc_total_", age)]] <- rowMeans(domain_scores, na.rm = TRUE)
  merged_data[[paste0("sc_total_", age)]][rowSums(!is.na(domain_scores)) < 2] <- NA
}

cat("Self-control composites created.\n")

# =============================================================================
# 3. DESCRIPTIVE STATISTICS
# =============================================================================

cat("\n=== DESCRIPTIVE STATISTICS ===\n")

analysis_vars <- c("sc_total_11", "sc_total_14", "sc_total_17",
                   "agr5_fig", "agr6_fig", "agr7_fig")

for(var in analysis_vars) {
  if(var %in% names(merged_data)) {
    valid_n <- sum(!is.na(merged_data[[var]]))
    mean_val <- mean(merged_data[[var]], na.rm = TRUE)
    sd_val <- sd(merged_data[[var]], na.rm = TRUE)
    cat(var, ": N =", valid_n, ", M =", round(mean_val, 3), ", SD =", round(sd_val, 3), "\n")
  }
}

# Create analysis dataset
analysis_data <- merged_data[, analysis_vars, drop = FALSE]
analysis_data$id <- 1:nrow(analysis_data)

# =============================================================================
# 4. PARALLEL-PROCESS LATENT GROWTH CURVE MODEL
# =============================================================================

cat("\n=== FITTING PARALLEL-PROCESS LGC MODEL ===\n")

parallel_syntax <- '
# Self-control growth factors
i_sc =~ 1*sc_total_11 + 1*sc_total_14 + 1*sc_total_17
s_sc =~ 0*sc_total_11 + 1*sc_total_14 + 2*sc_total_17

# Fight behavior growth factors
i_fight =~ 1*agr5_fig + 1*agr6_fig + 1*agr7_fig
s_fight =~ 0*agr5_fig + 1*agr6_fig + 2*agr7_fig

# Growth means
i_sc ~ 1
s_sc ~ 1
i_fight ~ 1
s_fight ~ 1

# Parallel-process correlations
i_sc ~~ i_fight       # Initial levels correlation
s_sc ~~ s_fight       # Slope correlation
i_sc ~~ s_fight       # Cross-domain: initial SC with fight change
s_sc ~~ i_fight       # Cross-domain: SC change with initial fight
'

# Fit the model
fit_parallel <- tryCatch({
  growth(parallel_syntax, data = analysis_data, missing = "ML", estimator = "MLR")
}, error = function(e) {
  cat("Error:", e$message, "\n")
  return(NULL)
})

# =============================================================================
# 5. RESULTS
# =============================================================================

if(!is.null(fit_parallel) && lavInspect(fit_parallel, "converged")) {
  cat("✅ MODEL CONVERGED SUCCESSFULLY\n\n")
  
  # Summary
  cat("=== MODEL SUMMARY ===\n")
  summary(fit_parallel, fit.measures = TRUE, standardized = TRUE)
  
  # Extract key parameters
  params <- parameterEstimates(fit_parallel, standardized = TRUE)
  
  cat("\n=== KEY FINDINGS ===\n")
  
  # Growth means
  i_sc_mean <- params[params$lhs == "i_sc" & params$op == "~1", ]
  s_sc_mean <- params[params$lhs == "s_sc" & params$op == "~1", ]
  i_fight_mean <- params[params$lhs == "i_fight" & params$op == "~1", ]
  s_fight_mean <- params[params$lhs == "s_fight" & params$op == "~1", ]
  
  cat("1. DEVELOPMENTAL TRAJECTORIES:\n")
  if(nrow(i_sc_mean) > 0) {
    cat("   Self-control intercept =", round(i_sc_mean$est, 3), 
        "(p =", round(i_sc_mean$pvalue, 3), ")\n")
  }
  if(nrow(s_sc_mean) > 0) {
    cat("   Self-control slope =", round(s_sc_mean$est, 3), 
        "(p =", round(s_sc_mean$pvalue, 3), ")")
    if(!is.na(s_sc_mean$pvalue) && s_sc_mean$pvalue < 0.05) {
      if(s_sc_mean$est > 0) cat(" [WORSENING]") else cat(" [IMPROVING]")
    }
    cat("\n")
  }
  if(nrow(i_fight_mean) > 0) {
    cat("   Fighting intercept =", round(i_fight_mean$est, 3), 
        "(p =", round(i_fight_mean$pvalue, 3), ")\n")
  }
  if(nrow(s_fight_mean) > 0) {
    cat("   Fighting slope =", round(s_fight_mean$est, 3), 
        "(p =", round(s_fight_mean$pvalue, 3), ")")
    if(!is.na(s_fight_mean$pvalue) && s_fight_mean$pvalue < 0.05) {
      if(s_fight_mean$est > 0) cat(" [INCREASING]") else cat(" [DECREASING]")
    }
    cat("\n")
  }
  
  # Correlations
  intercept_cor <- params[params$lhs == "i_sc" & params$op == "~~" & params$rhs == "i_fight", ]
  slope_cor <- params[params$lhs == "s_sc" & params$op == "~~" & params$rhs == "s_fight", ]
  cross_cor1 <- params[params$lhs == "i_sc" & params$op == "~~" & params$rhs == "s_fight", ]
  cross_cor2 <- params[params$lhs == "s_sc" & params$op == "~~" & params$rhs == "i_fight", ]
  
  cat("\n2. PARALLEL-PROCESS ASSOCIATIONS:\n")
  if(nrow(intercept_cor) > 0) {
    cat("   Initial levels correlation =", round(intercept_cor$est, 3), 
        "(p =", round(intercept_cor$pvalue, 3), ")")
    if(!is.na(intercept_cor$pvalue) && intercept_cor$pvalue < 0.05) {
      cat(" [SIGNIFICANT]")
    }
    cat("\n")
  }
  
  if(nrow(slope_cor) > 0) {
    cat("   Change correlation =", round(slope_cor$est, 3), 
        "(p =", round(slope_cor$pvalue, 3), ")")
    if(!is.na(slope_cor$pvalue) && slope_cor$pvalue < 0.05) {
      cat(" [SIGNIFICANT COUPLING]")
    }
    cat("\n")
  }
  
  if(nrow(cross_cor1) > 0) {
    cat("   Initial SC → Fighting change =", round(cross_cor1$est, 3), 
        "(p =", round(cross_cor1$pvalue, 3), ")")
    if(!is.na(cross_cor1$pvalue) && cross_cor1$pvalue < 0.05) {
      cat(" [PREDICTIVE]")
    }
    cat("\n")
  }
  
  if(nrow(cross_cor2) > 0) {
    cat("   Initial Fighting → SC change =", round(cross_cor2$est, 3), 
        "(p =", round(cross_cor2$pvalue, 3), ")")
    if(!is.na(cross_cor2$pvalue) && cross_cor2$pvalue < 0.05) {
      cat(" [PREDICTIVE]")
    }
    cat("\n")
  }
  
  # Model fit
  fit_indices <- fitMeasures(fit_parallel, c("chisq", "df", "pvalue", "cfi", "tli", "rmsea", "srmr"))
  cat("\n3. MODEL FIT:\n")
  cat("   χ² =", round(fit_indices["chisq"], 2), ", df =", fit_indices["df"], 
      ", p =", round(fit_indices["pvalue"], 3), "\n")
  cat("   CFI =", round(fit_indices["cfi"], 3), 
      ", TLI =", round(fit_indices["tli"], 3), "\n")
  cat("   RMSEA =", round(fit_indices["rmsea"], 3), 
      ", SRMR =", round(fit_indices["srmr"], 3), "\n")
  
  # Interpretation
  cat("\n=== INTERPRETATION ===\n")
  cat("This model tests developmental coupling between self-control and fighting.\n")
  cat("Significant correlations suggest:\n")
  cat("- Shared developmental processes\n")
  cat("- Cross-domain influences\n") 
  cat("- Need for integrated interventions\n")
  
} else {
  cat("❌ MODEL FAILED TO CONVERGE\n")
  cat("Check data quality and model specification.\n")
}

cat("\n=== ANALYSIS COMPLETE ===\n") 