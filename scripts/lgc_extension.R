# =============================================================================
# LATENT GROWTH CURVE EXTENSION
# Add LGC modeling to existing three-factor measurement invariance analysis
# =============================================================================

cat("\n=== EXTENDING TO LATENT GROWTH CURVE MODELING ===\n")

# This script assumes you have already run measurement_invariance_3factor.R
# and have the wide_data and factor structure variables in memory

# Check if required objects exist
if(!exists("wide_data") || !exists("executive_items") || !exists("selfcent_items") || !exists("temper_items")) {
  cat("Running measurement invariance script first...\n")
  source("scripts/measurement_invariance_3factor.R")
}

cat("Proceeding with second-order LGC analysis...\n")
cat("Sample size:", nrow(wide_data), "\n")

# =============================================================================
# SECOND-ORDER LGC MODEL: EXECUTIVE CONTROL ONLY (SIMPLIFIED)
# =============================================================================

cat("\n--- EXECUTIVE CONTROL GROWTH MODEL ---\n")

# Create Executive LGC model syntax
exec_lgc_syntax <- paste0(
  "# First level: Executive factors at each wave\n",
  "Executive11 =~ ", paste(paste0("sc11_", executive_items), collapse = " + "), "\n",
  "Executive14 =~ ", paste(paste0("sc14_", executive_items), collapse = " + "), "\n",
  "Executive17 =~ ", paste(paste0("sc17_", executive_items), collapse = " + "), "\n",
  "\n",
  "# Second level: Growth factors\n",
  "iExecutive =~ 1*Executive11 + 1*Executive14 + 1*Executive17\n",
  "sExecutive =~ 0*Executive11 + 3*Executive14 + 6*Executive17\n",
  "\n",
  "# Identification constraints\n",
  "# Fix first indicator intercepts per wave\n",
  "sc11_task_completion ~ c1*1\n",
  "sc14_task_completion ~ c1*1\n",
  "sc17_task_completion ~ c1*1\n"
)

cat("Executive LGC Model:\n")
cat(exec_lgc_syntax, "\n")

# Fit Executive LGC model
cat("Fitting Executive Control growth model...\n")
fit_exec_lgc <- tryCatch({
  sem(
    model = exec_lgc_syntax,
    data = wide_data,
    estimator = "WLSMV",
    ordered = paste0("sc", rep(c(11, 14, 17), each = length(executive_items)), "_", rep(executive_items, 3)),
    missing = "pairwise"
  )
}, error = function(e) {
  cat("Error fitting Executive LGC:", e$message, "\n")
  return(NULL)
})

# Evaluate Executive LGC results
if(!is.null(fit_exec_lgc) && lavInspect(fit_exec_lgc, "converged")) {
  cat("✓ Executive LGC model converged\n")
  
  # Fit measures
  fit_exec <- fitMeasures(fit_exec_lgc, c("cfi", "tli", "rmsea", "srmr"))
  cat("Executive LGC Fit:\n")
  cat("  CFI =", round(fit_exec["cfi"], 3), "\n")
  cat("  TLI =", round(fit_exec["tli"], 3), "\n")
  cat("  RMSEA =", round(fit_exec["rmsea"], 3), "\n")
  cat("  SRMR =", round(fit_exec["srmr"], 3), "\n")
  
  # Growth parameters
  exec_params <- parameterEstimates(fit_exec_lgc)
  
  # Intercept and slope
  exec_intercept <- exec_params[exec_params$lhs == "iExecutive" & exec_params$op == "~1", ]
  exec_slope <- exec_params[exec_params$lhs == "sExecutive" & exec_params$op == "~1", ]
  
  cat("\nExecutive Control Development:\n")
  if(nrow(exec_intercept) > 0) {
    cat("  Intercept =", round(exec_intercept$est, 3), "(p =", round(exec_intercept$pvalue, 3), ")\n")
  }
  if(nrow(exec_slope) > 0) {
    cat("  Slope =", round(exec_slope$est, 3), "(p =", round(exec_slope$pvalue, 3), ")\n")
    if(!is.na(exec_slope$pvalue) && exec_slope$pvalue < 0.05) {
      if(exec_slope$est > 0) {
        cat("  → Executive control INCREASES significantly over time\n")
      } else {
        cat("  → Executive control DECREASES significantly over time\n")
      }
    } else {
      cat("  → No significant change in executive control\n")
    }
  }
  
} else {
  cat("❌ Executive LGC model failed\n")
}

# =============================================================================
# SECOND-ORDER LGC MODEL: TEMPER CONTROL
# =============================================================================

cat("\n--- TEMPER CONTROL GROWTH MODEL ---\n")

# Create Temper LGC model syntax
temper_lgc_syntax <- paste0(
  "# First level: Temper factors at each wave\n",
  "Temper11 =~ ", paste(paste0("sc11_", temper_items), collapse = " + "), "\n",
  "Temper14 =~ ", paste(paste0("sc14_", temper_items), collapse = " + "), "\n",
  "Temper17 =~ ", paste(paste0("sc17_", temper_items), collapse = " + "), "\n",
  "\n",
  "# Second level: Growth factors\n",
  "iTemper =~ 1*Temper11 + 1*Temper14 + 1*Temper17\n",
  "sTemper =~ 0*Temper11 + 3*Temper14 + 6*Temper17\n",
  "\n",
  "# Identification constraints\n",
  "# Fix first indicator intercepts per wave\n",
  "sc11_temper ~ c2*1\n",
  "sc14_temper ~ c2*1\n",
  "sc17_temper ~ c2*1\n"
)

# Fit Temper LGC model
cat("Fitting Temper Control growth model...\n")
fit_temper_lgc <- tryCatch({
  sem(
    model = temper_lgc_syntax,
    data = wide_data,
    estimator = "WLSMV",
    ordered = paste0("sc", rep(c(11, 14, 17), each = length(temper_items)), "_", rep(temper_items, 3)),
    missing = "pairwise"
  )
}, error = function(e) {
  cat("Error fitting Temper LGC:", e$message, "\n")
  return(NULL)
})

# Evaluate Temper LGC results
if(!is.null(fit_temper_lgc) && lavInspect(fit_temper_lgc, "converged")) {
  cat("✓ Temper LGC model converged\n")
  
  # Growth parameters
  temper_params <- parameterEstimates(fit_temper_lgc)
  
  # Intercept and slope
  temper_intercept <- temper_params[temper_params$lhs == "iTemper" & temper_params$op == "~1", ]
  temper_slope <- temper_params[temper_params$lhs == "sTemper" & temper_params$op == "~1", ]
  
  cat("\nTemper Control Development:\n")
  if(nrow(temper_intercept) > 0) {
    cat("  Intercept =", round(temper_intercept$est, 3), "(p =", round(temper_intercept$pvalue, 3), ")\n")
  }
  if(nrow(temper_slope) > 0) {
    cat("  Slope =", round(temper_slope$est, 3), "(p =", round(temper_slope$pvalue, 3), ")\n")
    if(!is.na(temper_slope$pvalue) && temper_slope$pvalue < 0.05) {
      if(temper_slope$est < 0) {
        cat("  → Temper problems DECREASE significantly over time (improved control)\n")
      } else {
        cat("  → Temper problems INCREASE significantly over time (worse control)\n")
      }
    } else {
      cat("  → No significant change in temper control\n")
    }
  }
  
} else {
  cat("❌ Temper LGC model failed\n")
}

# =============================================================================
# SECOND-ORDER LGC MODEL: SELF-CENTERED PROSOCIALITY (LATENT BASIS)
# =============================================================================

cat("\n--- SELF-CENTERED PROSOCIALITY GROWTH MODEL ---\n")

# Create SelfCent LGC model with latent basis
selfcent_lgc_syntax <- paste0(
  "# First level: SelfCent factors at each wave\n",
  "SelfCent11 =~ ", paste(paste0("sc11_", selfcent_items), collapse = " + "), "\n",
  "SelfCent14 =~ ", paste(paste0("sc14_", selfcent_items), collapse = " + "), "\n",
  "SelfCent17 =~ ", paste(paste0("sc17_", selfcent_items), collapse = " + "), "\n",
  "\n",
  "# Second level: Growth factors (latent basis model)\n",
  "iSelfCent =~ 1*SelfCent11 + 1*SelfCent14 + 1*SelfCent17\n",
  "sSelfCent =~ 0*SelfCent11 + 1*SelfCent14 + lambda*SelfCent17\n",
  "\n",
  "# Identification constraints\n",
  "# Fix first indicator intercepts per wave\n",
  "sc11_considerate ~ c3*1\n",
  "sc14_considerate ~ c3*1\n",
  "sc17_considerate ~ c3*1\n"
)

# Fit SelfCent LGC model
cat("Fitting Self-Centered Prosociality growth model (latent basis)...\n")
fit_selfcent_lgc <- tryCatch({
  sem(
    model = selfcent_lgc_syntax,
    data = wide_data,
    estimator = "WLSMV",
    ordered = paste0("sc", rep(c(11, 14, 17), each = length(selfcent_items)), "_", rep(selfcent_items, 3)),
    missing = "pairwise"
  )
}, error = function(e) {
  cat("Error fitting SelfCent LGC:", e$message, "\n")
  return(NULL)
})

# Evaluate SelfCent LGC results
if(!is.null(fit_selfcent_lgc) && lavInspect(fit_selfcent_lgc, "converged")) {
  cat("✓ Self-Centered Prosociality LGC model converged\n")
  
  # Growth parameters
  selfcent_params <- parameterEstimates(fit_selfcent_lgc)
  
  # Lambda parameter (key test of rise-then-plateau)
  lambda_param <- selfcent_params[selfcent_params$label == "lambda", ]
  
  # Intercept and slope
  selfcent_intercept <- selfcent_params[selfcent_params$lhs == "iSelfCent" & selfcent_params$op == "~1", ]
  selfcent_slope <- selfcent_params[selfcent_params$lhs == "sSelfCent" & selfcent_params$op == "~1", ]
  
  cat("\nSelf-Centered Prosociality Development:\n")
  if(nrow(selfcent_intercept) > 0) {
    cat("  Intercept =", round(selfcent_intercept$est, 3), "(p =", round(selfcent_intercept$pvalue, 3), ")\n")
  }
  if(nrow(selfcent_slope) > 0) {
    cat("  Slope =", round(selfcent_slope$est, 3), "(p =", round(selfcent_slope$pvalue, 3), ")\n")
  }
  if(nrow(lambda_param) > 0) {
    cat("  Lambda (Age 17 loading) =", round(lambda_param$est, 3), "(p =", round(lambda_param$pvalue, 3), ")\n")
    if(lambda_param$est < 1) {
      cat("  → Pattern: Rise then PLATEAU (λ < 1)\n")
    } else if(lambda_param$est > 1) {
      cat("  → Pattern: Accelerating INCREASE (λ > 1)\n")
    } else {
      cat("  → Pattern: Linear INCREASE (λ ≈ 1)\n")
    }
  }
  
} else {
  cat("❌ Self-Centered Prosociality LGC model failed\n")
}

# =============================================================================
# SUMMARY
# =============================================================================

cat("\n=== SECOND-ORDER LGC SUMMARY ===\n")
cat("Tested three domains of self-control development:\n")
cat("1. Executive Control: Linear growth model\n")
cat("2. Temper Control: Linear growth model\n") 
cat("3. Self-Centered Prosociality: Latent basis model\n\n")

cat("Results interpret developmental trajectories from age 11 to 17.\n")
cat("Significant slopes indicate mean-level change over time.\n")
cat("Individual differences in growth captured by slope variances.\n")

cat("\n=== LGC EXTENSION COMPLETE ===\n") 