# =============================================================================
# TEST: EXPLICITLY FREED FIRST-ORDER FACTOR MEANS
# Verify that "Factor ~ 1" syntax allows growth parameters to be estimated
# =============================================================================

library(pacman)
p_load(lavaan, dplyr)

cat("=== TESTING EXPLICITLY FREED FACTOR MEANS ===\n")

# Load data
data_path <- "/home/<USER>/dissertation_folder/data"
if(file.exists(file.path(data_path, "merged1203.rds"))) {
  merged_data <- readRDS(file.path(data_path, "merged1203.rds"))
  cat("Data loaded successfully\n")
  
  # Run recoding if needed
  if(!"sc11_task_completion" %in% names(merged_data)) {
    cat("Running recoding scripts...\n")
    source("scripts/recode_self_control.R")
    source("scripts/recode_self_control_age14.R") 
    source("scripts/recode_self_control_age17.R")
  }
  
  # Test with Executive Control (3 items for speed)
  executive_items <- c("task_completion", "distracted", "think_act")
  exec_vars <- c(paste0("sc11_", executive_items),
                 paste0("sc14_", executive_items), 
                 paste0("sc17_", executive_items))
  
  test_data <- merged_data[, exec_vars, drop = FALSE]
  complete_rows <- rowSums(!is.na(test_data)) > 0
  test_data <- test_data[complete_rows, ]
  
  cat("Test sample size:", nrow(test_data), "\n")
  
  # Model with EXPLICITLY FREED factor means
  freed_means_model <- '
  # First level: Executive factors
  Executive11 =~ sc11_task_completion + sc11_distracted + sc11_think_act
  Executive14 =~ sc14_task_completion + sc14_distracted + sc14_think_act
  Executive17 =~ sc17_task_completion + sc17_distracted + sc17_think_act
  
  # Second level: Growth factors  
  iExecutive =~ 1*Executive11 + 1*Executive14 + 1*Executive17
  sExecutive =~ 0*Executive11 + 1*Executive14 + 2*Executive17
  
  # CRITICAL: Explicitly FREE first-order factor means
  Executive11 ~ 1
  Executive14 ~ 1
  Executive17 ~ 1
  '
  
  cat("\nFitting model with explicitly freed factor means...\n")
  fit_freed <- tryCatch({
    sem(
      model = freed_means_model,
      data = test_data,
      estimator = "WLSMV",
      ordered = exec_vars,
      missing = "pairwise",
      std.lv = TRUE,
      meanstructure = TRUE
    )
  }, error = function(e) {
    cat("Error:", e$message, "\n")
    return(NULL)
  })
  
  if(!is.null(fit_freed)) {
    converged <- lavInspect(fit_freed, "converged")
    cat("Model converged:", converged, "\n")
    
    if(converged) {
      cat("✅ Model with freed means converged!\n")
      
      # Check for identification problems
      param_table <- parameterEstimates(fit_freed)
      
      # Check growth parameters
      intercept_mean <- param_table[param_table$lhs == "iExecutive" & param_table$op == "~1", ]
      slope_mean <- param_table[param_table$lhs == "sExecutive" & param_table$op == "~1", ]
      
      cat("\n🎯 GROWTH PARAMETER RESULTS:\n")
      if(nrow(intercept_mean) > 0) {
        cat("Intercept mean:", round(intercept_mean$est, 4))
        if(!is.na(intercept_mean$se)) {
          cat(" (SE =", round(intercept_mean$se, 4), ")")
        }
        if(!is.na(intercept_mean$pvalue)) {
          cat(", p =", round(intercept_mean$pvalue, 4))
          if(intercept_mean$est != 0 && !is.na(intercept_mean$pvalue)) {
            cat(" ✅ NON-ZERO with valid p-value!")
          }
        } else {
          cat(", p = NA ❌")
        }
        cat("\n")
      }
      
      if(nrow(slope_mean) > 0) {
        cat("Slope mean:", round(slope_mean$est, 4))
        if(!is.na(slope_mean$se)) {
          cat(" (SE =", round(slope_mean$se, 4), ")")
        }
        if(!is.na(slope_mean$pvalue)) {
          cat(", p =", round(slope_mean$pvalue, 4))
          if(slope_mean$est != 0 && !is.na(slope_mean$pvalue)) {
            cat(" ✅ NON-ZERO with valid p-value!")
            if(slope_mean$pvalue < 0.05) {
              if(slope_mean$est > 0) {
                cat("\n  → Significant INCREASE over time!")
              } else {
                cat("\n  → Significant DECREASE over time!")
              }
            }
          }
        } else {
          cat(", p = NA ❌")
        }
        cat("\n")
      }
      
      # Check first-order factor means
      fo_means <- param_table[param_table$op == "~1" & grepl("Executive", param_table$lhs), ]
      cat("\n📊 FIRST-ORDER FACTOR MEANS:\n")
      for(i in 1:nrow(fo_means)) {
        cat(fo_means$lhs[i], "mean:", round(fo_means$est[i], 4))
        if(!is.na(fo_means$pvalue[i])) {
          cat(" (p =", round(fo_means$pvalue[i], 4), ")")
        }
        cat("\n")
      }
      
      # Check for negative variances
      variances <- param_table[param_table$op == "~~" & param_table$lhs == param_table$rhs, ]
      negative_vars <- variances[variances$est < 0, ]
      
      if(nrow(negative_vars) > 0) {
        cat("\n⚠️ WARNING: Negative variances detected:\n")
        for(i in 1:nrow(negative_vars)) {
          cat("  ", negative_vars$lhs[i], ":", round(negative_vars$est[i], 4), "\n")
        }
      } else {
        cat("\n✅ No negative variances!\n")
      }
      
      # Model fit
      fit_measures <- fitMeasures(fit_freed, c("cfi.scaled", "tli.scaled", "rmsea.scaled", "srmr"))
      cat("\n📈 MODEL FIT:\n")
      cat("CFI =", round(fit_measures["cfi.scaled"], 3), "\n")
      cat("TLI =", round(fit_measures["tli.scaled"], 3), "\n")
      cat("RMSEA =", round(fit_measures["rmsea.scaled"], 3), "\n")
      cat("SRMR =", round(fit_measures["srmr"], 3), "\n")
      
      if(fit_measures["cfi.scaled"] >= 0.95) {
        cat("✅ GOOD fit achieved!\n")
      } else if(fit_measures["cfi.scaled"] >= 0.90) {
        cat("✅ ACCEPTABLE fit achieved!\n")
      }
      
      cat("\n🎉 FREED FACTOR MEANS APPROACH WORKING!\n")
      
    } else {
      cat("❌ Model did not converge\n")
      
      # Check for convergence issues
      warnings <- lavInspect(fit_freed, "post.check")
      if(length(warnings) > 0) {
        cat("Convergence warnings:\n")
        print(warnings)
      }
    }
    
  } else {
    cat("❌ Model fitting failed completely\n")
  }
  
} else {
  cat("❌ Data file not found\n")
}

cat("\n=== FREED MEANS TEST COMPLETE ===\n") 
