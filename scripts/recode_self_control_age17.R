# =============================================================================
# Recode Self-Control age 17 variables
# =============================================================================

# Load packages
library(pacman)
p_load(psych, corrplot, lavaan, semTools, VIM)

# =============================================================================
# 1. SELF-CONTROL VARIABLES DEFINITION
# =============================================================================

# Variables that NEED reverse coding
self_control_reverse <- c(
  "gpsdst00",  # SDST: Think things out before acting
  "gpsdgf00",  # Having at least one good friend
  "gpsdlc00",   # Generally liked by other children
  "gpsdpf00",  # Being considerate of other people's feelings
  "gpsdsr00",  # Sharing readily with other children
  "gpsdhu00",  # Being helpful if someone is hurt
  "gpsdky00",  # Being kind to younger children
  "gpsdvh00",  # Often volunteering to help others
  "gpsdte00",  # Sees tasks through to the end, good attention span
  "gpsdor00"   # Generally obedient
)

# Variables that do NOT need reverse coding
self_control_normal <- c(
  "gpsdsp00",  # Being rather solitary and tending to play alone
  "gpsddc00",  # Is easily distracted, concentration wanders
  "gpsdgb00",  # Getting on better with adults than other children
  "gpsdtt00",  # Often has temper tantrums or hot tempers
  "gpsdmw00",  # Having many worries
  "gpsdud00",  # Being often unhappy, down-hearted, or tearful
  "gpsdnc00",  # Being nervous or clingy in new situations
  "gpsdfe00",  # Having many fears, being easily scared
  "gpsdpb00",  # Child is restless, overactive, cannot stay still for long
  "gpsdfs00",  # Child is constantly fidgeting or squirming
  "gpsdoa00"   # Lying or cheating
)

# Combined list of all self-control variables
all_self_control_vars <- c(self_control_reverse, self_control_normal)

cat("=== SELF-CONTROL VARIABLES PROCESSING (AGE 17) ===\n")
cat("Variables requiring reverse coding:", length(self_control_reverse), "\n")
cat("Variables with normal coding:", length(self_control_normal), "\n")
cat("Total variables:", length(all_self_control_vars), "\n\n")

# =============================================================================
# 2. CHECK EXISTING VARIABLES
# =============================================================================

# Check which variables exist in the data
existing_vars <- all_self_control_vars[all_self_control_vars %in% names(merged_data)]
missing_vars <- all_self_control_vars[!all_self_control_vars %in% names(merged_data)]

cat("=== VARIABLE AVAILABILITY ===\n")
cat("Found variables (", length(existing_vars), "):", paste(existing_vars, collapse = ", "), "\n")
if(length(missing_vars) > 0) {
  cat("Missing variables (", length(missing_vars), "):", paste(missing_vars, collapse = ", "), "\n")
}
cat("\n")

# =============================================================================
# 3. DISPLAY FREQUENCY TABLES FOR EXISTING VARIABLES
# =============================================================================

cat("=== ORIGINAL VARIABLE DISTRIBUTIONS ===\n")
for(var in existing_vars) {
  cat("\n--- ", var, " ---\n")
  var_numeric <- as.vector(merged_data[[var]])
  print(table(var_numeric, useNA = "ifany"))
}

# =============================================================================
# 4. RECODE VARIABLES THAT NEED REVERSE CODING
# =============================================================================

cat("\n=== REVERSE CODING VARIABLES ===\n")

# SDST: Think things out before acting (reverse code)
if("gpsdst00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$gpsdst00)
  merged_data$sc17_think_act <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                       ifelse(var_numeric == 1, 2,
                                              ifelse(var_numeric == 2, 1,
                                                     ifelse(var_numeric == 3, 0, NA))))
  cat("✓ Reverse coded: gpsdst00 -> sc17_think_act\n")
}

# Having at least one good friend (reverse code)
if("gpsdgf00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$gpsdgf00)
  merged_data$sc17_good_friend <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                         ifelse(var_numeric == 1, 2,
                                                ifelse(var_numeric == 2, 1,
                                                       ifelse(var_numeric == 3, 0, NA))))
  cat("✓ Reverse coded: gpsdgf00 -> sc17_good_friend\n")
}

# Generally liked by other children (reverse code)
if("gpsdlc00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$gpsdlc00)
  merged_data$sc17_liked_children <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                            ifelse(var_numeric == 1, 2,
                                                   ifelse(var_numeric == 2, 1,
                                                          ifelse(var_numeric == 3, 0, NA))))
  cat("✓ Reverse coded: gpsdlc00 -> sc17_liked_children\n")
}

# Being considerate of other people's feelings (reverse code)
if("gpsdpf00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$gpsdpf00)
  merged_data$sc17_considerate <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                         ifelse(var_numeric == 1, 2,
                                                ifelse(var_numeric == 2, 1,
                                                       ifelse(var_numeric == 3, 0, NA))))
  cat("✓ Reverse coded: gpsdpf00 -> sc17_considerate\n")
}

# Sharing readily with other children (reverse code)
if("gpsdsr00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$gpsdsr00)
  merged_data$sc17_sharing <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                     ifelse(var_numeric == 1, 2,
                                            ifelse(var_numeric == 2, 1,
                                                   ifelse(var_numeric == 3, 0, NA))))
  cat("✓ Reverse coded: gpsdsr00 -> sc17_sharing\n")
}

# Being helpful if someone is hurt (reverse code)
if("gpsdhu00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$gpsdhu00)
  merged_data$sc17_helpful <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                     ifelse(var_numeric == 1, 2,
                                            ifelse(var_numeric == 2, 1,
                                                   ifelse(var_numeric == 3, 0, NA))))
  cat("✓ Reverse coded: gpsdhu00 -> sc17_helpful\n")
}

# Being kind to younger children (reverse code)
if("gpsdky00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$gpsdky00)
  merged_data$sc17_kind_younger <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                          ifelse(var_numeric == 1, 2,
                                                 ifelse(var_numeric == 2, 1,
                                                        ifelse(var_numeric == 3, 0, NA))))
  cat("✓ Reverse coded: gpsdky00 -> sc17_kind_younger\n")
}

# Often volunteering to help others (reverse code)
if("gpsdvh00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$gpsdvh00)
  merged_data$sc17_volunteer_help <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                            ifelse(var_numeric == 1, 2,
                                                   ifelse(var_numeric == 2, 1,
                                                          ifelse(var_numeric == 3, 0, NA))))
  cat("✓ Reverse coded: gpsdvh00 -> sc17_volunteer_help\n")
}

# Sees tasks through to the end, good attention span (reverse code)
if("gpsdte00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$gpsdte00)
  merged_data$sc17_task_completion <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                             ifelse(var_numeric == 1, 2,
                                                    ifelse(var_numeric == 2, 1,
                                                           ifelse(var_numeric == 3, 0, NA))))
  cat("✓ Reverse coded: gpsdte00 -> sc17_task_completion\n")
}

# Generally obedient (reverse code)
if("gpsdor00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$gpsdor00)
  merged_data$sc17_obedient <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                      ifelse(var_numeric == 1, 2,
                                             ifelse(var_numeric == 2, 1,
                                                    ifelse(var_numeric == 3, 0, NA))))
  cat("✓ Reverse coded: gpsdor00 -> sc17_obedient\n")
}

# =============================================================================
# 5. RECODE VARIABLES THAT DO NOT NEED REVERSE CODING
# =============================================================================

cat("\n=== NORMAL CODING VARIABLES ===\n")

# Being rather solitary and tending to play alone (normal coding)
if("gpsdsp00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$gpsdsp00)
  merged_data$sc17_solitary <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                      ifelse(var_numeric == 1, 0,
                                             ifelse(var_numeric == 2, 1,
                                                    ifelse(var_numeric == 3, 2, NA))))
  cat("✓ Normal coded: gpsdsp00 -> sc17_solitary\n")
}

# Is easily distracted, concentration wanders (normal coding)
if("gpsddc00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$gpsddc00)
  merged_data$sc17_distracted <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                        ifelse(var_numeric == 1, 0,
                                               ifelse(var_numeric == 2, 1,
                                                      ifelse(var_numeric == 3, 2, NA))))
  cat("✓ Normal coded: gpsddc00 -> sc17_distracted\n")
}

# Getting on better with adults than other children (normal coding)
if("gpsdgb00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$gpsdgb00)
  merged_data$sc17_better_adults <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                           ifelse(var_numeric == 1, 0,
                                                  ifelse(var_numeric == 2, 1,
                                                         ifelse(var_numeric == 3, 2, NA))))
  cat("✓ Normal coded: gpsdgb00 -> sc17_better_adults\n")
}

# Often has temper tantrums or hot tempers (normal coding)
if("gpsdtt00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$gpsdtt00)
  merged_data$sc17_temper <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                    ifelse(var_numeric == 1, 0,
                                           ifelse(var_numeric == 2, 1,
                                                  ifelse(var_numeric == 3, 2, NA))))
  cat("✓ Normal coded: gpsdtt00 -> sc17_temper\n")
}

# Having many worries (normal coding)
if("gpsdmw00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$gpsdmw00)
  merged_data$sc17_worries <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                     ifelse(var_numeric == 1, 0,
                                            ifelse(var_numeric == 2, 1,
                                                   ifelse(var_numeric == 3, 2, NA))))
  cat("✓ Normal coded: gpsdmw00 -> sc17_worries\n")
}

# Being often unhappy, down-hearted, or tearful (normal coding)
if("gpsdud00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$gpsdud00)
  merged_data$sc17_unhappy <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                     ifelse(var_numeric == 1, 0,
                                            ifelse(var_numeric == 2, 1,
                                                   ifelse(var_numeric == 3, 2, NA))))
  cat("✓ Normal coded: gpsdud00 -> sc17_unhappy\n")
}

# Being nervous or clingy in new situations (normal coding)
if("gpsdnc00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$gpsdnc00)
  merged_data$sc17_nervous <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                     ifelse(var_numeric == 1, 0,
                                            ifelse(var_numeric == 2, 1,
                                                   ifelse(var_numeric == 3, 2, NA))))
  cat("✓ Normal coded: gpsdnc00 -> sc17_nervous\n")
}

# Having many fears, being easily scared (normal coding)
if("gpsdfe00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$gpsdfe00)
  merged_data$sc17_fears <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                   ifelse(var_numeric == 1, 0,
                                          ifelse(var_numeric == 2, 1,
                                                 ifelse(var_numeric == 3, 2, NA))))
  cat("✓ Normal coded: gpsdfe00 -> sc17_fears\n")
}

# Child is restless, overactive, cannot stay still for long (normal coding)
if("gpsdpb00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$gpsdpb00)
  merged_data$sc17_restless <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                      ifelse(var_numeric == 1, 0,
                                             ifelse(var_numeric == 2, 1,
                                                    ifelse(var_numeric == 3, 2, NA))))
  cat("✓ Normal coded: gpsdpb00 -> sc17_restless\n")
}

# Child is constantly fidgeting or squirming (normal coding)
if("gpsdfs00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$gpsdfs00)
  merged_data$sc17_fidgeting <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                       ifelse(var_numeric == 1, 0,
                                              ifelse(var_numeric == 2, 1,
                                                     ifelse(var_numeric == 3, 2, NA))))
  cat("✓ Normal coded: gpsdfs00 -> sc17_fidgeting\n")
}

# Lying or cheating (normal coding)
if("gpsdoa00" %in% names(merged_data)) {
  var_numeric <- as.vector(merged_data$gpsdoa00)
  merged_data$sc17_lying <- ifelse(var_numeric < 0 | var_numeric == 4, NA,
                                   ifelse(var_numeric == 1, 0,
                                          ifelse(var_numeric == 2, 1,
                                                 ifelse(var_numeric == 3, 2, NA))))
  cat("✓ Normal coded: gpsdoa00 -> sc17_lying\n")
}

# =============================================================================
# 6. CHECK RECODED VARIABLES
# =============================================================================

# List of all new self-control variables
sc17_vars_reverse <- c("sc17_think_act", "sc17_good_friend", "sc17_liked_children",
                       "sc17_considerate", "sc17_sharing", "sc17_helpful", "sc17_kind_younger", "sc17_volunteer_help",
                       "sc17_task_completion", "sc17_obedient")

sc17_vars_normal <- c("sc17_solitary", "sc17_distracted", "sc17_better_adults", "sc17_temper",
                      "sc17_worries", "sc17_unhappy", "sc17_nervous", "sc17_fears", "sc17_restless", "sc17_fidgeting", "sc17_lying")

all_sc17_vars <- c(sc17_vars_reverse, sc17_vars_normal)

# Check which recoded variables exist
existing_recoded <- all_sc17_vars[all_sc17_vars %in% names(merged_data)]

cat("\n=== RECODED VARIABLE DISTRIBUTIONS ===\n")
cat("Successfully recoded variables (", length(existing_recoded), "):\n")

for(var in existing_recoded) {
  cat("\n--- ", var, " ---\n")
  print(table(merged_data[[var]], useNA = "ifany"))
}

# =============================================================================
# 7. SUMMARY
# =============================================================================

cat("\n=== PROCESSING SUMMARY ===\n")
cat("Original variables found:", length(existing_vars), "/", length(all_self_control_vars), "\n")
cat("Variables requiring reverse coding:", length(sc17_vars_reverse), "(10 total)\n")
cat("Variables with normal coding:", length(sc17_vars_normal), "(11 total)\n")
cat("Successfully processed variables:", length(existing_recoded), "\n")

if(length(missing_vars) > 0) {
  cat("\nMissing variables that need to be checked:\n")
  for(var in missing_vars) {
    cat("- ", var, "\n")
  }
}

cat("\n=== VARIABLE MAPPING ===\n")
cat("Variables that need reverse coding (higher original score = lower self-control):\n")
reverse_mapping <- data.frame(
  Original = c("gpsdst00", "gpsdgf00", "gpsdlc00", "gpsdpf00", 
               "gpsdsr00", "gpsdhu00", "gpsdky00", "gpsdvh00", "gpsdte00", "gpsdor00"),
  New = c("sc17_think_act", "sc17_good_friend", "sc17_liked_children",
          "sc17_considerate", "sc17_sharing", "sc17_helpful", "sc17_kind_younger", "sc17_volunteer_help", 
          "sc17_task_completion", "sc17_obedient"),
  Description = c("Think things out before acting", "Having at least one good friend",
                  "Generally liked by other children",
                  "Being considerate of feelings", "Sharing readily with children",
                  "Being helpful if someone hurt", "Being kind to younger children",
                  "Often volunteering to help others", "Sees tasks through to end", "Generally obedient")
)
print(reverse_mapping)

cat("\nVariables with normal coding (higher score = lower self-control):\n")
normal_mapping <- data.frame(
  Original = c("gpsdsp00", "gpsddc00", "gpsdgb00", "gpsdtt00", "gpsdmw00",
               "gpsdud00", "gpsdnc00", "gpsdfe00", "gpsdpb00", "gpsdfs00", "gpsdoa00"),
  New = c("sc17_solitary", "sc17_distracted", "sc17_better_adults", "sc17_temper", "sc17_worries",
          "sc17_unhappy", "sc17_nervous", "sc17_fears", "sc17_restless", "sc17_fidgeting", "sc17_lying"),
  Description = c("Being rather solitary", "Easily distracted", "Getting on better with adults",
                  "Often has temper tantrums", "Having many worries", "Often unhappy/tearful",
                  "Nervous in new situations", "Having many fears", "Restless/overactive",
                  "Constantly fidgeting", "Lying or cheating")
)
print(normal_mapping)

cat("\nNote: All age 17 self-control variables are now processed in this script.\n")
cat("All recoded variables have prefix 'sc17_' to distinguish from age 11 and age 14 variables.\n") 
