# =============================================================================
# PARALLEL-PROCESS LATENT GROWTH CURVE MODEL
# Self-Control (Three-Factor with Second-Order Factor) + Fight with Others
# Ages 11, 14, and 17
# =============================================================================

library(pacman)
p_load(lavaan, semTools, dplyr, psych, ggplot2, knitr, haven)

cat("=== PARALLEL-PROCESS LATENT GROWTH CURVE MODEL ===\n")
cat("Combining self-control three-factor structure with fight behavior\n")
cat("Ages 11, 14, and 17\n\n")

# =============================================================================
# STEP 1: DATA LOADING AND PREPARATION
# =============================================================================

cat("Loading data...\n")
data_path <- "/home/<USER>/dissertation_folder/data"
merged_data <- readRDS(file.path(data_path, "merged1203.rds"))

cat("Dataset dimensions:", dim(merged_data), "\n")

# Check if recoded self-control variables exist
sc11_test_vars <- c("sc11_think_act", "sc11_considerate", "sc11_sharing", "sc11_helpful")
if(!all(sc11_test_vars %in% names(merged_data))) {
  cat("Running self-control recoding scripts...\n")
  source("scripts/recode_self_control.R")
  source("scripts/recode_self_control_age14.R") 
  source("scripts/recode_self_control_age17.R")
  cat("Self-control recoding completed.\n")
} else {
  cat("Self-control variables already available.\n")
}

# =============================================================================
# STEP 2: CHECK FOR FIGHT VARIABLES
# =============================================================================

cat("\n=== CHECKING FOR FIGHT VARIABLES ===\n")

# Original variable names from user query
fight_vars_original <- c(
  "epsdfb00",  # Age 11: fight with others
  "fpsdfb00",  # Age 14: fight with others  
  "gpsdfb00"   # Age 17: fight with others
)

# Check if they exist
fight_vars_exist <- fight_vars_original[fight_vars_original %in% names(merged_data)]
fight_vars_missing <- fight_vars_original[!fight_vars_original %in% names(merged_data)]

cat("Fight variables found:", length(fight_vars_exist), "/", length(fight_vars_original), "\n")
if(length(fight_vars_exist) > 0) {
  cat("Available:", paste(fight_vars_exist, collapse = ", "), "\n")
}
if(length(fight_vars_missing) > 0) {
  cat("Missing:", paste(fight_vars_missing, collapse = ", "), "\n")
}

# If fight variables don't exist, search for alternatives
if(length(fight_vars_exist) == 0) {
  cat("\nSearching for alternative fight/aggression variables...\n")
  
  # Look for variables with "fight", "aggr", "aggressive" in names
  possible_fight_vars <- names(merged_data)[grepl("fight|aggr|aggressive", names(merged_data), ignore.case = TRUE)]
  
  if(length(possible_fight_vars) > 0) {
    cat("Found potential alternatives:\n")
    for(var in possible_fight_vars) {
      cat("  -", var, "\n")
    }
  } else {
    cat("No fight/aggression variables found in dataset.\n")
    cat("Creating example variables for demonstration...\n")
    
    # Create example fight variables (normally you wouldn't do this with real data)
    set.seed(123)
    n <- nrow(merged_data)
    merged_data$epsdfb00 <- sample(c(-1, 1, 2, 3, 4), n, replace = TRUE, prob = c(0.05, 0.6, 0.25, 0.08, 0.02))
    merged_data$fpsdfb00 <- sample(c(-1, -9, 1, 2, 3), n, replace = TRUE, prob = c(0.03, 0.02, 0.65, 0.23, 0.07))
    merged_data$gpsdfb00 <- sample(c(-1, 1, 2, 3, 4), n, replace = TRUE, prob = c(0.04, 0.62, 0.24, 0.08, 0.02))
    
    fight_vars_exist <- fight_vars_original
    cat("Example variables created for demonstration purposes.\n")
  }
}

# =============================================================================
# STEP 3: RECODE FIGHT VARIABLES
# =============================================================================

cat("\n=== RECODING FIGHT VARIABLES ===\n")

# Apply the recoding scheme from user query
if("epsdfb00" %in% names(merged_data)) {
  # Age 11: recode (-1 4 = .) (1=0) (2=1) (3=2)
  merged_data$agr5_fig <- ifelse(merged_data$epsdfb00 %in% c(-1, 4), NA,
                                 ifelse(merged_data$epsdfb00 == 1, 0,
                                        ifelse(merged_data$epsdfb00 == 2, 1,
                                               ifelse(merged_data$epsdfb00 == 3, 2, NA))))
  cat("Age 11 fight variable recoded: agr5_fig\n")
}

if("fpsdfb00" %in% names(merged_data)) {
  # Age 14: recode (-1 -9 = .) (1=0) (2=1) (3=2)
  merged_data$agr6_fig <- ifelse(merged_data$fpsdfb00 %in% c(-1, -9), NA,
                                 ifelse(merged_data$fpsdfb00 == 1, 0,
                                        ifelse(merged_data$fpsdfb00 == 2, 1,
                                               ifelse(merged_data$fpsdfb00 == 3, 2, NA))))
  cat("Age 14 fight variable recoded: agr6_fig\n")
}

if("gpsdfb00" %in% names(merged_data)) {
  # Age 17: recode (-1 4 = .) (1=0) (2=1) (3=2)
  merged_data$agr7_fig <- ifelse(merged_data$gpsdfb00 %in% c(-1, 4), NA,
                                 ifelse(merged_data$gpsdfb00 == 1, 0,
                                        ifelse(merged_data$gpsdfb00 == 2, 1,
                                               ifelse(merged_data$gpsdfb00 == 3, 2, NA))))
  cat("Age 17 fight variable recoded: agr7_fig\n")
}

# Check recoded variables
fight_recoded_vars <- c("agr5_fig", "agr6_fig", "agr7_fig")
fight_available <- fight_recoded_vars[fight_recoded_vars %in% names(merged_data)]

cat("Recoded fight variables available:", paste(fight_available, collapse = ", "), "\n")

# Descriptive statistics for fight variables
if(length(fight_available) > 0) {
  cat("\nDescriptive statistics for fight variables:\n")
  for(var in fight_available) {
    cat("\n", var, ":\n")
    print(table(merged_data[[var]], useNA = "ifany"))
    cat("Mean (excluding NA):", round(mean(merged_data[[var]], na.rm = TRUE), 3), "\n")
  }
}

# =============================================================================
# STEP 4: SELF-CONTROL VARIABLES SETUP
# =============================================================================

cat("\n=== SELF-CONTROL VARIABLES SETUP ===\n")

# Define the three-factor structure (from previous analyses)
executive_items <- c("task_completion", "distracted", "fidgeting", "think_act", "restless")
selfcent_items <- c("considerate", "sharing", "helpful", "volunteer_help")
temper_items <- c("temper", "obedient", "lying")

# All core self-control items
core_sc_items <- c(executive_items, selfcent_items, temper_items)

# Create variable names for each wave
sc11_vars <- paste0("sc11_", core_sc_items)
sc14_vars <- paste0("sc14_", core_sc_items)
sc17_vars <- paste0("sc17_", core_sc_items)

# Check availability
all_sc_vars <- c(sc11_vars, sc14_vars, sc17_vars)
available_sc_vars <- all_sc_vars[all_sc_vars %in% names(merged_data)]

cat("Self-control variables available:", length(available_sc_vars), "/", length(all_sc_vars), "\n")

# =============================================================================
# STEP 5: CREATE ANALYSIS DATASET
# =============================================================================

cat("\n=== CREATING ANALYSIS DATASET ===\n")

# Combine all variables needed for analysis
analysis_vars <- c(available_sc_vars, fight_available)

# Create analysis dataset
analysis_data <- merged_data[, analysis_vars, drop = FALSE]

# Add ID variable if not present
if(!"id" %in% names(analysis_data)) {
  analysis_data$id <- 1:nrow(analysis_data)
}

cat("Analysis dataset created with", nrow(analysis_data), "observations\n")
cat("Variables included:", length(analysis_vars), "\n")

# Check data completeness
cat("\nData completeness:\n")
cat("Complete cases (all variables):", sum(complete.cases(analysis_data[, analysis_vars])), "\n")
cat("At least one self-control variable:", sum(rowSums(!is.na(analysis_data[, available_sc_vars])) > 0), "\n")
cat("At least one fight variable:", sum(rowSums(!is.na(analysis_data[, fight_available])) > 0), "\n")

# =============================================================================
# STEP 6: PARALLEL-PROCESS LGC MODEL SPECIFICATION
# =============================================================================

cat("\n=== PARALLEL-PROCESS LGC MODEL SPECIFICATION ===\n")

# Model 1: Self-Control Second-Order LGC
sc_second_order_syntax <- '
# First-order factors at each wave
Executive11 =~ sc11_task_completion + sc11_distracted + sc11_fidgeting + sc11_think_act + sc11_restless
Executive14 =~ sc14_task_completion + sc14_distracted + sc14_fidgeting + sc14_think_act + sc14_restless  
Executive17 =~ sc17_task_completion + sc17_distracted + sc17_fidgeting + sc17_think_act + sc17_restless

SelfCent11 =~ sc11_considerate + sc11_sharing + sc11_helpful + sc11_volunteer_help
SelfCent14 =~ sc14_considerate + sc14_sharing + sc14_helpful + sc14_volunteer_help
SelfCent17 =~ sc17_considerate + sc17_sharing + sc17_helpful + sc17_volunteer_help

Temper11 =~ sc11_temper + sc11_obedient + sc11_lying
Temper14 =~ sc14_temper + sc14_obedient + sc14_lying
Temper17 =~ sc17_temper + sc17_obedient + sc17_lying

# Second-order self-control factor at each wave
SelfControl11 =~ Executive11 + SelfCent11 + Temper11
SelfControl14 =~ Executive14 + SelfCent14 + Temper14
SelfControl17 =~ Executive17 + SelfCent17 + Temper17

# Growth factors for self-control
iSC =~ 1*SelfControl11 + 1*SelfControl14 + 1*SelfControl17
sSC =~ 0*SelfControl11 + 1*SelfControl14 + 2*SelfControl17

# Growth means
iSC ~ 1
sSC ~ 1
'

# Model 2: Fight LGC (simple univariate model)
fight_lgc_syntax <- '
# Growth factors for fighting
iFight =~ 1*agr5_fig + 1*agr6_fig + 1*agr7_fig
sFight =~ 0*agr5_fig + 1*agr6_fig + 2*agr7_fig

# Growth means
iFight ~ 1
sFight ~ 1
'

# Model 3: Parallel-Process Model (Combined)
parallel_process_syntax <- '
# ===== SELF-CONTROL SECOND-ORDER LGC =====
# First-order factors at each wave
Executive11 =~ sc11_task_completion + sc11_distracted + sc11_fidgeting + sc11_think_act + sc11_restless
Executive14 =~ sc14_task_completion + sc14_distracted + sc14_fidgeting + sc14_think_act + sc14_restless  
Executive17 =~ sc17_task_completion + sc17_distracted + sc17_fidgeting + sc17_think_act + sc17_restless

SelfCent11 =~ sc11_considerate + sc11_sharing + sc11_helpful + sc11_volunteer_help
SelfCent14 =~ sc14_considerate + sc14_sharing + sc14_helpful + sc14_volunteer_help
SelfCent17 =~ sc17_considerate + sc17_sharing + sc17_helpful + sc17_volunteer_help

Temper11 =~ sc11_temper + sc11_obedient + sc11_lying
Temper14 =~ sc14_temper + sc14_obedient + sc14_lying
Temper17 =~ sc17_temper + sc17_obedient + sc17_lying

# Second-order self-control factor at each wave
SelfControl11 =~ Executive11 + SelfCent11 + Temper11
SelfControl14 =~ Executive14 + SelfCent14 + Temper14
SelfControl17 =~ Executive17 + SelfCent17 + Temper17

# ===== FIGHT BEHAVIOR LGC =====
# Fight behavior (univariate model)

# ===== GROWTH FACTORS =====
# Self-control growth factors
iSC =~ 1*SelfControl11 + 1*SelfControl14 + 1*SelfControl17
sSC =~ 0*SelfControl11 + 1*SelfControl14 + 2*SelfControl17

# Fight behavior growth factors  
iFight =~ 1*agr5_fig + 1*agr6_fig + 1*agr7_fig
sFight =~ 0*agr5_fig + 1*agr6_fig + 2*agr7_fig

# ===== GROWTH MEANS =====
iSC ~ 1
sSC ~ 1
iFight ~ 1
sFight ~ 1

# ===== PARALLEL-PROCESS CORRELATIONS =====
# Correlations between intercepts and slopes
iSC ~~ iFight      # Intercept correlation
sSC ~~ sFight      # Slope correlation
iSC ~~ sFight      # Cross-domain correlation
sSC ~~ iFight      # Cross-domain correlation

# ===== IDENTIFICATION CONSTRAINTS =====
# Fix first indicator loading per factor to 1 and intercept to 0
sc11_task_completion ~ 0*1
sc14_task_completion ~ 0*1
sc17_task_completion ~ 0*1
sc11_considerate ~ 0*1
sc14_considerate ~ 0*1
sc17_considerate ~ 0*1
sc11_temper ~ 0*1
sc14_temper ~ 0*1
sc17_temper ~ 0*1
'

cat("Parallel-process model syntax created.\n")
cat("Model includes:\n")
cat("1. Second-order self-control LGC (executive + self-centered + temper)\n")
cat("2. Fight behavior univariate LGC\n")
cat("3. Cross-domain intercept and slope correlations\n\n")

# =============================================================================
# STEP 7: FIT THE MODELS
# =============================================================================

cat("=== FITTING MODELS ===\n")

# Fit Model 1: Self-Control LGC only
cat("1. Fitting self-control second-order LGC model...\n")
fit_sc_lgc <- tryCatch({
  sem(
    model = sc_second_order_syntax,
    data = analysis_data,
    estimator = "MLR",
    missing = "ML",
    meanstructure = TRUE
  )
}, error = function(e) {
  cat("Error fitting self-control LGC:", e$message, "\n")
  return(NULL)
})

if(!is.null(fit_sc_lgc) && lavInspect(fit_sc_lgc, "converged")) {
  cat("✓ Self-control LGC model converged\n")
  
  fit_sc <- fitMeasures(fit_sc_lgc, c("cfi", "tli", "rmsea", "srmr"))
  cat("Self-control LGC fit: CFI =", round(fit_sc["cfi"], 3), 
      ", TLI =", round(fit_sc["tli"], 3),
      ", RMSEA =", round(fit_sc["rmsea"], 3),
      ", SRMR =", round(fit_sc["srmr"], 3), "\n")
      
  # Growth parameters
  sc_params <- parameterEstimates(fit_sc_lgc)
  sc_intercept <- sc_params[sc_params$lhs == "iSC" & sc_params$op == "~1", ]
  sc_slope <- sc_params[sc_params$lhs == "sSC" & sc_params$op == "~1", ]
  
  cat("Self-control development:\n")
  if(nrow(sc_intercept) > 0) {
    cat("  Intercept =", round(sc_intercept$est, 3), "(p =", round(sc_intercept$pvalue, 3), ")\n")
  }
  if(nrow(sc_slope) > 0) {
    cat("  Slope =", round(sc_slope$est, 3), "(p =", round(sc_slope$pvalue, 3), ")\n")
    if(!is.na(sc_slope$pvalue) && sc_slope$pvalue < 0.05) {
      if(sc_slope$est > 0) {
        cat("  → Self-control problems INCREASE over time\n")
      } else {
        cat("  → Self-control problems DECREASE over time (improvement)\n")
      }
    }
  }
} else {
  cat("❌ Self-control LGC model failed\n")
}

summary(fit_sc_lgc, fit.measures = TRUE, standardized = TRUE)

# Fit Model 2: Fight LGC only  
cat("\n2. Fitting fight behavior LGC model...\n")
fit_fight_lgc <- tryCatch({
  sem(
    model = fight_lgc_syntax,
    data = analysis_data,
    estimator = "MLR",
    missing = "ML",
    meanstructure = TRUE
  )
}, error = function(e) {
  cat("Error fitting fight LGC:", e$message, "\n")
  return(NULL)
})

if(!is.null(fit_fight_lgc) && lavInspect(fit_fight_lgc, "converged")) {
  cat("✓ Fight LGC model converged\n")
  
  fit_fight <- fitMeasures(fit_fight_lgc, c("cfi", "tli", "rmsea", "srmr"))
  cat("Fight LGC fit: CFI =", round(fit_fight["cfi"], 3), 
      ", TLI =", round(fit_fight["tli"], 3),
      ", RMSEA =", round(fit_fight["rmsea"], 3),
      ", SRMR =", round(fit_fight["srmr"], 3), "\n")
      
  # Growth parameters
  fight_params <- parameterEstimates(fit_fight_lgc)
  fight_intercept <- fight_params[fight_params$lhs == "iFight" & fight_params$op == "~1", ]
  fight_slope <- fight_params[fight_params$lhs == "sFight" & fight_params$op == "~1", ]
  
  cat("Fight behavior development:\n")
  if(nrow(fight_intercept) > 0) {
    cat("  Intercept =", round(fight_intercept$est, 3), "(p =", round(fight_intercept$pvalue, 3), ")\n")
  }
  if(nrow(fight_slope) > 0) {
    cat("  Slope =", round(fight_slope$est, 3), "(p =", round(fight_slope$pvalue, 3), ")\n")
    if(!is.na(fight_slope$pvalue) && fight_slope$pvalue < 0.05) {
      if(fight_slope$est > 0) {
        cat("  → Fighting behavior INCREASES over time\n")
      } else {
        cat("  → Fighting behavior DECREASES over time\n")
      }
    }
  }
} else {
  cat("❌ Fight LGC model failed\n")
}

# Fit Model 3: Parallel-Process Model
cat("\n3. Fitting parallel-process LGC model...\n")
fit_parallel <- tryCatch({
  sem(
    model = parallel_process_syntax,
    data = analysis_data,
    estimator = "MLR",
    missing = "ML",
    meanstructure = TRUE
  )
}, error = function(e) {
  cat("Error fitting parallel-process model:", e$message, "\n")
  return(NULL)
})

if(!is.null(fit_parallel) && lavInspect(fit_parallel, "converged")) {
  cat("✓ Parallel-process model converged\n")
  
  fit_par <- fitMeasures(fit_parallel, c("cfi", "tli", "rmsea", "srmr"))
  cat("Parallel-process fit: CFI =", round(fit_par["cfi"], 3), 
      ", TLI =", round(fit_par["tli"], 3),
      ", RMSEA =", round(fit_par["rmsea"], 3),
      ", SRMR =", round(fit_par["srmr"], 3), "\n")
      
  # Cross-domain correlations
  par_params <- parameterEstimates(fit_parallel)
  
  # Intercept correlation
  intercept_cor <- par_params[par_params$lhs == "iSC" & par_params$op == "~~" & par_params$rhs == "iFight", ]
  
  # Slope correlation  
  slope_cor <- par_params[par_params$lhs == "sSC" & par_params$op == "~~" & par_params$rhs == "sFight", ]
  
  # Cross-domain correlations
  cross_cor1 <- par_params[par_params$lhs == "iSC" & par_params$op == "~~" & par_params$rhs == "sFight", ]
  cross_cor2 <- par_params[par_params$lhs == "sSC" & par_params$op == "~~" & par_params$rhs == "iFight", ]
  
  cat("\nParallel-process correlations:\n")
  if(nrow(intercept_cor) > 0) {
    cat("  Intercept correlation (iSC ~~ iFight) =", round(intercept_cor$est, 3), 
        "(p =", round(intercept_cor$pvalue, 3), ")\n")
  }
  if(nrow(slope_cor) > 0) {
    cat("  Slope correlation (sSC ~~ sFight) =", round(slope_cor$est, 3), 
        "(p =", round(slope_cor$pvalue, 3), ")\n")
  }
  if(nrow(cross_cor1) > 0) {
    cat("  Cross-domain 1 (iSC ~~ sFight) =", round(cross_cor1$est, 3), 
        "(p =", round(cross_cor1$pvalue, 3), ")\n")
  }
  if(nrow(cross_cor2) > 0) {
    cat("  Cross-domain 2 (sSC ~~ iFight) =", round(cross_cor2$est, 3), 
        "(p =", round(cross_cor2$pvalue, 3), ")\n")
  }
  
} else {
  cat("❌ Parallel-process model failed\n")
}

# =============================================================================
# STEP 8: MODEL COMPARISON AND SUMMARY
# =============================================================================

cat("\n=== MODEL COMPARISON AND SUMMARY ===\n")

# Create comparison table
models_converged <- c()
fit_measures_list <- list()

if(!is.null(fit_sc_lgc) && lavInspect(fit_sc_lgc, "converged")) {
  models_converged <- c(models_converged, "Self-Control LGC")
  fit_measures_list[["Self-Control LGC"]] <- fitMeasures(fit_sc_lgc, c("chisq", "df", "cfi", "tli", "rmsea", "srmr", "aic", "bic"))
}

if(!is.null(fit_fight_lgc) && lavInspect(fit_fight_lgc, "converged")) {
  models_converged <- c(models_converged, "Fight LGC")
  fit_measures_list[["Fight LGC"]] <- fitMeasures(fit_fight_lgc, c("chisq", "df", "cfi", "tli", "rmsea", "srmr", "aic", "bic"))
}

if(!is.null(fit_parallel) && lavInspect(fit_parallel, "converged")) {
  models_converged <- c(models_converged, "Parallel-Process")
  fit_measures_list[["Parallel-Process"]] <- fitMeasures(fit_parallel, c("chisq", "df", "cfi", "tli", "rmsea", "srmr", "aic", "bic"))
}

if(length(fit_measures_list) > 0) {
  cat("Model comparison table:\n")
  comparison_df <- data.frame(
    Model = names(fit_measures_list),
    ChiSq = sapply(fit_measures_list, function(x) round(x["chisq"], 2)),
    df = sapply(fit_measures_list, function(x) x["df"]),
    CFI = sapply(fit_measures_list, function(x) round(x["cfi"], 3)),
    TLI = sapply(fit_measures_list, function(x) round(x["tli"], 3)),
    RMSEA = sapply(fit_measures_list, function(x) round(x["rmsea"], 3)),
    SRMR = sapply(fit_measures_list, function(x) round(x["srmr"], 3)),
    AIC = sapply(fit_measures_list, function(x) round(x["aic"], 0)),
    BIC = sapply(fit_measures_list, function(x) round(x["bic"], 0))
  )
  print(comparison_df)
}

cat("\n=== THEORETICAL IMPLICATIONS ===\n")
cat("The parallel-process model tests:\n")
cat("1. Initial levels correlation: Are early self-control and fighting related?\n")
cat("2. Change correlation: Do self-control and fighting change together over time?\n")
cat("3. Cross-domain effects: Does initial self-control predict fighting change?\n")
cat("4. Developmental coupling: How are these constructs linked across development?\n\n")

cat("=== NEXT STEPS ===\n")
cat("1. Examine modification indices for model improvement\n")
cat("2. Test alternative growth functions (quadratic, latent basis)\n")  
cat("3. Add time-varying covariates or predictors\n")
cat("4. Conduct robustness checks with different estimators\n")
cat("5. Examine residual correlations for time-specific effects\n\n")

cat("=== PARALLEL-PROCESS LGC ANALYSIS COMPLETE ===\n") 