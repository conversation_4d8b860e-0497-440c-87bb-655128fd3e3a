# Test Parallel Setup for blavaan
# Quick test to verify parallel processing is working
#################

library(blavaan)
library(lavaan)
library(parallel)

# Test parallel setup
cat("=== TESTING PARALLEL SETUP ===\n")
n_cores <- detectCores()
n_cores_use <- max(1, n_cores - 1)

cat("System cores:", n_cores, "\n")
cat("Cores to use:", n_cores_use, "\n")

# Set up parallel options
options(mc.cores = n_cores_use)
Sys.setenv(STAN_NUM_THREADS = n_cores_use)

cat("✓ Parallel options configured\n")

# Test with a simple model
cat("\n=== TESTING WITH SIMPLE MODEL ===\n")

# Generate simple test data
set.seed(123)
n <- 100
test_data <- data.frame(
  x1 = rnorm(n),
  x2 = rnorm(n),
  x3 = rnorm(n)
)
test_data$y <- 0.5 * test_data$x1 + 0.3 * test_data$x2 + rnorm(n, 0, 0.5)

# Simple regression model
test_model <- 'y ~ x1 + x2 + x3'

cat("Running test model with parallel chains...\n")
start_time <- Sys.time()

# Test bsem with minimal iterations
test_fit <- bsem(test_model, 
                data = test_data,
                n.chains = 2,
                burnin = 10,
                sample = 20,
                target = "stan",
                bcontrol = list(
                  verbose = TRUE,
                  refresh = 5
                ))

end_time <- Sys.time()
elapsed <- end_time - start_time

cat("\n=== TEST RESULTS ===\n")
cat("Test completed successfully!\n")
cat("Elapsed time:", round(elapsed, 2), attr(elapsed, "units"), "\n")

# Check if model converged
rhat_vals <- blavInspect(test_fit, "rhat")
max_rhat <- max(rhat_vals, na.rm = TRUE)
cat("Max R-hat:", round(max_rhat, 3), "\n")

if(max_rhat < 1.2) {
  cat("✓ Parallel setup working correctly\n")
} else {
  cat("⚠ May need more iterations for convergence\n")
}

cat("\n=== PARALLEL SETUP VERIFIED ===\n")
cat("Your system is ready for parallel Bayesian analysis!\n")
cat("Proceed with your main analysis script.\n")
