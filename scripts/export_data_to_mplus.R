# In R: Prepare data for MPlus
library(pacman)
p_load(MplusAutomation)

# Select variables in the order specified in MPlus syntax
analysis_vars <- c(
  # Self-control variables (age 11)
  "sc11_task_completion", "sc11_distracted", "sc11_fidgeting", "sc11_think_act", "sc11_restless",
  "sc11_considerate", "sc11_sharing", "sc11_helpful", "sc11_volunteer_help",
  "sc11_temper", "sc11_obedient", "sc11_lying",
  
  # Self-control variables (age 14) 
  "sc14_task_completion", "sc14_distracted", "sc14_fidgeting", "sc14_think_act", "sc14_restless",
  "sc14_considerate", "sc14_sharing", "sc14_helpful", "sc14_volunteer_help",
  "sc14_temper", "sc14_obedient", "sc14_lying",
  
  # Self-control variables (age 17)
  "sc17_task_completion", "sc17_distracted", "sc17_fidgeting", "sc17_think_act", "sc17_restless",
  "sc17_considerate", "sc17_sharing", "sc17_helpful", "sc17_volunteer_help",
  "sc17_temper", "sc17_obedient", "sc17_lying",
  
  # Fighting variables
  "agr11_fight", "agr14_fight", "agr17_fight"
)

# Create MPlus dataset
mplus_data <- merged_data[, analysis_vars]

# Replace missing values with MPlus missing code
mplus_data[is.na(mplus_data)] <- -999

# Export to tab-delimited file (no variable names)
write.table(mplus_data, "mplus_data_parallel_lgc_aggression.dat", 
           row.names = FALSE, col.names = FALSE, 
           sep = "\t", na = "-999")
