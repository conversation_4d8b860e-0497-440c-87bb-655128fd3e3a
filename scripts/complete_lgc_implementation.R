# =============================================================================
# COMPLETE SECOND-ORDER LATENT GROWTH CURVE IMPLEMENTATION
# Self-Control Scale: Three-Factor Structure with Growth Modeling
# Ages 11, 14, and 17
# 
# To run: First execute measurement_invariance_3factor.R, then run this script
# =============================================================================

library(pacman)
p_load(lavaan, semTools, dplyr, psych, ggplot2, knitr, haven)

cat("=== SECOND-ORDER LATENT GROWTH CURVE MODELING ===\n")
cat("Building on three-factor measurement invariance analysis\n\n")

# =============================================================================
# STEP 1: ENSURE DATA IS LOADED
# =============================================================================

# Check if measurement invariance analysis was already run
if(!exists("wide_data") || !exists("executive_items") || !exists("selfcent_items") || !exists("temper_items")) {
  cat("Loading data and running prerequisite analysis...\n")
  
  # Load data
  data_path <- "/home/<USER>/dissertation_folder/data"
  merged_data <- readRDS(file.path(data_path, "merged1203.rds"))
  
  # Run recoding if needed
  sc11_test_vars <- c("sc11_think_act", "sc11_considerate", "sc11_sharing", "sc11_helpful")
  if(!all(sc11_test_vars %in% names(merged_data))) {
    source("scripts/recode_self_control.R")
    source("scripts/recode_self_control_age14.R") 
    source("scripts/recode_self_control_age17.R")
  }
  
  # Define factor structure (from measurement invariance analysis)
  executive_items <- c("task_completion", "distracted", "fidgeting", "think_act", "restless")
  selfcent_items <- c("considerate", "sharing", "helpful", "volunteer_help")
  temper_items <- c("temper", "obedient", "lying")
  
  core_items <- c(executive_items, selfcent_items, temper_items)
  
  # Create variable names for each wave
  sc11_vars <- paste0("sc11_", core_items)
  sc14_vars <- paste0("sc14_", core_items)
  sc17_vars <- paste0("sc17_", core_items)
  
  # Create analysis dataset
  all_vars <- c(sc11_vars, sc14_vars, sc17_vars)
  available_vars <- all_vars[all_vars %in% names(merged_data)]
  
  wide_data <- merged_data[, available_vars, drop = FALSE]
  if(!"id" %in% names(merged_data)) {
    wide_data$id <- 1:nrow(wide_data)
  } else {
    wide_data$id <- merged_data$id[1:nrow(wide_data)]
  }
  
  # Remove cases with no data
  complete_check <- rowSums(!is.na(wide_data[, available_vars])) > 0
  wide_data <- wide_data[complete_check, ]
  
  cat("Data preparation complete.\n")
}

cat("Sample size for LGC analysis:", nrow(wide_data), "\n")
cat("Factor structure:\n")
cat("- Executive (cool control):", paste(executive_items, collapse = ", "), "\n")
cat("- Self-Centered (prosocial):", paste(selfcent_items, collapse = ", "), "\n") 
cat("- Temper (hot control):", paste(temper_items, collapse = ", "), "\n\n")

# =============================================================================
# STEP 2: SECOND-ORDER LGC MODEL 1 - EXECUTIVE CONTROL
# =============================================================================

cat("=== MODEL 1: EXECUTIVE CONTROL GROWTH ===\n")
cat("Hypothesis: Linear increase from age 11 to 17\n")

exec_lgc <- '
### 1.  First-order factors: fix only the first loading
  Executive11 =~ 1*sc11_task_completion + sc11_distracted + sc11_fidgeting +
                 sc11_think_act + sc11_restless
  Executive14 =~ 1*sc14_task_completion + sc14_distracted + sc14_fidgeting +
                 sc14_think_act + sc14_restless
  Executive17 =~ 1*sc17_task_completion + sc17_distracted + sc17_fidgeting +
                 sc17_think_act + sc17_restless

# Serial residuals (full lattice)
  sc11_task_completion ~~ sc14_task_completion 
  sc14_task_completion ~~ sc17_task_completion
  sc11_distracted      ~~ sc14_distracted      
  sc14_distracted      ~~ sc17_distracted
  sc11_fidgeting       ~~ sc14_fidgeting       
  sc14_fidgeting       ~~ sc17_fidgeting
  sc11_think_act       ~~ sc14_think_act       
  sc14_think_act       ~~ sc17_think_act
  sc11_restless        ~~ sc14_restless        
  sc14_restless        ~~ sc17_restless

  # Growth factors
  iExec =~ 1*Executive11 + 1*Executive14 + 1*Executive17
  sExec =~ 0*Executive11 + 1*Executive14 + 2*Executive17 

  # Marker method: Fix one indicator intercept per latent to 0
  sc11_task_completion ~ 0*1  # Marker for Executive11
  sc14_task_completion ~ 0*1  # Marker for Executive14
  sc17_task_completion ~ 0*1  # Marker for Executive17

  # Latent means
  iExec ~ 1
  sExec ~ 1
'

fit_exec <- sem(exec_lgc, data = wide_data,
                estimator = "MLR",
                parameterization = "delta",
                meanstructure = TRUE)

summary(fit_exec, fit.measures = TRUE, standardized = TRUE)

# =============================================================================
# STEP 3: SECOND-ORDER LGC MODEL 2 - SELF-CENTERED (PROSOCIAL)
# =============================================================================

cat("=== MODEL 2: SELF-CENTERED (PROSOCIAL) GROWTH ===\n")
cat("Hypothesis: Linear increase from age 11 to 17\n")

selfcent_lgc <- '
### 1.  First-order factors: fix only the first loading
  SelfCent11 =~ 1*sc11_considerate + sc11_sharing + sc11_helpful +
                sc11_volunteer_help
  SelfCent14 =~ 1*sc14_considerate + sc14_sharing + sc14_helpful +
                sc14_volunteer_help
  SelfCent17 =~ 1*sc17_considerate + sc17_sharing + sc17_helpful +
                sc17_volunteer_help

# Serial residuals (full lattice)
  sc11_considerate     ~~ sc14_considerate     
  sc14_considerate     ~~ sc17_considerate
  sc11_sharing         ~~ sc14_sharing         
  sc14_sharing         ~~ sc17_sharing
  sc11_helpful         ~~ sc14_helpful         
  sc14_helpful         ~~ sc17_helpful
  sc11_volunteer_help  ~~ sc14_volunteer_help  
  sc14_volunteer_help  ~~ sc17_volunteer_help

  # Marker method: Fix one indicator intercept per latent to 0
  sc11_considerate ~ 0*1  # Marker for SelfCent11
  sc14_considerate ~ 0*1  # Marker for SelfCent14
  sc17_considerate ~ 0*1  # Marker for SelfCent17

  # Growth factors
  iSelfCent =~ 1*SelfCent11 + 1*SelfCent14 + 1*SelfCent17
  sSelfCent =~ 0*SelfCent11 + 1*SelfCent14 + 2*SelfCent17 

  # Latent means
  iSelfCent ~ 1
  sSelfCent ~ 1
'

fit_selfcent <- sem(selfcent_lgc, data = wide_data,
                    estimator = "MLR",
                    parameterization = "delta",
                    meanstructure = TRUE)

summary(fit_selfcent, fit.measures = TRUE, standardized = TRUE)

# =============================================================================
# STEP 4: SECOND-ORDER LGC MODEL 3 - TEMPER (HOT CONTROL)
# =============================================================================

cat("=== MODEL 3: TEMPER (HOT CONTROL) GROWTH ===\n")
cat("Hypothesis: Linear decrease from age 11 to 17\n")

temper_lgc <- '
### 1.  First-order factors: fix only the first loading
  Temper11 =~ 1*sc11_temper + sc11_obedient + sc11_lying
  Temper14 =~ 1*sc14_temper + sc14_obedient + sc14_lying
  Temper17 =~ 1*sc17_temper + sc17_obedient + sc17_lying

# Serial residuals (full lattice)
  sc11_temper   ~~ sc14_temper   
  sc14_temper   ~~ sc17_temper
  sc11_obedient ~~ sc14_obedient 
  sc14_obedient ~~ sc17_obedient
  sc11_lying    ~~ sc14_lying    
  sc14_lying    ~~ sc17_lying

  # Growth factors
  iTemper =~ 1*Temper11 + 1*Temper14 + 1*Temper17
  sTemper =~ 0*Temper11 + 1*Temper14 + 2*Temper17 

  # Marker method: Fix one indicator intercept per latent to 0
  sc11_temper ~ 0*1  # Marker for Temper11
  sc14_temper ~ 0*1  # Marker for Temper14
  sc17_temper ~ 0*1  # Marker for Temper17

  # Latent means
  iTemper ~ 1
  sTemper ~ 1
'

fit_temper <- sem(temper_lgc, data = wide_data,
                  estimator = "MLR",
                  parameterization = "delta",
                  meanstructure = TRUE)

summary(fit_temper, fit.measures = TRUE, standardized = TRUE)
