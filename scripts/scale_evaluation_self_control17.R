library(pacman)
p_load(stats, car, foreign, svMisc, devtools, roxygen2, lattice, psych, lavaan, semTools)

self_control_vars <- c(
  # Variables that were reverse coded (positive behaviors → higher score = more problems)
  "sc17_think_act",        # Think things out before acting
  "sc17_considerate",      # Being considerate of other people's feelings
  "sc17_sharing",          # Sharing readily with other children
  "sc17_helpful",          # Being helpful if someone is hurt
  "sc17_volunteer_help",   # Often volunteering to help others
  "sc17_task_completion",  # Sees tasks through to the end, good attention span
  "sc17_obedient",         # Generally obedient
  
  # Variables that were normal coded (negative behaviors → higher score = more problems)
  "sc17_distracted",       # Is easily distracted, concentration wanders
  "sc17_temper",           # Often has temper tantrums or hot tempers
  "sc17_restless",         # Child is restless, overactive, cannot stay still for long
  "sc17_fidgeting",        # Child is constantly fidgeting or squirming
  "sc17_lying"             # Lying or cheating
)


# Check which recoded variables are available in the dataset
available_vars <- self_control_vars[self_control_vars %in% names(merged_data)]
missing_vars <- self_control_vars[!self_control_vars %in% names(merged_data)]

cat("=== RECODED VARIABLE AVAILABILITY CHECK (AGE 17) ===\n")
cat("Available recoded variables:", length(available_vars), "/", length(self_control_vars), "\n")
cat("Available:", paste(available_vars, collapse = ", "), "\n")
if(length(missing_vars) > 0) {
  cat("Missing:", paste(missing_vars, collapse = ", "), "\n")
  cat("Note: Run recode_self_control_age17.R first to create these variables\n")
}

# Create subset with available self-control variables and complete cases
sc_data <- merged_data[, available_vars, drop = FALSE]
sc_data_complete <- na.omit(sc_data)

cat("\nSample sizes:\n")
cat("Original dataset:", nrow(merged_data), "\n")
cat("With recoded self-control variables:", nrow(sc_data), "\n")
cat("Complete cases:", nrow(sc_data_complete), "\n")

# Check for sufficient sample size
if(nrow(sc_data_complete) < 100) {
  warning("Small sample size (< 100) may affect reliability of factor analysis results")
}

####
## Check dimensionality
####

cat("\n=== DIMENSIONALITY ANALYSIS ===\n")

# Correlation matrix
cor_matrix <- cor(sc_data_complete)
cat("Correlation matrix computed for", ncol(sc_data_complete), "recoded variables\n")

# Check for adequate correlations
mean_cor <- mean(cor_matrix[lower.tri(cor_matrix)])
cat("Mean inter-item correlation:", round(mean_cor, 3), "\n")

if(mean_cor < 0.15) {
  warning("Low mean inter-item correlation (< 0.15) suggests items may not measure same construct")
} else if(mean_cor > 0.50) {
  warning("High mean inter-item correlation (> 0.50) suggests possible redundancy")
}

# Scree plot
check.scree <- fa(cor_matrix, fm = "pa", SMC = TRUE, rotate = "none")

# Create scree plot
scree_plot <- xyplot(check.scree$values ~ 1:ncol(cor_matrix),
                     aspect = 1,
                     type = "b",
                     col = "black",
                     xlab = "Factor",
                     ylab = "Eigenvalue",
                     main = "Scree Plot for Recoded Self-Control Items (Age 17)",
                     pch = 16
)
print(scree_plot)

# Parallel analysis
cat("\nParallel Analysis:\n")
parallel_result <- fa.parallel(cor_matrix, n.obs = nrow(sc_data_complete), fa = "fa")


#################
## EFA
#################

# Initial EFA with 1 factor
cat("\nExploratory Factor Analysis (1 factor):\n")
efa1 <- fa(cor_matrix, fm = "pa", nfactors = 1, SMC = TRUE, 
           residuals = TRUE, rotate = "none")
print(efa1)

# EFA with 2 factors to check for potential subscales
cat("\nExploratory Factor Analysis (2 factors):\n")
efa2 <- fa(cor_matrix, fm = "pa", nfactors = 2, SMC = TRUE, 
           residuals = TRUE, rotate = "varimax")
print(efa2)

# EFA with 3 factors to check for potential subscales
cat("\nExploratory Factor Analysis (3 factors):\n")
efa3 <- fa(cor_matrix, fm = "pa", nfactors = 3, SMC = TRUE, 
           residuals = TRUE, rotate = "varimax")
print(efa3)

# EFA with 4 factors to check for potential subscales
cat("\nExploratory Factor Analysis (4 factors):\n")
efa4 <- fa(cor_matrix, fm = "pa", nfactors = 4, SMC = TRUE, 
           residuals = TRUE, rotate = "varimax")
print(efa4)

# Compute polychoric correlations (appropriate for ordinal data with 3-5 categories)
cat("\nAttempting to compute polychoric correlation matrix for ordinal data...\n")
tryCatch({
  poly_matrix <- polychoric(sc_data_complete)$rho
  cat("Polychoric correlation matrix computed successfully\n")
  
  # EFA with 3 factors using oblique rotation and polychoric correlations
  cat("\nExploratory Factor Analysis (3 factors, oblique rotation, polychoric correlations):\n")
  efa3_poly_oblique <- fa(poly_matrix, fm = "pa", nfactors = 3, SMC = TRUE, 
                          residuals = TRUE, rotate = "oblimin")
  print(efa3_poly_oblique)
  
}, error = function(e) {
  cat("Error computing polychoric correlations:", e$message, "\n")
  cat("Using regular Pearson correlations instead...\n")
  
  # EFA with 3 factors using oblique rotation and Pearson correlations
  cat("\nExploratory Factor Analysis (3 factors, oblique rotation, Pearson correlations):\n")
  efa3_pearson_oblique <- fa(cor_matrix, fm = "pa", nfactors = 3, SMC = TRUE, 
                            residuals = TRUE, rotate = "oblimin")
  print(efa3_pearson_oblique)
})


#################
## CFA
#################

# CFA with 1 factor
cat("\nConfirmatory Factor Analysis (1 factor):\n")
cfa1 <- cfa(model = "f1 =~ sc17_think_act + sc17_considerate + sc17_sharing + sc17_helpful + sc17_volunteer_help + sc17_task_completion + sc17_obedient + sc17_distracted + sc17_temper + sc17_restless + sc17_fidgeting + sc17_lying", 
            data = sc_data_complete,
            estimator = "WLSMV",
            ordered = c("sc17_think_act", "sc17_considerate", "sc17_sharing", "sc17_helpful", "sc17_volunteer_help", "sc17_task_completion", "sc17_obedient", "sc17_distracted", "sc17_temper", "sc17_restless", "sc17_fidgeting", "sc17_lying"),
            std.lv = TRUE)
print(cfa1)
summary(cfa1, fit.measures = TRUE, standardized = TRUE, rsquare = TRUE)

# CFA with 3 factors
cat("\nConfirmatory Factor Analysis (3 factors):\n")
cfa2 <- cfa(model = "Executive =~ sc17_task_completion + sc17_distracted + sc17_fidgeting + sc17_think_act + sc17_restless
                     SelfCent  =~ sc17_considerate + sc17_sharing + sc17_helpful + sc17_volunteer_help
                     Temper    =~ sc17_temper + sc17_obedient + sc17_lying", 
            data = sc_data_complete,
            estimator = "WLSMV",
            ordered = c("sc17_think_act", "sc17_considerate", "sc17_sharing", "sc17_helpful", "sc17_volunteer_help", "sc17_task_completion", "sc17_obedient", "sc17_distracted", "sc17_temper", "sc17_restless", "sc17_fidgeting", "sc17_lying"),
            std.lv = TRUE)
print(cfa2)
summary(cfa2, fit.measures = TRUE, standardized = TRUE, rsquare = TRUE)

# CFA with 3 factors and a second order factor
cat("\nConfirmatory Factor Analysis (3 factors and a second order factor):\n")
cfa3 <- cfa(model = "Executive =~ sc17_task_completion + sc17_distracted + sc17_fidgeting + sc17_think_act + sc17_restless
                     SelfCent  =~ sc17_considerate + sc17_sharing + sc17_helpful + sc17_volunteer_help
                     Temper    =~ sc17_temper + sc17_obedient + sc17_lying
                     LowSC     =~ Executive + SelfCent + Temper",
            data = sc_data_complete,
            estimator = "WLSMV",
            ordered = c("sc17_think_act", "sc17_considerate", "sc17_sharing", "sc17_helpful", "sc17_volunteer_help", "sc17_task_completion", "sc17_obedient", "sc17_distracted", "sc17_temper", "sc17_restless", "sc17_fidgeting", "sc17_lying"),
            std.lv = TRUE)
print(cfa3)
summary(cfa3, fit.measures = TRUE, standardized = TRUE, rsquare = TRUE)

# Bifactor CFA (won't identify)
cat("\nBifactor Confirmatory Factor Analysis:\n")
bifactor_model <- "
  # General factor - all items load on this
  General =~ sc17_task_completion + sc17_distracted + sc17_fidgeting + sc17_think_act + sc17_restless + sc17_considerate + sc17_sharing + sc17_helpful + sc17_volunteer_help + sc17_temper + sc17_obedient + sc17_lying
  
  # Specific factors - orthogonal to general factor
  Executive =~ sc17_task_completion + sc17_distracted + sc17_fidgeting + sc17_think_act + sc17_restless
  SelfCent  =~ sc17_considerate + sc17_sharing + sc17_helpful + sc17_volunteer_help
  Temper    =~ sc17_temper + sc17_obedient + sc17_lying
  
  # Orthogonal constraints
  General ~~ 0*Executive
  General ~~ 0*SelfCent
  General ~~ 0*Temper
  Executive ~~ 0*SelfCent
  Executive ~~ 0*Temper
  SelfCent ~~ 0*Temper
"

cfa_bifactor <- cfa(model = bifactor_model, 
                    data = sc_data_complete,
                    estimator = "WLSMV",
                    ordered = c("sc17_think_act", "sc17_considerate", "sc17_sharing", "sc17_helpful", "sc17_volunteer_help", "sc17_task_completion", "sc17_obedient", "sc17_distracted", "sc17_temper", "sc17_restless", "sc17_fidgeting", "sc17_lying"),
                    std.lv = TRUE)
print(cfa_bifactor)
summary(cfa_bifactor, fit.measures = TRUE, standardized = TRUE, rsquare = TRUE)

# BI-factor CFA (let the specific factors correlate to each other)
cat("\nBifactor Confirmatory Factor Analysis (correlated specific factors):\n")
bifactor_model_s1 <- '
  # General factor - all items load on this
  General =~ sc17_task_completion + sc17_distracted + sc17_fidgeting + sc17_think_act + sc17_restless + sc17_considerate + sc17_sharing + sc17_helpful + sc17_volunteer_help + sc17_temper + sc17_obedient + sc17_lying
  
  # Specific factors - orthogonal to general factor
  Executive =~ sc17_task_completion + sc17_distracted + sc17_fidgeting + sc17_think_act + sc17_restless
  SelfCent  =~ sc17_considerate + sc17_sharing + sc17_helpful + sc17_volunteer_help
  Temper    =~ sc17_temper + sc17_obedient + sc17_lying
  
  # Orthogonal constraints
  General ~~ 0*Executive
  General ~~ 0*SelfCent
  General ~~ 0*Temper
'
cfa_bifactor_s1 <- cfa(model = bifactor_model_s1, 
                    data = sc_data_complete,
                    estimator = "WLSMV",
                    ordered = c("sc17_think_act", "sc17_considerate", "sc17_sharing", "sc17_helpful", "sc17_volunteer_help", "sc17_task_completion", "sc17_obedient", "sc17_distracted", "sc17_temper", "sc17_restless", "sc17_fidgeting", "sc17_lying"),
                    std.lv = TRUE)
print(cfa_bifactor_s1)
summary(cfa_bifactor_s1, fit.measures = TRUE, standardized = TRUE, rsquare = TRUE) 


